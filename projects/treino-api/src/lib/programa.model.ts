import { UsuarioBase } from "./user.model";
import { AtividadeBase } from "./atividade.model";
import { AtividadeMetodoExecucao } from "./metodo-execucao.model";

export interface Programa extends ProgramaBase {
	codigo?: number;
	professor?: UsuarioBase;
	fichas?: Array<FichaPrograma>;
	predefinido?: boolean;
}

export interface ProgramaAcompanhamento {
	fichas?: Array<FichaPrograma>;
	nomeAluno?: string;
	nivelAluno?: string;
	imagemUri?: string;
	urlFoto?: string;
	programa?: Programa;
}

export class Programa {
	constructor(programa: Partial<Programa>) {
		Object.assign(this, programa);
	}

	obterFichaPorId(fichaId): FichaPrograma {
		if (this.fichas) {
			return this.fichas.find((fichaI) => {
				return fichaI.id === fichaId;
			});
		} else {
			return undefined;
		}
	}

	obterAtividadeFichaPorId(fichaId, atividadeFichaId): AtividadeFicha {
		const ficha = this.obterFichaPorId(fichaId);
		if (ficha && ficha.atividades) {
			return ficha.atividades.find((atividadeId) => {
				return atividadeId.id === atividadeFichaId;
			});
		} else {
			return undefined;
		}
	}
}

export interface FichaPrograma {
	id?: string;
	ordem?: number;
	nome: string;
	categoria: CategoriaFicha;
	tipo_execucao: FichaTipoExecucao;
	ultimaExecucao: string;
	mensagem: string;
	dias_semana: Array<DIA_SEMANA>;
	atividades: Array<AtividadeFicha>;
	predefinida: boolean;
	ativo: boolean;
}

export interface AtividadeFicha {
	id: string;
	sequencia: number;
	nomeNaFicha: string;
	atividade: AtividadeBase;
	esforco: number;
	series: Array<SerieAtividade>;
	metodoExecucao: AtividadeMetodoExecucao;
	setAtividades: Array<any>;
	complementoNomeAtividade: string;
}

export enum DIA_SEMANA {
	SEG = "SG",
	TER = "TR",
	QUA = "QA",
	QUI = "QI",
	SEX = "SX",
	SAB = "SB",
	DOM = "DM",
}

export enum FichaTipoExecucao {
	ALTERNADO = "ALTERNADO",
	DIAS_SEMANA = "DIAS_SEMANA",
}

export interface ProgramaBase {
	id?: string;
	nome?: string;
	chaveOrigem?: string;
	alunoId?: string;
	professorMontou?: ProfessorSimples;
	dataLancamento?: Date;
	inicio?: string;
	termino?: string;
	totalTreinos?: number;
	qtdDiasSemana?: number;
	revisao?: string;
	treinosConcluidos?: number;
	colaboradorId?: string;
	situacaoUltimoTreinoAluno?: boolean;
	geradoPorIA?: boolean;
	emRevisaoProfessor?: boolean;
}

export class ProgramaAtual {
	constructor(atual: Partial<ProgramaAtual>) {
		Object.assign(this, atual);
	}

	id?: number;
	nome?: string;
	fichas?: number;
	previstas?: number;
	realizadas?: number;
	percentual?: number;
}

export class FichaDoDia {
	constructor(atual: Partial<FichaDoDia>) {
		Object.assign(this, atual);
	}

	id?: number;
	nome?: string;
	vezes?: number;
}

export class FichasRelacionadas {
	constructor(atual: Partial<FichasRelacionadas>) {
		Object.assign(this, atual);
	}

	idUltima?: number;
	nomeUltima?: string;
	idProxima?: number;
	nomeProxima?: string;
}

export class DiasQueTreinouProgramaAtual {
	constructor(atual: Partial<DiasQueTreinouProgramaAtual>) {
		Object.assign(this, atual);
	}

	treinosExecutadosPeriodo: number;
	domingo: number;
	segunda: number;
	terca: number;
	quarta: number;
	quinta: number;
	sexta: number;
	sabado: number;
}

export class HorariosQueTreinouProgramaAtual {
	constructor(atual: Partial<HorariosQueTreinouProgramaAtual>) {
		Object.assign(this, atual);
	}

	manha: number;
	tarde: number;
	noite: number;
}

export interface SerieAtividade {
	id?: string;
	atividadeFichaId?: string;
	sequencia?: number;
	repeticoes?: string;
	repeticaoComp?: number;
	carga?: number;
	cargaComp?: number;
	cadencia?: string;
	descanso?: number; // segundos
	velocidade?: string;
	duracao?: number; // segundos
	distancia?: string;
	complemento?: string;
	serieRealizada?: boolean;
}

export interface CategoriaFicha {
	id: string;
	nome: string;
}

export interface ProfessorSimples {
	id?: number;
	codigoColaborador?: number;
	nome?: string;
	imageUri?: string;
	cref?: string;
}

export interface ProgramaConflitante {
	idPrograma: number;
	nomePrograma: string;
	dataInicio: number;
	dataFim: number;
}

export interface ValidacaoConflitoProgramas {
	dataFim: number;
	conflitoInicio?: ProgramaConflitante;
	conflitoFim?: ProgramaConflitante;
}

export interface ProgramaCriar {
	alunoId?: number;
	colaboradorId?: number;
	nome?: string;
	professorId?: string;
	inicio?: string;
	termino?: string;
	totalTreinos?: string;
	qtdDiasSemana?: string;
	revisao?: string;
	treinosConcluidos?: string;
	genero?: string;
}

export interface ParamsCalcularAulasPrevista {
	campoAlterado: string;
	value: string;
	inicio: number;
	termino: number;
	totalTreinos: number;
	qtdDiasSemana: number;
}

export interface ProgramaPredefinido {
	id?: number;
	categoria?: string;
	professor?: ProfessorSimples;
	nomePrograma?: string;
	diasPorSemana?: number;
	totalAulasPrevistas?: number;
	genero?: string;
	nomeProfessor?: string;
	codigoProfessor?: number;
}

export interface ProgramaIA {
	age?: Number;
	height?: Number;
	weight?: Number;
	body_type?: string;
	goal?: string;
	training_days?: Number;
	training_time?: Number;
	experience_level?: string;
	current_condition?: string;
	client_id?: Number;
}
