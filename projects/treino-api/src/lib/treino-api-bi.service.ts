import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";

import { ApiResponseSingle } from "./base.model";
import { BiAgenda, BiApp } from "./bi.model";
import { ProgramaAcompanhamento } from "./programa.model";
import { TreinoApiBaseService } from "./treino-api-base.service";
import { TreinoApiModule } from "./treino-api.module";

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiBiService {
	private constructor(private apiBase: TreinoApiBaseService) {}

	public gerarBI(professorId: number, codigoPessoa: number): Observable<any> {
		const url = "treino-bi/atualizar";
		const params: any = {};
		params["idProfessor"] = professorId;
		params["codigoPessoa"] = codigoPessoa;
		return this.apiBase.get(url, { params }).pipe(
			map((response: any) => {
				return response;
			})
		);
	}

	public obterDashboard(
		professorId: number,
		pessoaId: number
	): Observable<any> {
		const url = "treino-bi/dash";
		const params: any = {};
		params["idProfessor"] = professorId;
		params["idPessoa"] = pessoaId;
		return this.apiBase.get(url, { params }).pipe(
			map((response: any) => {
				return response;
			})
		);
	}

	public biApp(reload): Observable<BiApp> {
		const url = `bi-app${reload}`;
		return this.apiBase.get(url, {}).pipe(
			map((response: ApiResponseSingle<BiApp>) => {
				return response.content;
			})
		);
	}

	public biAulas(params): Observable<BiAgenda> {
		const url = `bi-agenda`;
		return this.apiBase.get(url, { params }).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}

	iniciarAcompanhamento(id, professor): Observable<boolean> {
		return this.apiBase
			.put(`treino-bi/alunos-sem-acompanhamento/${id}`, professor)
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return true;
				})
			);
	}

	finalizarAcompanhamento(
		id,
		professor,
		programaId: number,
		fichaId: number
	): Observable<boolean> {
		return this.apiBase
			.put(
				`treino-bi/alunos-com-acompanhamento/${id}/${programaId}/${fichaId}`,
				professor
			)
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return true;
				})
			);
	}

	concluirAcompanhamentoAtividade(
		codigoAtividadeFicha,
		idProfessor
	): Observable<boolean> {
		return this.apiBase
			.put(
				`treino-bi/concluir-acompanhamento-atividade/${codigoAtividadeFicha}`,
				idProfessor
			)
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return true;
				})
			);
	}

	obterAlunoAcompanhamento(id): Observable<ProgramaAcompanhamento> {
		return this.apiBase.get(`programas/aluno-acompanhamento/${id}`).pipe(
			map((response: ApiResponseSingle<ProgramaAcompanhamento>) => {
				return response.content;
			})
		);
	}

	alterarSerie(id, serie): Observable<any> {
		return this.apiBase.put(`series/${id}`, serie).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}

	alterarSerieRealizada(id): Observable<any> {
		return this.apiBase.put(`series/${id}/serieRealizada`, null).pipe(
			map((response: ApiResponseSingle<any>) => {
				return response.content;
			})
		);
	}

	verificarPendencia(
		idProfessor: number,
		codigoPessoa: number
	): Observable<any> {
		const url = "treino-bi/verificar-pendencias";
		const params: any = {};
		params["idProfessor"] = idProfessor;
		params["codigoPessoa"] = codigoPessoa;
		return this.apiBase.get(url, { params }).pipe(
			map((response: any) => {
				return response;
			})
		);
	}

	public obterListAlunosExecucaoTreinoUltimosDias(
		empresaId: number,
		dia: number,
		periodo
	): Observable<any> {
		const url = `treino-bi/lista-alunos-execucao-treino-ultimos-dias/${empresaId}/${dia}/${periodo}`;
		return this.apiBase.get(url).pipe(
			map((response: any) => {
				return response;
			})
		);
	}

	public obterAvaliacaoDoProfessor(
		professorId: number,
		pessoaId: number
	): Observable<any> {
		const url = "treino-bi/dash_avaliacao_do_professor";
		const params: any = {};
		params["idProfessor"] = professorId;
		params["idPessoa"] = pessoaId;
		return this.apiBase.get(url, { params }).pipe(
			map((response: any) => {
				return response;
			})
		);
	}
}
