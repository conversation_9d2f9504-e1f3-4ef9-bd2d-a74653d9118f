/**
 * Constantes de criptografia para diferentes módulos do sistema
 */
export class CryptoConstants {
  /**
   * Chaves de criptografia específicas para cada módulo
   */
  static readonly CRYPTO_KEYS = {
    /**
     * Chave de criptografia específica para PIX Automático
     * Deve ser a mesma usada no backend Java
     */
    PIX_AUTOMATICO: 'pIxAut0CrYpt',

    /**
     * Chave padrão do sistema (já existente)
     */
    SISTEMA_PADRAO: 'SisTeP4ctoZwUi25',
  };

  /**
   * Configurações de algoritmos de criptografia
   */
  static readonly ALGORITHMS = {
    /**
     * Algoritmo AES (equivalente ao AlgoritmoCriptoEnum.ALGORITMO_AES do Java)
     */
    AES: {
      name: 'AES',
      mode: 'CBC',
      ivSize: 16, // 16 bytes para AES
    },
  };
}
