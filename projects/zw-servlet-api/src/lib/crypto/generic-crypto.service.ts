import { Injectable } from '@angular/core';
import * as CryptoJS from 'crypto-js';
import { CryptoConstants } from './crypto.constants';

/**
 * Serviço de criptografia genérico
 * Implementa os mesmos algoritmos usados no backend Java
 * Pode ser usado por diferentes módulos passando a chave específica
 */
@Injectable({
  providedIn: 'root',
})
export class GenericCryptoService {
  /**
   * Criptografa um texto usando AES-CBC com IV de zeros
   * Equivalente ao método encrypt do Java
   * @param text Texto a ser criptografado
   * @param chave Chave de criptografia a ser usada
   * @param usarWorkaround Sempre true conforme especificado
   * @returns Texto criptografado em base64 com workarounds aplicados
   */
  encrypt(text: string, chave: string, usarWorkaround: boolean): string {
    try {
      // Cria a chave secreta (equivalente ao getSecretKey do Java)
      const key = this.getSecretKey(chave);

      // Cria IV de zeros (equivalente ao new byte[algoritmo.getTamanho()] do Java)
      // Cria um array de 16 bytes (128 bits) todos com valor 0
      const ivBytes = new Array(16).fill(0);
      const iv = CryptoJS.lib.WordArray.create(ivBytes, 16);

      // Criptografa usando AES-CBC
      const encrypted = CryptoJS.AES.encrypt(text, key, {
        iv: iv, // IV de zeros (16 bytes)
        mode: CryptoJS.mode.CBC, // AES/CBC
        padding: CryptoJS.pad.Pkcs7, // PKCS7Padding (equivalente ao PKCS5 do Java para AES)
      });

      // Converte para base64 (equivalente ao enc.encode do Java)
      let codigoCripto = encrypted.toString();

      // Aplica workarounds para contornar problemas de código criptografado em URLs
      if (usarWorkaround) {
        codigoCripto = codigoCripto.replace(/\+/g, '*');
      }
      codigoCripto = codigoCripto.replace(/\//g, '@');

      return codigoCripto;
    } catch (error) {
      throw new Error('Falha na criptografia: ' + error.message);
    }
  }

  /**
   * Descriptografa um texto usando AES-CBC com IV de zeros
   * Equivalente ao método decrypt do Java
   * @param text Texto criptografado
   * @param chave Chave de criptografia a ser usada
   * @param usarWorkaround Sempre true conforme especificado
   * @returns Texto descriptografado
   */
  decrypt(text: string, chave: string, usarWorkaround: boolean = true): string {
    try {
      // Cria a chave secreta
      const key = this.getSecretKey(chave);

      // Cria IV de zeros
      const ivBytes = new Array(16).fill(0);
      const iv = CryptoJS.lib.WordArray.create(ivBytes, 16);

      // Reverte os workarounds
      let textToDecrypt = text;
      if (usarWorkaround) {
        textToDecrypt = textToDecrypt.replace(/\*/g, '+');
      }
      textToDecrypt = textToDecrypt.replace(/@/g, '/');

      // Descriptografa usando AES-CBC
      const decrypted = CryptoJS.AES.decrypt(textToDecrypt, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
      });

      // Converte para string UTF-8
      const result = decrypted.toString(CryptoJS.enc.Utf8);

      if (!result) {
        throw new Error('Falha na descriptografia - resultado vazio');
      }

      return result;
    } catch (error) {
      throw error; // Lança a exceção em vez de retornar string
    }
  }

  /**
   * Cria a chave secreta a partir da string de chave
   * Equivalente ao método getSecretKey do Java
   * @param chave Chave em string
   * @returns Chave secreta para uso no CryptoJS
   */
  private getSecretKey(chave: string): CryptoJS.lib.WordArray {
    // Replica exatamente a lógica do Java:
    // byte[] keyB = new byte[tam];
    // for (int i = 0; i < keyString.length() && i < keyB.length; i++) {
    //     keyB[i] = (byte) keyString.charAt(i);
    // }

    const tam = 16; // Tamanho para AES (128 bits)
    const keyB = new Array(tam).fill(0); // Inicializa com zeros

    // Preenche o array com os bytes da chave (usando charCodeAt para replicar charAt do Java)
    for (let i = 0; i < chave.length && i < keyB.length; i++) {
      keyB[i] = chave.charCodeAt(i) & 0xff; // Garante que seja um byte (0-255)
    }

    // Converte o array de bytes para o formato que CryptoJS espera (words de 32 bits)
    const words = [];
    for (let i = 0; i < keyB.length; i += 4) {
      const word = (keyB[i] << 24) | (keyB[i + 1] << 16) | (keyB[i + 2] << 8) | keyB[i + 3];
      words.push(word);
    }

    const wordArray = CryptoJS.lib.WordArray.create(words, tam);

    return wordArray;
  }
}
