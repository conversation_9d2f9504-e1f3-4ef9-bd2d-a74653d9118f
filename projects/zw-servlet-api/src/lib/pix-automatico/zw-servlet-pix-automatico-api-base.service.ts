import { HttpClient } from '@angular/common/http';
import { Injectable, Inject, InjectionToken } from '@angular/core';
import { Observable } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { ZwServletApiConfig, ZwServletApiConfigProviderBase } from '../zw-servlet-api-config-provider-base.service';

export const ZwServletPixAutomaticoApiConfigProviderBase = new InjectionToken<ZwServletApiConfigProviderBase>(
  'ZwServletPixAutomaticoApiConfigProviderBase',
);

export interface RequestOptions {
  headers?: any;
  params?: any;
}

@Injectable({
  providedIn: 'root',
})
export class ZwServletPixAutomaticoApiBaseService {
  constructor(
    private readonly httpClient: HttpClient,
    @Inject(ZwServletPixAutomaticoApiConfigProviderBase)
    private readonly apiConfigProvider: ZwServletApiConfigProviderBase,
  ) {
    if (!this.apiConfigProvider) {
      throw Error('Não foi fornecida uma implementação para ZwServletPixAutomaticoApiConfigProviderBase');
    }
  }

  private mergeOptions(options: RequestOptions, apiConfig: ZwServletApiConfig): RequestOptions {
    const headers = options.headers ? options.headers : {};
    const params = options.params ? options.params : {};

    const mergedParams = {
      ...params,
      key: apiConfig.key,
      origin: apiConfig.origin,
      empresa: apiConfig.empresa,
      username: apiConfig.username,
    };

    options.headers = headers;
    options.params = mergedParams;
    return options;
  }

  public get<T>(url: string, options: RequestOptions = {}): Observable<T> {
    return this.apiConfigProvider.getApiConfig().pipe(
      mergeMap((apiConfig) => {
        const fullUrl = `${apiConfig.baseUrl}/pixAutomatico${url ? '/' + url : ''}`;
        const mergedOptions = this.mergeOptions(options, apiConfig);
        return this.httpClient.get<T>(fullUrl, mergedOptions);
      }),
    );
  }

  public delete<T>(url: string, options: RequestOptions = {}): Observable<T> {
    return this.apiConfigProvider.getApiConfig().pipe(
      mergeMap((apiConfig) => {
        const fullUrl = `${apiConfig.baseUrl}/pixAutomatico${url ? '/' + url : ''}`;
        const mergedOptions = this.mergeOptions(options, apiConfig);
        return this.httpClient.delete<T>(fullUrl, mergedOptions);
      }),
    );
  }

  public put<T>(url: string, body: any, options: RequestOptions = {}): Observable<T> {
    return this.apiConfigProvider.getApiConfig().pipe(
      mergeMap((apiConfig) => {
        const fullUrl = `${apiConfig.baseUrl}/pixAutomatico${url ? '/' + url : ''}`;
        const mergedOptions = this.mergeOptions(options, apiConfig);
        return this.httpClient.put<T>(fullUrl, body, mergedOptions);
      }),
    );
  }

  public post<T>(url: string, body: any, options: RequestOptions = {}): Observable<T> {
    return this.apiConfigProvider.getApiConfig().pipe(
      mergeMap((apiConfig) => {
        const fullUrl = `${apiConfig.baseUrl}/pixAutomatico${url ? '/' + url : ''}`;
        const mergedOptions = this.mergeOptions(options, apiConfig);
        return this.httpClient.post<T>(fullUrl, body, mergedOptions);
      }),
    );
  }
}
