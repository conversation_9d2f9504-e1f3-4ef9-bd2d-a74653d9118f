import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ApiResponseSingle } from '../base.model';
import { ZwServletPixAutomaticoApiBaseService } from './zw-servlet-pix-automatico-api-base.service';
import { GenericCryptoService } from '../crypto/generic-crypto.service';
import { CryptoConstants } from '../crypto/crypto.constants';

@Injectable({
  providedIn: 'root',
})
export class ZWServletPixAutomaticoApiService {
  constructor(
    private readonly restService: ZwServletPixAutomaticoApiBaseService,
    private readonly cryptoService: GenericCryptoService,
  ) {}

  public enviarLinkAutorizacaoViaEmail(payload: any): Observable<ApiResponseSingle<any>> {
    const params: any = {
      op: 'enviarLinkAutorizacaoViaEmail',
    };
    return this.restService.post('', payload, { params }).pipe(
      map((response: any) => {
        return response;
      }),
    );
  }

  public solicitarAutorizacaoPixAutomatico(dados: any): Observable<any> {
    const params = { op: 'solicitarAutorizacaoPixAutomatico' };
    return this.restService.post('', JSON.stringify(dados), { params }).pipe(
      map((response: any) => {
        return response.content;
      }),
      catchError((error) => {
        return of(error.error.meta);
      }),
    );
  }

  public obterInstituicoesDisponiveisPixAutomatico(dados: any): Observable<any> {
    const params = { op: 'obterInstituicoesDisponiveisPixAutomatico' };
    return this.restService.post('', JSON.stringify(dados), { params }).pipe(
      map((response: any) => {
        return response.content;
      }),
      catchError((error) => {
        return of(error.error.meta);
      }),
    );
  }

  public cancelarAutorizacaoPixAutomatico(dados: any): Observable<any> {
    const params = { op: 'cancelarAutorizacaoPixAutomatico' };
    return this.restService.post('', JSON.stringify(dados), { params }).pipe(
      map((response: any) => {
        return response.content;
      }),
      catchError((error) => {
        return of(error.error.meta);
      }),
    );
  }

  public consultarStatusPixAutomatico(codigoPixAutomatico: string): Observable<any> {
    const params = { op: 'consultarStatusPixAutomatico' };
    const payload = { codigoPixAutomatico };

    // Criptografa o payload antes de enviar
    const payloadCriptografado = this.cryptoService.encrypt(
      JSON.stringify(payload),
      CryptoConstants.CRYPTO_KEYS.PIX_AUTOMATICO,
      true,
    );

    return this.restService.post('', payloadCriptografado, { params }).pipe(
      map((response: any) => {
        // Descriptografa a resposta do backend
        try {
          const responseDecriptografada = this.cryptoService.decrypt(
            response.content,
            CryptoConstants.CRYPTO_KEYS.PIX_AUTOMATICO,
            true,
          );

          // Backend retorna: {"statusBelvo":"AWAITING_AUTHORIZATION"}
          // Retorna o objeto descriptografado que contém o statusBelvo
          return JSON.parse(responseDecriptografada);
        } catch (error) {
          return response.content; // Retorna original se falhar
        }
      }),
      catchError((error) => {
        return of(null);
      }),
    );
  }
}
