@import "../../../../assets/ui-kit.scss";
@import "projects/ui/assets/ds3/colors.var";

.ds3-bar-line {
	min-width: 364px;

	.superior {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-direction: row;

		.nome {
			.nome-texto {
				@extend .typography-title-5;
				color: var(--color-typography-default-text);
			}
		}

		.info {
			.info-texto {
				@extend .typography-display-6;
				color: var(--color-typography-default-title);
			}

			.info-text-clickable {
				cursor: pointer;
				color: $actionDefaultAble04;
			}

			.info-text-clickable {
				cursor: pointer;
				color: $actionDefaultAble04;
			}
		}
	}

	.inferior {
		display: flex;
		flex-direction: row;
		align-items: baseline;
		justify-content: space-between;

		.etapas {
			display: flex;
			position: relative;
			justify-content: flex-start;
			flex-direction: row;
			align-items: center;
			width: 100%;
			gap: 4px;

			.etapa {
				width: 100%;
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				justify-content: center;

				.barra {
					background-color: var(--color-support-gray-2);
					width: 100%;
					height: 12px;

					&.ehInicial {
						border-top-left-radius: 8px;
						border-bottom-left-radius: 8px;
					}

					&.ehFinal {
						border-top-right-radius: 8px;
						border-bottom-right-radius: 8px;
					}
				}

				small {
					@extend .typography-overline-2;
					color: var(--color-typography-default-text);
				}
			}
		}

		.porcentagem {
			display: flex;
			align-items: center;
			padding-left: 4px;

			span {
				@extend .typography-title-5;
				font-size: 18px;
				color: var(--color-typography-default-title);
			}

			small {
				@extend .typography-title-5;
				font-size: 12px;
				color: var(--color-typography-default-title);
			}
		}
	}

	.descricao {
		display: flex;
		justify-content: flex-start;
		align-content: flex-start;
		flex-direction: row;
		align-items: flex-start;

		.descricao-texto {
			@extend .typography-overline-2;
			color: var(--color-typography-default-text);
		}
	}

	.marcador {
		position: absolute;
		top: -25px;
		display: flex;
		min-height: 13px;
		justify-content: flex-end;

		.marcador-texto {
			@extend .typography-overline-2;
			color: var(--color-typography-default-title);
			min-height: 30px;
			min-width: 25px;
			display: flex;
			flex-direction: row;
			justify-content: space-evenly;
			align-items: flex-start;
			justify-content: space-between;
		}

		&::before,
		&::after {
			content: "";
			width: 0;
			height: 0;
			position: absolute;
		}

		&::before {
			border-left: 10px solid transparent;
			border-right: 10px solid transparent;
			border-top: 10px solid var(--color-support-gray-4);
			border-radius: 5px 4px 0px 0px;
			top: 14px;
			right: 3px;
		}

		&::after {
			border-left: 8px solid transparent;
			border-right: 8px solid transparent;
			border-top: 8px solid var(--color-support-gray-0);
			border-radius: 2px 0px 0px 2px;
			top: 15px;
			right: 5px;
		}
	}
}
