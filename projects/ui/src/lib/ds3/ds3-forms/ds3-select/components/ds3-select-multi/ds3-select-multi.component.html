<div
	#ds3Select
	(click)="showDropdown()"
	[class.ds3-disabled]="disabled"
	class="ds3-select">
	<div class="ds3-select-body">
		<p class="ds3-select-value pct-body1">
			<ng-container *ngIf="selectedValues.length > 0 && isChipsListOverflowing">
				<span class="ds3-select-value-length">
					{{ selectedValues.length }} itens selecionados
				</span>
			</ng-container>
			<span
				*ngIf="
					selectedValues.length > 0 && !isChipsListOverflowing;
					else ds3SelectPlaceholder
				">
				<ds3-chips-list>
					<ng-container
						*ngFor="let value of selectedValues; trackBy: trackByValue">
						<ds3-chips
							(removed)="discardValue(value, value); $event.stopPropagation()"
							[isDisabled]="disabled"
							[isRemovable]="true">
							<ng-container
								*ngTemplateOutlet="
									templateOrText;
									context: {
										item:
											value
											| findOptionNameBasedOnValue
												: options
												: valueKey
												: nameKey
												: useValueAsObject
									}
								"></ng-container>
						</ds3-chips>
					</ng-container>
				</ds3-chips-list>
			</span>
			<span #ds3SelectHiddenChipsList class="ds3-select-hidden-chipslist">
				<ds3-chips-list>
					<ng-container
						*ngFor="let value of selectedValues; trackBy: trackByValue">
						<ds3-chips
							(removed)="discardValue(value, value); $event.stopPropagation()"
							[isDisabled]="disabled"
							[isRemovable]="true">
							<ng-container
								*ngTemplateOutlet="
									templateOrText;
									context: {
										item:
											value
											| findOptionNameBasedOnValue
												: options
												: valueKey
												: nameKey
												: useValueAsObject
									}
								"></ng-container>
						</ds3-chips>
					</ng-container>
				</ds3-chips-list>
			</span>
			<ng-template #ds3SelectPlaceholder>
				<span *ngIf="selectedValues.length == 0">
					{{ placeholder }}
				</span>
			</ng-template>
		</p>
	</div>

	<ng-template #cdkPortal cdk-portal>
		<div class="ds3-select-options ds3-select-multiple-options">
			<div class="ds3-select-multi-search-section">
				<input
					(click)="$event.stopPropagation()"
					(keyup)="onKeyEvent($event)"
					[formControl]="searchControl"
					class="ds3-select-multiple-search pct-body2"
					placeholder="Filtrar por:"
					cdkTrapFocus
					type="text" />
				<div
					class="ds3-select-refresh"
					ds3Tooltip="Atualizar as opções"
					*ngIf="canReload"
					(click)="$event.stopPropagation()">
					<button
						ds3-icon-button
						[id]="id + '-refresh-option'"
						(click)="
							onSearch(searchControl.value, null, true, true);
							$event.stopPropagation()
						">
						<i class="pct pct-refresh-cw"></i>
					</button>
				</div>
			</div>
			<ng-container *ngIf="!searchControl.value; else multipleSearchResults">
				<ds3-diviser *ngIf="selectedValues.length"></ds3-diviser>
				<div
					(click)="$event.stopPropagation(); toggleSelectedAccordion()"
					*ngIf="selectedValues.length"
					[class.ds3-select-multiple-title-disabled]="!selectedValues.length"
					class="ds3-select-multiple-title">
					<i *ngIf="isSelectedOpen" class="pct pct-chevron-down"></i>
					<i *ngIf="!isSelectedOpen" class="pct pct-chevron-right"></i>
					<h2>
						<i class="pct pct-plus-square"></i>
						Selecionados
					</h2>
				</div>
				<div *ngIf="isSelectedOpen" class="ds3-select-multiple-list">
					<ng-container
						*ngFor="
							let value of selectedValues;
							let i = index;
							trackBy: trackByValue
						">
						<ds3-option
							(click)="discardValue(value, value); $event.stopPropagation()"
							[value]="value"
							[class.is-highlighted]="highlightedIndex === i">
							<i class="pct pct-check-square"></i>
							<ng-container
								*ngTemplateOutlet="
									templateOrText;
									context: {
										item:
											value
											| findOptionNameBasedOnValue
												: options
												: valueKey
												: nameKey
												: useValueAsObject
									}
								"></ng-container>
						</ds3-option>
					</ng-container>
				</div>
				<ds3-diviser *ngIf="notSelectedValues.length"></ds3-diviser>
				<div
					(click)="$event.stopPropagation(); toggleNotSelectedAccordion()"
					*ngIf="notSelectedValues.length"
					[class.ds3-select-multiple-title-disabled]="!notSelectedValues.length"
					class="ds3-select-multiple-title">
					<i *ngIf="isNotSelectedOpen" class="pct pct-chevron-down"></i>
					<i *ngIf="!isNotSelectedOpen" class="pct pct-chevron-right"></i>
					<h2>
						<i class="pct pct-minus-square"></i>
						Não Selecionados
					</h2>
				</div>
				<div *ngIf="isNotSelectedOpen" class="ds3-select-multiple-list">
					<ng-container
						*ngFor="
							let option of notSelectedValues;
							let i = index;
							trackBy: trackByOption
						">
						<ds3-option
							[class.is-highlighted]="
								highlightedIndex === selectedValues.length + i
							"
							(click)="
								selectValue(option[valueKey], option); $event.stopPropagation()
							"
							[value]="option[valueKey]">
							<i class="pct pct-square"></i>
							<ng-container
								*ngTemplateOutlet="
									templateOrText;
									context: { item: option[nameKey] }
								"></ng-container>
						</ds3-option>
					</ng-container>
				</div>
			</ng-container>
			<ng-template #multipleSearchResults>
				<div *ngIf="searchedOptions.length" class="ds3-select-multiple-list">
					<ng-container
						*ngFor="
							let option of searchedOptions;
							let i = index;
							trackBy: trackByOption
						">
						<ds3-option
							[class.is-highlighted]="highlightedIndex === i"
							(click)="
								toggleSelectValue(option[valueKey], option);
								$event.stopPropagation()
							"
							[value]="option[valueKey]">
							<i
								*ngIf="!isValueSelected(option[valueKey], option)"
								class="pct pct-square"></i>
							<i
								*ngIf="isValueSelected(option[valueKey], option)"
								class="pct pct-check-square"></i>
							<ng-container
								*ngTemplateOutlet="
									templateOrText;
									context: { item: option[nameKey] }
								"></ng-container>
						</ds3-option>
					</ng-container>
				</div>
				<div *ngIf="!searchedOptions.length">
					<p>Nenhuma opção encontrada.</p>
				</div>
			</ng-template>
		</div>
	</ng-template>

	<ds3-select-arrow
		[triggerFocus]="shouldFocusArrow"
		(click)="$event.stopPropagation(); toggleDropdown()"
		[isOpen]="isOpen"></ds3-select-arrow>
</div>

<ng-template #templateOrText let-item="item">
	<ng-container *ngIf="isTemplate(item)">
		<ng-container *ngTemplateOutlet="item"></ng-container>
	</ng-container>
	<ng-container *ngIf="!isTemplate(item)">
		{{ item }}
	</ng-container>
</ng-template>
