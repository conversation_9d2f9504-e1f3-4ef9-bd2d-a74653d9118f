<header>
	<span class="titulo">Cancelar parcelas</span>
	<i
		id="cancelar-parcelas-btn-fechar"
		(click)="close('x')"
		class="pct pct-x cursor-pointer"></i>
</header>

<body>
	<div matDialogContent>
		<pacto-relatorio
			#relatorio
			[table]="table"
			id="relatorio"
			[showShare]="false"
			class="inteiro"
			tableTitle="Parcelas selecionadas"></pacto-relatorio>
		<ds3-form-field class="inteiro">
			<ds3-field-label>Justificativa*</ds3-field-label>
			<textarea
				ds3Input
				id="cancelar-parcelas-justificativa"
				[formControl]="form.get('justificativa')"
				placeholder="Insira aqui a justificativa de cancelamento"
				rows="4"></textarea>
		</ds3-form-field>
	</div>

	<div matDialogActions>
		<button
			ds3-flat-button
			id="cancelar-parcelas-btn-confirmar"
			(click)="confirmaCancelamento()"
			[disabled]="isDisabledCancelar">
			Confirmar cancelamento das parcelas
		</button>
	</div>

	<ng-template #thCodigo>
		<ds3-checkbox
			[square]="true"
			(click)="checkAllRows($event)"
			[checked]="
				quantidadeDeItensSelecionados == parcelas.length
			"></ds3-checkbox>
		<span>Código</span>
	</ng-template>
	<ng-template #celulaCodigo let-item="item" let-index="index">
		<ds3-checkbox
			[square]="true"
			id="cancelar-parcelas-cod-{{ index }}"
			(click)="checkRow($event, item)"
			[checked]="!!checks[item.codigo]"></ds3-checkbox>
		<span>{{ item.codigo }}</span>
	</ng-template>

	<ng-template #celulaSituacao let-data="item" let-index="index">
		<ds3-status [color]="data.situacao.statusCor">
			{{ data.situacao.statusText | captalize }}
		</ds3-status>
	</ng-template>
	<ng-template #celulaMultaOuJuros let-data="item" let-index="index">
		<span>{{ data?.valorMulta + data?.valorJuros | currency : "BRL" }}</span>
	</ng-template>

	<body></body>
</body>
