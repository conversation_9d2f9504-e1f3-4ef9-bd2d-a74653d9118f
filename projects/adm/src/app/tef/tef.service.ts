import { TefComponent } from "@adm/tef/tef.component";
import { DatePipe } from "@angular/common";
import {
	HttpClient,
	HttpErrorResponse,
	HttpParams,
} from "@angular/common/http";
import { Injectable } from "@angular/core";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { SessionService } from "@base-core/client/session.service";
import { throwError } from "rxjs";

interface DadosTransacaoFiserv {
	funcao: number;
	ipsitef?: string;
	datafiscal?: string;
	horafiscal?: string;
	cupomfiscal?: string;
	empresa?: string;
	terminal?: string;
	valor?: string;
	operador?: string;
	nrParcelasCartaoCredito?: number;
	trn_additional_parameters?: string;
	trn_init_parameters?: string;
}

interface TransacaoFiservResponse {
	content: Array<{
		ipsitef: string;
		empresa: string;
	}>;
}

@Injectable({ providedIn: "root" })
export class TefService {
	private agentUrl = "https://127.0.0.1/agente/clisitef"; // URL do agente
	private sessao: {
		sessionId: string;
		ret: any[];
		continua: number;
		cupomFiscal: string;
		dataFiscal: string;
		horaFiscal: string;
	};
	private verificacaoTransacoesPendentesEmAndamento = false;

	constructor(
		private http: HttpClient,
		private sessionService: SessionService,
		private datePipe: DatePipe
	) {}

	processarTransacaoVerificacao(
		dialog: MatDialog,
		component: typeof TefComponent,
		config: any
	): void {
		const params = new HttpParams()
			.append("chave", this.sessionService.chave)
			.append("usuario", this.sessionService.codUsuarioZW)
			.append("empresa", this.sessionService.empresaId)
			.append("op", "obterDadosTransacaoFiserv");
		const url = `${
			this.sessionService.pathUrlZw
		}/insec/adm?${params.toString()}`;

		this.http.get<TransacaoFiservResponse>(url).subscribe(
			(dadosTransacaoFiservResponse) => {
				const listaDadosTransacaoFiservResponse =
					dadosTransacaoFiservResponse.content;
				if (listaDadosTransacaoFiservResponse.length < 1) {
					return; // Retorna sem abrir o modal
				}

				const dialogRef = dialog.open(component, config);
				const modal = dialogRef.componentInstance;
				modal.showStatus("Iniciando verificação de pagamentos pendentes...");

				for (const item of listaDadosTransacaoFiservResponse) {
					// No Angular, só verifica, por isso está setando direto como true
					// No ZW, tem validações para passar quando é pagamento e quando é verificação.
					// Se for fazer pagamento aproveitando esse service, tem de ajustar aqui para receber o valor do front, igual ao ZW
					// No ZW, pode ver essa lógica no arquivo include_pinpad_fiserv.jsp, no metodo verificarPendente(dadosTransacao, verificarTransacoesPendentes)
					this.verificacaoTransacoesPendentesEmAndamento = true;

					const dadosTransacao = this.montarDadosTransacaoFiserv(item);

					this.sessao = {
						sessionId: "",
						ret: [],
						continua: 0,
						cupomFiscal: dadosTransacao.cupomfiscal,
						dataFiscal: dadosTransacao.datafiscal,
						horaFiscal: dadosTransacao.horafiscal,
					};

					this.startTransaction(dadosTransacao).subscribe(
						(data) => {
							if (data.serviceStatus !== 0) {
								modal.showStatus(
									"Agente CliSiTef ocupado: " +
										data.serviceStatus +
										" - " +
										data.serviceMessage
								);
							} else if (data.clisitefStatus !== 10000) {
								// Documentação com código de erros: SiTef - Interface Simplificada com a aplicação(VRS-254).pdf
								// Título conteúdo na documentação: Tabela 2 - Códigos de retorno das funções de configuração
								// Página: 12
								// Além da tab ela, lembrar do -5 que indica estar desligado o SitDemo/Sitef
								modal.showStatus(
									"Retorno " + data.clisitefStatus + " da CliSiTef"
								);
							} else {
								// Inicia retornou 10000 (via clisitef)
								this.sessao.continua = 0;
								this.sessao.sessionId = data.sessionId;
								this.continueTransaction("", dialogRef);
							}
						},
						(error) => {
							// Aqui cai o HTTP 400 e error.error contém o JSON retornado
							const msg = error.error.serviceMessage || error.message;
							modal.showStatus(`Erro startTransaction: ${msg}`);
							modal.exibirBotaoFecharModal = true;
						}
					);
				}
			},
			(error) => {
				console.log("Erro requisição DadosTransacaoFiserv:");
				console.log(error);
			}
		);
	}

	private montarDadosTransacaoFiserv(item: {
		empresa: string;
		ipsitef: string;
	}): DadosTransacaoFiserv {
		const now = new Date();
		// tslint:disable-next-line:no-non-null-assertion
		const dataFiscal = this.datePipe.transform(now, "yyyyMMdd")!;
		// tslint:disable-next-line:no-non-null-assertion
		const horaFiscal = this.datePipe.transform(now, "HHmmss")!;
		const cupomFiscal = dataFiscal + horaFiscal;

		const usuarioLogado = this.sessionService.loggedUser;
		const operador = this.obterOperadorFiserv(usuarioLogado.nome);
		const terminal = this.obterTerminalFiserv("US", usuarioLogado.usuarioZw);

		return {
			cupomfiscal: cupomFiscal,
			datafiscal: dataFiscal,
			empresa: item.empresa,
			funcao: 130,
			horafiscal: horaFiscal,
			ipsitef: item.ipsitef,
			operador,
			terminal,
		};
	}

	private obterOperadorFiserv(nomeUsuarioLogado: string) {
		if (
			nomeUsuarioLogado != null &&
			nomeUsuarioLogado !== undefined &&
			nomeUsuarioLogado !== ""
		) {
			const nomeMaiusculo = nomeUsuarioLogado.toUpperCase();

			// se maior que 14 caracteres, pega os primeiros 15
			if (nomeMaiusculo.length > 14) {
				return nomeMaiusculo.substring(0, 15);
			}

			// se tiver até 14, retorna completo
			return nomeMaiusculo;
		}

		// se não houver nome, retorna vazio
		return "";
	}

	private obterTerminalFiserv(prefixo, codigoUsuario) {
		// converte o código para string
		const codigoStr = String(codigoUsuario);
		// concatena prefixo + código + zeros e trunca em 8 chars
		return (prefixo + codigoStr + "00000000").substring(0, 8);
	}

	private startTransaction(dados: DadosTransacaoFiserv) {
		const params = new HttpParams()
			.set("sitefIp", dados.ipsitef)
			.set("storeId", dados.empresa)
			.set("terminalId", dados.terminal)
			.set("functionId", String(dados.funcao))
			.set("taxInvoiceNumber", dados.cupomfiscal)
			.set("taxInvoiceDate", dados.datafiscal)
			.set("taxInvoiceTime", dados.horafiscal)
			.set("cashierOperator", dados.operador)
			.set("trnAmount", "")
			.set("trnAdditionalParameters", "")
			.set("trnInitParameters", "[00000000000000;00000000000000]");

		return this.http.post<any>(
			`${this.agentUrl}/startTransaction`,
			params.toString(),
			{
				headers: { "Content-Type": "application/x-www-form-urlencoded" },
			}
		);
	}

	private isExibirBotaoFecharModal(clisiTefStatus) {
		// Documentação com código de retorno CliSiTef: SiTef - Interface Simplificada com a aplicação(VRS-254).pdf
		// Título conteúdo na documentação: 5.2.1 Tabela de códigos de retorno
		// Página: 20
		if (
			clisiTefStatus === 4 ||
			clisiTefStatus === 255 ||
			clisiTefStatus === -5 ||
			clisiTefStatus === -2 ||
			clisiTefStatus === -30 ||
			clisiTefStatus === -43 ||
			clisiTefStatus === -40 ||
			clisiTefStatus === -6 ||
			clisiTefStatus === -120
		) {
			return true;
		}
		return false;
	}

	private continueTransaction(
		dados: string,
		dialogRef: MatDialogRef<TefComponent>
	): void {
		const modal = dialogRef.componentInstance;

		// 1. Monta o payload no formato x-www-form-urlencoded
		const body = new HttpParams()
			.set("sessionId", this.sessao.sessionId)
			.set("data", dados)
			.set("continue", String(this.sessao.continua));

		// 2. Dispara a requisição e já assina aqui dentro
		this.http
			.post<any>(`${this.agentUrl}/continueTransaction`, body.toString(), {
				headers: { "Content-Type": "application/x-www-form-urlencoded" },
			})
			.subscribe(
				(data) => {
					console.log("Resposta /continueTransaction: ", data);
					// Aplicação Agente CliSiTef ocupado
					if (data.serviceStatus !== 0 && this.sessao.continua !== -1) {
						// Manter as mensagens de erro padrão a pedido da Fiserv para ajudar com abertura de chamados
						modal.showStatus(
							"Agente CliSiTef ocupado: " +
								data.serviceStatus +
								" - " +
								data.serviceMessage
						);
						return;
					} else if (data.serviceStatus !== 0 && this.sessao.continua === -1) {
						const retorno = data.data;
						modal.showStatus(retorno.replace(/\?$/, ""));
						modal.exibirBotaoFecharModal = true;
						return;
					}

					// Aplicação Sitef ocupado
					if (data.clisitefStatus !== 10000) {
						// Documentação com código de retorno CliSiTef: SiTef - Interface Simplificada com a aplicação(VRS-254).pdf
						// Título conteúdo na documentação: Tabela 2 - Códigos de retorno das funções de configuração
						// Página: 12
						// Título conteúdo na documentação: 5.2.1 Tabela de códigos de retorno status CliSiTef
						// Página: 20
						let s = "";

						console.log("ENTROU NA FUNÇÃO CONTINUA COM !== 10000");
						console.log("VALOR DO CLISITEFSTATUS: " + data.clisitefStatus);

						if (
							data.clisitefStatus === 0 &&
							!this.verificacaoTransacoesPendentesEmAndamento
						) {
							s = JSON.stringify(this.sessao.ret);
							console.log(s);
							s = s.replace(/},{/g, "},<br>{");
							this.finishTransaction(1, dialogRef); // confirmação = 1; estorno = 0;
						} else if (
							data.clisitefStatus === 0 &&
							this.verificacaoTransacoesPendentesEmAndamento
						) {
							const objeto = this.sessao.ret;
							console.log("Objeto Verificação: ");
							console.log(objeto);
							// Verifica se existe um objeto com TipoCampo 210 e Valor 0 => Isso indica que não tem pendentes
							const encontrado = objeto.some(
								(item) => item.TipoCampo === 210 && item.Valor === "0"
							);

							if (encontrado) {
								console.log("Objeto com TipoCampo 210 e Valor 0.");
								modal.showStatus("Não existe transações pendentes.");
								this.verificacaoTransacoesPendentesEmAndamento = false;
								modal.fecharModalVerificacaoPagamentoPendentesFiserv();
								return;
							} else {
								// Encontrou, deve finalizar
								// tslint:disable-next-line:no-shadowed-variable
								const encontrado: boolean = objeto.some(
									(item) => item.TipoCampo === 160 && item.Valor !== "0"
								);
								if (encontrado) {
									this.sessao.cupomFiscal = objeto.find(
										(item) => item.TipoCampo === 160
									).Valor;
									this.sessao.dataFiscal = objeto.find(
										(item) => item.TipoCampo === 163
									).Valor;
									this.sessao.horaFiscal = objeto.find(
										(item) => item.TipoCampo === 164
									).Valor;
									this.finishTransaction(0, dialogRef); // confirmação = 1; estorno = 0;
									return;
								}
							}
						}
						modal.showStatus("Situação CliSiTef: " + data.clisitefStatus);
						if (this.isExibirBotaoFecharModal(data.clisitefStatus)) {
							modal.exibirBotaoFecharModal = true;
							return;
						} else {
							return;
						}
					}

					// Acima trata apenas a Verifcação de Transações Pendentes
					// Se algum dia for reaproveitar esse service para as cobranças, precisa continuar o restante do códigoa baixo.
					// Se precisar de exemplo de como funciona no ZW, vai no arquivo include_pinpad_fiserv.jsp
					// O trecho aqui é da função continua(dados), após o trecho do comentário Aplicação Sitef ocupado

					switch (data.commandId) {
						case 0:
							// Lista de valores do fieldId: SiTef - Interface Simplificada com a aplicação(VRS-254).pdf
							// No item: 5.3.2 Tabela de valores para TipoCampo
							// Dá página: 37
							const item = {
								TipoCampo: data.fieldId,
								Valor: data.data,
							};
							// acumula o resultado em um JSON, pode ser usado no final da transação para POST ao servidor da automação
							this.sessao.ret.push(item);

							this.continueTransaction("", dialogRef);
							break;

						default:
							modal.showStatus(
								"Chegou uma captura desconhecida: " +
									data.serviceStatus +
									" - " +
									data.serviceMessage +
									"<br>" +
									"Tentando novamente..."
							);
							this.continueTransaction("", dialogRef);
					}
				},
				(error) => {
					// tratamento de erro
					console.error("Erro /continueTransaction", error);
					modal.showStatus(`Erro de rede: ${error.message}`);
				}
			);
	}

	private finishTransaction(
		confirma: 0 | 1,
		dialogRef: MatDialogRef<TefComponent>
	): void {
		const modal = dialogRef.componentInstance;

		// 1) Prepara o payload com todos os campos
		const params = new HttpParams()
			.set("confirm", String(confirma))
			.set("sessionId", this.sessao.sessionId)
			.set("taxInvoiceNumber", this.sessao.cupomFiscal || "")
			.set("taxInvoiceDate", this.sessao.dataFiscal || "")
			.set("taxInvoiceTime", this.sessao.horaFiscal || "");

		// 2) Informa que está finalizando
		modal.showStatus("Finalizando pagamento...");

		// 3) Chama o endpoint
		this.http
			.post<any>(`${this.agentUrl}/finishTransaction`, params.toString(), {
				headers: { "Content-Type": "application/x-www-form-urlencoded" },
			})
			.subscribe(
				(data) => {
					// Converte string JSON para objeto, se necessário
					if (typeof data === "string") {
						try {
							data = JSON.parse(data);
						} catch {
							/* keep as string */
						}
					}

					// 4) Se serviceStatus != 0 → tenta confirmar, após 2,5s
					if (data.serviceStatus !== undefined && data.serviceStatus !== 0) {
						modal.showStatus(
							`Agente CliSiTef ocupado: ${data.serviceStatus} – ${data.serviceMessage}`
						);
						setTimeout(() => {
							// na segunda vez, força confirmação = 1
							this.finishTransaction(1, dialogRef);
						}, 2500);
						return;
					}

					// 5) Se está no fluxo de verificação de pendentes
					if (this.verificacaoTransacoesPendentesEmAndamento) {
						modal.showStatus("As transações pendentes foram canceladas.");
						this.verificacaoTransacoesPendentesEmAndamento = false;
						modal.exibirBotaoFecharModal = true;
						return;
					}

					// 6) Fluxo normal de finalização
					setTimeout(() => {
						modal.showStatus("Finalizando com Sucesso");
						this.verificacaoTransacoesPendentesEmAndamento = false;
						modal.exibirBotaoFecharModal = true;
					}, 2500);
				},
				(err) => {
					// 7) Em caso de erro de rede ou HTTP
					console.error("Erro /finishTransaction", err);
					modal.showStatus(
						`Erro /finishTransaction: ${err.status} – ${err.message}`
					);
				}
			);
	}

	/** Tratamento genérico de erros HTTP */
	private handleError(error: HttpErrorResponse) {
		// Log no console ou serviço de logging
		console.error("Erro HTTP", error);
		return throwError(
			() => new Error(error.error.message || "Erro de comunicação com o agente")
		);
	}
}
