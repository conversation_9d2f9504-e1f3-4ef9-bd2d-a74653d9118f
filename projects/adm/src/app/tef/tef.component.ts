import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { MatDialogRef } from "@angular/material/dialog";

@Component({
	selector: "adm-tef",
	templateUrl: "./tef.component.html",
	styleUrls: ["./tef.component.scss"],
	encapsulation: ViewEncapsulation.None, // desliga a “sombra” de CSS
})
export class TefComponent implements OnInit {
	mensagemBackend = "";
	exibirBotaoFecharModal = false;

	constructor(public dialogRef: MatDialogRef<TefComponent>) {}

	ngOnInit() {}

	fecharModalVerificacaoPagamentoPendentesFiserv(): void {
		this.dialogRef.close(); // fecha o modal
	}

	showStatus(mensagemExibir: string): void {
		this.mensagemBackend = mensagemExibir;
	}
}
