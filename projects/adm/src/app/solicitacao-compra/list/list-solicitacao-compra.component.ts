import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	ViewChild,
} from "@angular/core";
import { MatDialog } from "@angular/material";
import { ActivatedRoute, Router } from "@angular/router";
import { SnotifyService } from "ng-snotify";
import { GridFilterType } from "projects/ui/src/public-api";
import { SessionService } from "@base-core/client/session.service";
import { GridFilterConfig, PactoDataGridConfig } from "ui-kit";
import { AdmCoreApiEmpresaService } from "adm-core-api";
import { AdmRestService } from "../../adm-rest.service";

@Component({
	selector: "adm-list-solicitacao-compra",
	templateUrl: "./list-solicitacao-compra.component.html",
	styleUrls: ["./list-solicitacao-compra.component.scss"],
})
export class ListSolicitacaoCompraComponent implements OnInit, AfterViewInit {
	@ViewChild("colunaTitulo", { static: true }) colunaTitulo;
	@ViewChild("colunaDataSolicitacao", { static: true }) colunaDataSolicitacao;
	@ViewChild("colunaSituacao", { static: true }) colunaSituacao;
	@ViewChild("tableData", { static: true }) tableData;
	@ViewChild("buttonAdd", { static: true }) buttonAdd;

	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };
	data: any;
	empresasList!: any;

	constructor(
		private cd: ChangeDetectorRef,
		private router: Router,
		private route: ActivatedRoute,
		public dialog: MatDialog,
		private admRest: AdmRestService,
		private sessionService: SessionService,
		private admCoreApiEmpresaService: AdmCoreApiEmpresaService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.loadEmpresas();
	}

	loadEmpresas(): void {
		this.admCoreApiEmpresaService.findAll().subscribe((res: any) => {
			const empresasMap = res.content.map((empresa) => ({
				value: empresa.codigo,
				label: empresa.nome,
			}));
			this.empresasList = empresasMap;
			this.initFilter();
			this.cd.detectChanges();
		});
	}

	ngAfterViewInit(): void {
		this.initTable();
	}

	private initTable() {
		this.table = new PactoDataGridConfig({
			quickSearch: true,
			showFilters: true,
			endpointUrl: this.admRest.buildFullUrlAdmCore("solicitacao-compra"),
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					defaultVisible: true,
				},
				{
					nome: "titulo",
					titulo: "Titulo",
					visible: true,
					defaultVisible: true,
				},
				{
					nome: "dataSolicitacao",
					titulo: "Data da Solicitação",
					valueTransform(v) {
						return v ? new Date(v).toLocaleDateString("pt-BR") : "";
					},
					visible: true,
					defaultVisible: true,
				},
				{
					nome: "situacao",
					titulo: "Situação",
					visible: true,
					defaultVisible: true,
					celula: this.colunaSituacao,
				},
			],
			actions: [
				{
					nome: "aprovarnegar",
					iconClass: "pct pct-eye cor-action-default-able04",
					tooltipText: "Visualizar solicitação de compra",
				},
			],
		});
		this.cd.detectChanges();
	}

	private initFilter() {
		this.filterConfig = {
			filters: [
				{
					name: "situacao",
					label: "Situação",
					type: GridFilterType.DS3_SELECT_ONE,
					options: [
						{ value: "APROVADO", label: "Aprovado" },
						{ value: "NEGADO", label: "Negado" },
						{ value: "PENDENTE", label: "Pendente" },
					],
					initialValue: "PENDENTE",
				},
				{
					name: "dataSolicitacao",
					label: "Data da Solicitação",
					type: GridFilterType.DS3_DATE_RANGE,
				},
			],
		};
	}

	btnClickHandler() {
		if (this.sessionService.temPermissaoAdm("10.09")) {
			this.router.navigate(["incluir"], {
				relativeTo: this.route,
				queryParams: { mode: "incluir" },
			});
		} else {
			this.notificationService.error(
				"10.09 - Permitir cadastrar solicitação de compras"
			);
		}
	}

	btnApproveHandler(item) {
		this.router.navigate(["aprovar"], {
			relativeTo: this.route,
			queryParams: { mode: "aprovar", codigo: item.codigo },
		});
	}

	actions($event) {
		switch ($event.iconName) {
			case "aprovarnegar":
				this.handleApproveAction($event.row.codigo);
				break;
			default:
				this.notificationService.error("Ação desconhecida");
		}
	}

	handleApproveAction(codigo: string) {
		this.router.navigate(["aprovar"], {
			relativeTo: this.route,
			queryParams: { mode: "aprovar", codigo: codigo },
		});
	}
}
