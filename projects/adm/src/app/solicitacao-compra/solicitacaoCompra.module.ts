import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";

import { UiModule } from "ui-kit";
import { SdkModule } from "sdk";
import { LayoutModule } from "../layout/layout.module";
import { ListSolicitacaoCompraComponent } from "@adm/solicitacao-compra/list/list-solicitacao-compra.component";
import { AddApproveSolicitacaoCompraComponent } from "@adm/solicitacao-compra/add/add-approve-solicitacao-compra.component";
import { SolicitacaoCompraRoutingModule } from "@adm/solicitacao-compra/solicitacaoCompraRouting.module";
import { ModalConfirmDisapproveComponent } from "@adm/solicitacao-compra/modal-confirm-disapprove/modal-confirm-disapprove.component";
import { ModalSolicitacaoCompraUploadArquivosComponent } from "@adm/solicitacao-compra/documentos/documentos-container/modal-upload-arquivos/modal-solicitacao-compra-upload-arquivos.component";
import { ModalSolicitacaoCompraDetalharDocumentoComponent } from "@adm/solicitacao-compra/documentos/documentos-container/modal-detalhar-documento/modal-solicitacao-compra-detalhar-documento.component";
import { SolicitacaoCompraHistoricoAnexosTableComponent } from "@adm/solicitacao-compra/documentos/documentos-container/historico-anexos-table/solicitacao-compra-historico-anexos-table.component";
import { SolicitacaoCompraDocumentosTableComponent } from "@adm/solicitacao-compra/documentos/documentos-container/documentos-table/solicitacao-compra-documentos-table.component";

@NgModule({
	declarations: [
		ListSolicitacaoCompraComponent,
		AddApproveSolicitacaoCompraComponent,
		ModalConfirmDisapproveComponent,
		ModalSolicitacaoCompraUploadArquivosComponent,
		ModalSolicitacaoCompraDetalharDocumentoComponent,
		SolicitacaoCompraHistoricoAnexosTableComponent,
		SolicitacaoCompraDocumentosTableComponent,
	],
	imports: [
		SdkModule,
		SolicitacaoCompraRoutingModule,
		LayoutModule,
		CommonModule,
		NgbModule,
		UiModule,
		ReactiveFormsModule,
	],
	entryComponents: [
		ModalConfirmDisapproveComponent,
		ModalSolicitacaoCompraUploadArquivosComponent,
		ModalSolicitacaoCompraDetalharDocumentoComponent,
	],
})
export class SolicitacaoCompraModule {}
