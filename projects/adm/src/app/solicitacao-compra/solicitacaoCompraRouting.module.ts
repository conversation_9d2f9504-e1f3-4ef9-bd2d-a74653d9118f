import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ListSolicitacaoCompraComponent } from "@adm/solicitacao-compra/list/list-solicitacao-compra.component";
import { AddApproveSolicitacaoCompraComponent } from "@adm/solicitacao-compra/add/add-approve-solicitacao-compra.component";

const routes: Routes = [
	{
		path: "",
		component: ListSolicitacaoCompraComponent,
	},
	{
		path: "incluir",
		component: AddApproveSolicitacaoCompraComponent,
	},
	{
		path: "aprovar",
		component: AddApproveSolicitacaoCompraComponent,
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class SolicitacaoCompraRoutingModule {}
