import { ChangeDetectorRef, Component, Input, OnInit } from "@angular/core";
import { DomSanitizer, SafeResourceUrl } from "@angular/platform-browser";

@Component({
	selector: "adm-modal-solicitacao-compra-detalhar-documento",
	templateUrl: "./modal-solicitacao-compra-detalhar-documento.component.html",
	styleUrls: ["./modal-solicitacao-compra-detalhar-documento.component.scss"],
})
export class ModalSolicitacaoCompraDetalharDocumentoComponent
	implements OnInit
{
	constructor(private cd: ChangeDetectorRef, private sanitizer: DomSanitizer) {}

	@Input() rowData;
	imagemUrl;
	titleImagem;
	formatoArquivo;
	urlSegura: SafeResourceUrl;
	carregando = false;

	ngOnInit() {
		console.log(this.rowData);
		this.imagemUrl =
			this.rowData.anexo ||
			this.rowData.urlArquivo ||
			this.rowData.arquivoUpload ||
			this.rowData.arquivoBase64;
		this.titleImagem = this.rowData.nome || this.rowData.nomeArquivo;
		this.formatoArquivo = this.rowData.formatoArquivo;
		let urlView = "";
		if (
			this.imagemUrl.startsWith("data") ||
			/^[A-Za-z0-9+/=]+$/.test(this.imagemUrl)
		) {
			urlView = this.imagemUrl;
		} else {
			urlView =
				"https://docs.google.com/gview?url=" +
				this.imagemUrl +
				"&embedded=true";
			console.log(urlView);
		}
		this.urlSegura = this.sanitizer.bypassSecurityTrustResourceUrl(urlView);
		this.cd.detectChanges();
	}

	isAbrirDocumento() {
		return (
			this.formatoArquivo === ".pdf" ||
			this.formatoArquivo === ".doc" ||
			this.formatoArquivo === ".docx" ||
			this.formatoArquivo === ".txt"
		);
	}
}
