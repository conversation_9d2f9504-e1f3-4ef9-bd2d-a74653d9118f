import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SnotifyService } from "ng-snotify";

import {
	DialogService,
	PactoDataGridConfig,
	PactoDataGridState,
	PactoModalSize,
	RelatorioComponent,
	TableData,
	TraducoesXinglingComponent,
} from "ui-kit";

import { TipoUploadEnum } from "../classes/tipo-upload.enum";
import { ModalSolicitacaoCompraDetalharDocumentoComponent } from "@adm/solicitacao-compra/documentos/documentos-container/modal-detalhar-documento/modal-solicitacao-compra-detalhar-documento.component";

@Component({
	selector: "adm-solicitacao-compra-documentos-table",
	templateUrl: "./solicitacao-compra-documentos-table.component.html",
	styleUrls: ["./solicitacao-compra-documentos-table.component.scss"],
})
export class SolicitacaoCompraDocumentosTableComponent
	implements OnInit, AfterViewInit
{
	@ViewChild("traducao", { static: true })
	traducao: TraducoesXinglingComponent;
	@ViewChild("columnNomeArquivoDocumento", { static: true })
	columnNomeArquivoDocumento: TemplateRef<any>;
	@ViewChild("columnDataArquivo", { static: true })
	columnDataArquivo: TemplateRef<any>;
	@ViewChild("columnFormato", { static: true }) columnFormato: TemplateRef<any>;
	@ViewChild("tableDocumentosComponent", { static: false })
	tableDocumentosComponent: RelatorioComponent;
	@Output() editEvent: EventEmitter<any> = new EventEmitter();
	@Input() anexos = new Array<any>();

	dataGridConfig: PactoDataGridConfig;
	dataTable = {
		content: new Array<{
			nomeArquivo: string;
			tipo: string;
			dataUpload: any;
		}>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	itensPerPage = [{ id: 5, label: "5" }];
	page = 0;
	size = 0;

	constructor(
		private notify: SnotifyService,
		private dialogService: DialogService,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {}

	ngAfterViewInit() {
		this.initTable();
		if (this.anexos.length > 0) {
			this.createPageObject();
		}
	}

	initTable(): void {
		const state: PactoDataGridState = new PactoDataGridState();
		state.paginaTamanho = 5;
		state.paginaNumero = 0;
		this.dataGridConfig = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: false,
			columns: [
				{
					nome: "nomeArquivo",
					titulo: this.columnNomeArquivoDocumento,
					visible: true,
					ordenavel: false,
					width: "20%",
				},
				{
					nome: "formatoArquivo",
					titulo: this.columnFormato,
					visible: true,
					ordenavel: false,
				},
				{
					nome: "observacao",
					titulo: "Observação",
					visible: true,
					ordenavel: false,
					width: "40%",
				},
			],
			actions: [
				{
					nome: "action-visualizar-documento",
					iconClass: "pct pct-eye cor-azulim05",
					tooltipText: "Visualizar arquivo",
				},
				{
					nome: "action-edit-documento",
					iconClass: "pct pct-edit cor-azulim05",
					tooltipText: "Editar",
					showIconFn: (row) =>
						row.codigo === null || row.codigo === undefined || row.codigo === 0,
				},
				{
					nome: "action-delet-documento",
					iconClass: "pct pct-trash-2 cor-laranjinha05",
					tooltipText: "Excluir",
					showIconFn: (row) =>
						row.codigo === null || row.codigo === undefined || row.codigo === 0,
				},
			],
		});
		if (this.tableDocumentosComponent) {
			this.tableDocumentosComponent.pageSizeControl.setValue(5);
		}
		this.dataGridConfig.dataAdapterFn = (serverData): TableData<any> => {
			serverData = this.dataTable;
			return serverData;
		};
	}

	loadData() {
		if (this.anexos.length > 0) {
			this.createPageObject();
		}
	}

	iconClickFn(event: { row: any; iconName: string }) {
		switch (event.iconName) {
			case "action-visualizar-documento":
				this.visualizar(event.row);
				break;
			case "action-edit-documento":
				this.edit(event.row);
				break;
			case "action-delet-documento":
				this.delete(event.row);
				break;
		}
	}

	edit(anexo) {
		const uploadArquivo = anexo;
		uploadArquivo.arquivoBase64 = anexo.arquivoUpload;
		uploadArquivo.tipo = TipoUploadEnum.DOCUMENTO;
		uploadArquivo.tituloModal = "Documento";
		uploadArquivo.nomeArquivoApresentar =
			anexo.nomeArquivo + anexo.formatoArquivo;
		this.editEvent.emit(uploadArquivo);
	}

	delete(row: any) {
		this.anexos = this.anexos.filter(
			(anexo) => anexo.nomeArquivo !== row.nomeArquivo
		);
		this.createPageObject();
	}

	private isNullOrEmpty(value) {
		return value === null || value === undefined || value === "";
	}

	isAbrirDocumento(formatoArquivo) {
		return (
			formatoArquivo === ".pdf" ||
			formatoArquivo === ".doc" ||
			formatoArquivo === ".docx" ||
			formatoArquivo === ".txt"
		);
	}

	visualizar(row) {
		console.log(row);
		if (!this.isNullOrEmpty(row)) {
			if (!this.isAbrirDocumento(row.formatoArquivo)) {
				const modalRef = this.dialogService.open(
					"Visualizar Documento",
					ModalSolicitacaoCompraDetalharDocumentoComponent,
					PactoModalSize.LARGE
				);
				modalRef.componentInstance.rowData = row;
			} else {
				const urlAbrir = row.anexo || row.urlArquivo || row.arquivoUpload;
				const abrirBlobEmPopup = (blob: Blob) => {
					const urlBlob = URL.createObjectURL(blob);
					const popupWidth = 800;
					const popupHeight = 600;
					const left = window.screenX + (window.outerWidth - popupWidth) / 2;
					const top = window.screenY + (window.outerHeight - popupHeight) / 2;

					window.open(
						urlBlob,
						"PopupVisualizacaoArquivo",
						`width=${popupWidth},height=${popupHeight},left=${left},top=${top},resizable=yes,scrollbars=yes`
					);
				};

				if (urlAbrir.startsWith("data:")) {
					const matches = urlAbrir.match(/^data:(.+);base64,(.+)$/);
					if (!matches || matches.length !== 3) {
						this.notify.warning("Anexo inválido!");
						return;
					}

					const mimeType = matches[1];
					const base64Data = matches[2];
					const byteCharacters = atob(base64Data);
					const byteNumbers = Array.from(byteCharacters, (c) =>
						c.charCodeAt(0)
					);
					const byteArray = new Uint8Array(byteNumbers);
					const blob = new Blob([byteArray], { type: mimeType });

					abrirBlobEmPopup(blob);
				} else {
					fetch(urlAbrir)
						.then((response) => {
							if (!response.ok) throw new Error("Response não OK");
							return response.blob();
						})
						.then((blob) => abrirBlobEmPopup(blob))
						.catch(() => {
							window.open(urlAbrir, "_blank");
						});
				}
			}
		} else {
			this.notify.warning("Esse registro não possui anexo!");
		}
	}

	createPageObject(page = 1, size = 5) {
		this.dataTable.totalElements = this.anexos.length;
		this.dataTable.size = size;
		this.dataTable.totalPages = Math.ceil(
			+(this.dataTable.totalElements / this.dataTable.size)
		);
		this.dataTable.first = page === 0 || page === 1;
		this.dataTable.last = page === this.dataTable.totalPages;
		this.dataTable.content = this.anexos.slice(size * page - size, size * page);
		if (this.tableDocumentosComponent) {
			this.tableDocumentosComponent.showBtnAdd = false;
			this.tableDocumentosComponent.reloadData();
		}
		this.cd.detectChanges();
	}

	changePage(page: any) {
		if (page >= 0) {
			this.page = page;
			this.createPageObject(this.page);
		}
	}

	changePageSize(size: any) {
		if (size > 0) {
			this.size = size;
			this.createPageObject(this.page, this.size);
		}
	}
}
