import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

import { SnotifyService } from "ng-snotify";

import { TipoUploadEnum } from "../classes/tipo-upload.enum";
import { UploadArquivo } from "../classes/upload-arquivo.model";
import {
	DialogService,
	PactoModalSize,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
} from "ui-kit";
import { ModalSolicitacaoCompraDetalharDocumentoComponent } from "@adm/solicitacao-compra/documentos/documentos-container/modal-detalhar-documento/modal-solicitacao-compra-detalhar-documento.component";

@Component({
	selector: "adm-modal-solicitacao-compra-upload-arquivos",
	templateUrl: "./modal-solicitacao-compra-upload-arquivos.component.html",
	styleUrls: ["./modal-solicitacao-compra-upload-arquivos.component.scss"],
})
export class ModalSolicitacaoCompraUploadArquivosComponent implements OnInit {
	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@Input() uploadArquivo: UploadArquivo;
	@Input() isPermitidolancamentoDeAtestadoDeAptidaoFisica: boolean;
	@Input() isPermitidolancamentoDeDocumento: boolean;
	formGroup: FormGroup = new FormGroup({
		tipoArquivo: new FormControl(),
		nomeArquivo: new FormControl(""),
		observacao: new FormControl("", Validators.required),
		dataInicialAtestado: new FormControl(new Date()),
		quantidadeDias: new FormControl(0, Validators.min(1)),
		produto: new FormControl(undefined, Validators.required),
		contrato: new FormControl(undefined, Validators.required),
		justificativa: new FormControl(undefined, Validators.required),
	});
	tipoUpload = [];

	constructor(
		private cd: ChangeDetectorRef,
		public dialog: NgbActiveModal,
		private dialogService: DialogService,
		private notify: SnotifyService
	) {}

	ngOnInit() {
		this.tipoUpload = [];
		if (this.isPermitidolancamentoDeDocumento) {
			this.tipoUpload.push({
				id: TipoUploadEnum.DOCUMENTO,
				descricao: "Documento",
			});
		}
		this.initForms();
		this.cd.detectChanges();
	}

	initForms() {
		this.formGroup.get("tipoArquivo").setValue(this.uploadArquivo.tipo);
		this.formGroup.get("nomeArquivo").setValue(this.uploadArquivo.nomeArquivo);
		this.formGroup.get("observacao").setValue(this.uploadArquivo.observacao);

		if (
			this.isNullOrUndefinedOrEmpty(this.uploadArquivo.nomeArquivoApresentar) &&
			!this.isNullOrUndefinedOrEmpty(this.uploadArquivo.urlArquivo)
		) {
			this.uploadArquivo.nomeArquivoApresentar = "arquivo";
		}
	}

	gravar() {
		switch (this.formGroup.get("tipoArquivo").value) {
			case TipoUploadEnum.DOCUMENTO:
				this.gravarDocumento();
				break;
			default:
				this.notify.error('O campo "Tipo de arquivo" deve ser informado!');
				break;
		}
	}

	gravarDocumento() {
		const dto = {
			codigo: undefined,
			data: undefined,
			anexo: undefined,
			arquivoUpload: undefined,
			nomeArquivo: "",
			formatoArquivo: "",
			observacao: "",
		};
		dto.nomeArquivo = this.formGroup.get("nomeArquivo").value;
		dto.observacao = this.formGroup.get("observacao").value;
		dto.data = new Date().getTime();
		dto.arquivoUpload = this.uploadArquivo.arquivoBase64;
		dto.formatoArquivo = this.uploadArquivo.formatoArquivo;

		if (this.validarCamposDocumento(dto)) {
			this.dialog.close(dto);
		}
	}

	produtoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
				quicksearchFields: ["nome"],
			}),
		};
	};

	formTipoArquivoEstaDesativado(): boolean {
		if (this.uploadArquivo.tipo !== TipoUploadEnum.NOVO) {
			return true;
		}
		return false;
	}

	isAbrirDocumento(formatoArquivo) {
		return (
			formatoArquivo === ".pdf" ||
			formatoArquivo === ".doc" ||
			formatoArquivo === ".docx" ||
			formatoArquivo === ".txt"
		);
	}

	download() {
		if (!this.isNullOrUndefinedOrEmpty(this.uploadArquivo.arquivoBase64)) {
			if (!this.isAbrirDocumento(this.uploadArquivo.formatoArquivo)) {
				const modalRef = this.dialogService.open(
					"Visualizar Documento",
					ModalSolicitacaoCompraDetalharDocumentoComponent,
					PactoModalSize.LARGE
				);
				modalRef.componentInstance.rowData = this.uploadArquivo;
			} else {
				const urlAbrir =
					this.uploadArquivo.urlArquivo || this.uploadArquivo.arquivoBase64;
				const abrirBlobEmPopup = (blob: Blob) => {
					const urlBlob = URL.createObjectURL(blob);
					const popupWidth = 800;
					const popupHeight = 600;
					const left = window.screenX + (window.outerWidth - popupWidth) / 2;
					const top = window.screenY + (window.outerHeight - popupHeight) / 2;

					window.open(
						urlBlob,
						"PopupVisualizacaoArquivo",
						`width=${popupWidth},height=${popupHeight},left=${left},top=${top},resizable=yes,scrollbars=yes`
					);
				};

				if (urlAbrir.startsWith("data:")) {
					const matches = urlAbrir.match(/^data:(.+);base64,(.+)$/);
					if (!matches || matches.length !== 3) {
						this.notify.warning("Anexo inv�lido!");
						return;
					}

					const mimeType = matches[1];
					const base64Data = matches[2];
					const byteCharacters = atob(base64Data);
					const byteNumbers = Array.from(byteCharacters, (c) =>
						c.charCodeAt(0)
					);
					const byteArray = new Uint8Array(byteNumbers);
					const blob = new Blob([byteArray], { type: mimeType });

					abrirBlobEmPopup(blob);
				} else {
					fetch(urlAbrir)
						.then((response) => {
							if (!response.ok) throw new Error("Response n�o OK");
							return response.blob();
						})
						.then((blob) => abrirBlobEmPopup(blob))
						.catch(() => {
							window.open(
								urlAbrir,
								"PopupVisualizacaoArquivo",
								`width=800,height=600,left=100,top=100,resizable=yes,scrollbars=yes`
							);
						});
				}
			}
		}
	}

	private validarCamposDocumento(dto): boolean {
		if (this.isNullOrUndefinedOrEmpty(dto.observacao)) {
			this.notify.error(this.traducoes.getLabel("observacao-obrigatoria"));
			return false;
		}
		return true;
	}

	private isNullOrUndefinedOrEmpty(value) {
		return value === null || value === undefined || value === "" || value === 0;
	}

	existeArquivoAnexo(): boolean {
		if (
			!this.isNullOrUndefinedOrEmpty(this.uploadArquivo.urlArquivo) ||
			!this.isNullOrUndefinedOrEmpty(this.uploadArquivo.arquivoBase64)
		) {
			return true;
		}
		return false;
	}
}
