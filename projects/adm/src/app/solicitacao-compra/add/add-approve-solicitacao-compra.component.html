<pacto-cat-layout-v2>
	<header class="cabecalho-principal">
		<div class="breadcrumbs">
			<div class="mod pct-title5">Administrativo</div>
			<i class="pct pct-chevron-right"></i>
			<div class="mod pct-title5">Operações</div>
			<i class="pct pct-chevron-right"></i>
			<div class="mod pct-title5">Solicitações de Compra</div>
			<i class="pct pct-chevron-right"></i>
		</div>
		<div class="navegate">
			<div class="seta type-default-title">
				<i
					(click)="return()"
					class="pct pct-arrow-left"
					id="detalhamento-contrato-voltar"></i>
			</div>
			<div class="titulo">Realizar solicitação de compras</div>
		</div>
	</header>
	<pacto-cat-card-plain class="mx-2">
		<form [formGroup]="form">
			<div class="row" *ngIf="!approveMode">
				<ds3-form-field class="col-12">
					<ds3-field-label>Título</ds3-field-label>
					<input ds3Input [min]="1" formControlName="titulo" type="text" />
				</ds3-form-field>
			</div>

			<div class="row" *ngIf="codigo && approveMode">
				<ds3-form-field class="col-12">
					<ds3-field-label>Título</ds3-field-label>
					<input
						ds3Input
						[readonly]="true"
						[min]="1"
						formControlName="titulo"
						type="text" />
				</ds3-form-field>
			</div>

			<div class="row">
				<ds3-form-field class="col-12 mt-4" *ngIf="!codigo && !approveMode">
					<ds3-field-label>
						Descrição do motivo da solicitação *
					</ds3-field-label>
					<textarea
						ds3Input
						formControlName="descricao"
						type="text"
						rows="5"
						cols="12"></textarea>
				</ds3-form-field>
			</div>

			<div class="row">
				<ds3-form-field class="col-12 mt-4" *ngIf="codigo && approveMode">
					<ds3-field-label>
						Descrição do motivo da solicitação *
					</ds3-field-label>
					<textarea
						disabled="true"
						ds3Input
						formControlName="descricao"
						type="text"
						rows="5"
						cols="12"></textarea>
				</ds3-form-field>
			</div>

			<div class="row">
				<ds3-form-field class="col-12 mt-4" *ngIf="codigo && isSituacaoNegado">
					<ds3-field-label>Motivo da negação *</ds3-field-label>
					<textarea
						disabled="true"
						ds3Input
						formControlName="motivoNegacao"
						type="text"
						rows="5"
						cols="12"></textarea>
				</ds3-form-field>
			</div>

			<div class="row mt-4">
				<div class="col-12">
					<pacto-cat-card-plain *ngIf="!codigo">
						<span class="section-title">Realizar upload</span>
						<div class="anexo-input">
							<pacto-cat-file-input
								#fileInputComponent
								[control]="form.get('file')"
								[nomeControl]="form.get('nomeArquivo')"></pacto-cat-file-input>
						</div>
					</pacto-cat-card-plain>
					<pacto-cat-card-plain>
						<span class="section-title">
							Anexo (cotação, especificações técnicas, etc...)
						</span>
						<div class="relatorio-anexos">
							<adm-solicitacao-compra-documentos-table
								#documentosTableComponent
								(documentosChangeEvent)="documentosChangeEvent($event)"
								(editEvent)="
									editEvent($event)
								"></adm-solicitacao-compra-documentos-table>
						</div>
					</pacto-cat-card-plain>
				</div>
			</div>
			<div class="footer">
				<div class="row d-flex justify-content-end mt-4">
					<div *ngIf="!codigo" class="buttons">
						<button (click)="return()" class="mr-2" ds3-outlined-button>
							Cancelar
						</button>
						<button (click)="save()" ds3-flat-button>Enviar solicitação</button>
					</div>

					<div *ngIf="codigo && isSituacaoAprovado" class="buttons">
						<button (click)="redirectToCompra()" class="mr-2" ds3-flat-button>
							Lançar compra
						</button>
					</div>

					<div *ngIf="codigo && isSituacaoPendente" class="buttons">
						<button
							(click)="btnApproveHandler(codigo)"
							class="mr-2"
							ds3-outlined-button
							style="border-color: #fa1e1e; color: #fa1e1e">
							Negar
						</button>
						<button (click)="aprovarSolicitacao()" class="mr-2" ds3-flat-button>
							Aprovar
						</button>
					</div>
				</div>
			</div>
		</form>
	</pacto-cat-card-plain>
</pacto-cat-layout-v2>
