<div class="negociacao-contrato">
	<div (scroll)="onScroll($event)" class="negociacao-contrato-scroll">
		<adm-layout
			(changeNovaVersao)="voltarHome()"
			(goBack)="voltarHome()"
			[forcarTrue]="irParaVersaoAntiga === false"
			[recurso]="recurso"
			appStickyFooter
			i18n-modulo="@@negociacao:modulo"
			i18n-pageTitle="@@sorteio:title"
			modulo="Administrativo"
			pageTitle="Negociação">
			<pacto-cat-card-plain>
				<div class="table-wrapper pacto-shadow">
					<div class="small-info">
						<i class="pct pct-info"></i>
						<span>
							Para tornar suas vendas mais ágeis, temos uma grande novidade em
							nosso sistema! Agora, com base em seu histórico de vendas,
							sugerimos o plano, duração, modalidades e horários.
						</span>
						<i
							*ngIf="zwBoot"
							title="Essa negociação utiliza tecnologia zwboot"
							class="pct pct-rocket"></i>
					</div>

					<div class="info-top">
						<div>
							<span>Data de lançamento: {{ dataLancamento }}</span>
							<span>
								Período de contrato: {{ simulado?.vigenciaDe }} -
								{{ simulado?.vigenciaAteAjustada }}
							</span>
							<span>Tipo de contrato: {{ tipoContrato }}</span>
							<span>Descricao: {{ descricao }}</span>
						</div>

						<pacto-cat-button
							(click)="abrirDataLancamento()"
							label="Editar"
							type="OUTLINE_NO_BORDER"></pacto-cat-button>
					</div>

					<div
						*ngIf="simulado?.informacaoRenovacaoCreditoTreino"
						class="toast cima">
						<i class="pct pct-check"></i>
						<span>
							{{ simulado?.informacaoRenovacaoCreditoTreino }}
						</span>
					</div>

					<div
						[ngClass]="{
							'credito-treino':
								negociacao?.vendaCreditoTreino && !negociacao?.creditoSessao
						}"
						class="linha-superior">
						<!-- PLANO -->
						<div class="select">
							<pacto-cat-select
								[control]="planoFc"
								[id]="'input-plano'"
								[items]="planos"
								idKey="codigo"
								label="Plano"
								labelKey="descricao"></pacto-cat-select>
						</div>

						<!-- duracao -->
						<div class="select">
							<pacto-cat-form-select
								[control]="duracaoFc"
								[id]="'input-duracao'"
								[items]="negociacao?.duracoes"
								idKey="codigo"
								label="Duração"
								labelKey="descricao"></pacto-cat-form-select>
						</div>

						<!-- horarios -->
						<div class="select">
							<pacto-cat-form-select
								[control]="horarioFc"
								[id]="'input-horario'"
								[items]="negociacao?.horarios"
								idKey="codigo"
								label="Horários"
								labelKey="descricao"></pacto-cat-form-select>
						</div>

						<!-- creditos -->
						<div
							*ngIf="
								negociacao?.vendaCreditoTreino && !negociacao?.creditoSessao
							"
							class="select">
							<pacto-cat-form-select
								[control]="creditoFc"
								[id]="'input-credito'"
								[items]="creditos"
								idKey="codigo"
								label="Créditos"
								labelKey="quantidadeCreditoCompra"></pacto-cat-form-select>
						</div>
					</div>

					<div
						*ngIf="
							negociacao?.creditoTreinoNaoCumulativo &&
							creditoEscolhido?.quantidadeCreditoMensal > 0
						"
						class="aviso-importante">
						<i class="pct pct-alert-triangle"></i>
						<span>
							O aluno poderá usar
							{{ creditoEscolhido.quantidadeCreditoMensal }} créditos por mês!
						</span>
					</div>

					<adm-pacote-modalidades-negociacao
						#tabelaModalidades></adm-pacote-modalidades-negociacao>

					<pacto-cat-accordion #accordionProduto [open]="usaProdutos">
						<accordion-header class="title-content">
							<span>Produtos</span>
						</accordion-header>
						<accordion-body>
							<div class="accordion-body-content">
								<adm-produtos-negociacao
									*ngIf="negociacao"
									[plano]="codigoPlanoSelecionado"
									[produtosPlano]="
										negociacao.produtos
									"></adm-produtos-negociacao>
							</div>
						</accordion-body>
					</pacto-cat-accordion>
					<div class="simule-clique"></div>

					<pacto-cat-accordion
						#accordionDescontoConvenio
						[open]="usaDesconto || descontoRenovacaoAntecipada">
						<accordion-header class="title-content">
							<span>Desconto e convênio</span>
						</accordion-header>
						<accordion-body>
							<div class="accordion-body-content">
								<div class="pct-table-cell">
									<adm-desconto-negociacao
										[descontoRenovacaoAntecipada]="descontoRenovacaoAntecipada"
										#addDescontoComponent></adm-desconto-negociacao>
								</div>
							</div>
							<div
								*ngIf="simulado?.descontoConvenioNaoAplicado"
								class="toast warning convenio">
								<i class="pct pct-alert-triangle"></i>
								<span>
									{{ simulado?.descontoConvenioNaoAplicado }}
								</span>
							</div>
						</accordion-body>
					</pacto-cat-accordion>
				</div>
			</pacto-cat-card-plain>

			<pacto-cat-card-plain>
				<div class="table-wrapper pacto-shadow">
					<div class="row">
						<!-- PLANO -->
						<div class="select col-sm-12 col-md-4 col-lg-4 col-xl-4">
							<pacto-cat-select
								*ngIf="!bolsa"
								[control]="condicaoFc"
								[id]="'input-condicao'"
								[items]="condicoes"
								idKey="codigo"
								label="Condição de pagamento"
								labelKey="descricao"></pacto-cat-select>
						</div>

						<!-- duracao -->
						<div class="select col-sm-12 col-md-2 col-lg-2 col-xl-2">
							<pacto-cat-select
								*ngIf="
									!negociacao?.deveGerarParcelasComValorDiferente &&
									negociacao?.diasCartao &&
									negociacao?.diasCartao.length > 0
								"
								[control]="diaCartaoFc"
								[items]="negociacao?.diasCartao"
								idKey="codigo"
								label="Vencimento das parcelas no dia"
								labelKey="descricao"></pacto-cat-select>
						</div>

						<div class="select col-sm-12 col-md-6 col-lg-6 col-xl-6 btns-right">
							<pacto-cat-button
								(click)="autorizacaoCobranca()"
								[icon]="'pct pct-plus'"
								i18n-label="@@label-autorizacai-cobranca"
								id="btn-autorizacao-cobranca"
								label="Autorização de cobrança"
								size="LARGE"
								type="OUTLINE_NO_BORDER"></pacto-cat-button>

							<adm-botao-dropdown-opcoes
								[conteudo]="custom"
								[label]="'Mais'"></adm-botao-dropdown-opcoes>

							<ng-template #custom>
								<div (click)="abrirOpcoesAvancadas()" class="opt-dropdown">
									<span>Opções avançadas</span>
								</div>
								<div
									(click)="abrirEditarParcelas()"
									*ngIf="temPermissaoEditarParcelas"
									class="opt-dropdown">
									<span>Editar parcelas</span>
								</div>
								<div (click)="abrirObservacao()" class="opt-dropdown">
									<span>Adicionar observação</span>
								</div>
								<div
									(click)="arredondarValores()"
									*ngIf="usarArredondamento"
									class="opt-dropdown">
									<span>Arredondar valores</span>
								</div>
							</ng-template>
						</div>
					</div>

					<div *ngIf="cadastrouCartao" class="toast">
						<i class="pct pct-check"></i>
						<span *ngIf="!autorizacaoCobrancaCliente">
							Convênio de cobrança cadastrado com sucesso
						</span>
						<span *ngIf="autorizacaoCobrancaCliente">
							Aluno possui autorização de
							{{
								autorizacaoCobrancaCliente.tipoAutorizacaoCobranca +
									"" +
									(autorizacaoCobrancaCliente.nrsCartao
										? ", cartão com final " +
										  autorizacaoCobrancaCliente.nrsCartao +
										  " e validade " +
										  autorizacaoCobrancaCliente.validadeCartao
										: "")
							}}.
						</span>
					</div>

					<div
						*ngIf="negociacao && negociacao.isentarRematricula"
						class="toast">
						<i class="pct pct-check"></i>
						<span>
							Aluno isento de rematrícula pois o período configurado para a
							cobrança ainda não foi atingido.
						</span>
					</div>

					<adm-parcelas-negociacao
						#parcelas
						*ngIf="bolsa === false"></adm-parcelas-negociacao>

					<div
						*ngIf="simulado?.grupo && simulado?.grupo?.descricao"
						class="toast warning">
						<i class="pct pct-alert-triangle"></i>
						<span>
							O cliente se encontra na categoria {{ simulado.grupo.descricao }},
							onde é incluído um desconto de
							{{ simulado.grupo.valor }} automaticamente.
						</span>
					</div>

					<div
						*ngIf="simulado?.valorProRata && simulado?.valorProRata > 0.0"
						class="toast warning">
						<i class="pct pct-alert-triangle"></i>
						<span>
							A data de vencimento escolhida gerou um valor pró-rata pelos dias
							de uso até a primeira parcela.
						</span>
					</div>
					<div #fluxo>
						<adm-info-negociacao
							[linha]="linha"
							(alteracaoCliente)="abrirEscolherCliente(null)"
							(alteracaoData)="simular()"
							id="info-fluxo"></adm-info-negociacao>
					</div>

					<div class="botoes-negociacao">
						<div *ngIf="parcelasAberto > 0.0" class="aviso-parcelas">
							<i class="pct pct-alert-triangle"></i>
							<span>
								Parcelas em aberto. Valor total
								{{ parcelasAberto | currency : "R$" }}
							</span>
						</div>

						<div class="botoes-negociacao-row">
							<div class="botoes-negociacao-row-left">
								<button
									ds3-text-button
									*ngIf="questionarioCliente?.preencherQuestionario"
									(click)="preencherBv()">
									Preencher BV
								</button>
							</div>

							<div *ngIf="btnReceber && !bolsa" style="display: inline-block">
								<pacto-cat-button
									(click)="abrirAutorizacao('caixa', true)"
									[disabled]="blockButtons"
									i18n-label="@@label-caixa-aberto-btn"
									id="btn-caixa-aberto-assinar"
									label="Concluir e assinar"
									size="LARGE"
									type="OUTLINE"></pacto-cat-button>
							</div>

							<div
								*ngIf="!bolsa"
								[ds3Tooltip]="
									linkPagamentoDesabilitado
										? 'Empresa não possui convênio padrão para link de pagamento'
										: false
								"
								tooltipPosition="top"
								style="display: inline-block">
								<pacto-cat-button
									(click)="abrirAutorizacao('enviar')"
									[disabled]="blockButtons || linkPagamentoDesabilitado"
									i18n-label="@@label-enviar-link-btn"
									id="btn-enviar"
									label="Enviar link de pagamento"
									size="LARGE"
									type="OUTLINE"></pacto-cat-button>
							</div>

							<div *ngIf="btnReceber && !bolsa" style="display: inline-block">
								<pacto-cat-button
									(click)="abrirAutorizacao('caixa')"
									[disabled]="blockButtons"
									i18n-label="@@label-caixa-aberto-btn"
									id="btn-caixa-aberto"
									label="Deixar no Caixa em Aberto"
									size="LARGE"
									type="OUTLINE"></pacto-cat-button>
							</div>

							<div *ngIf="!btnReceber && !bolsa" style="display: inline-block">
								<pacto-cat-button
									(click)="abrirAutorizacao('caixa')"
									[disabled]="blockButtons"
									i18n-label="@@label-caixa-aberto-btn"
									id="btn-caixa-aberto-2"
									label="Deixar no Caixa em Aberto"
									size="LARGE"
									type="PRIMARY"></pacto-cat-button>
							</div>

							<div *ngIf="btnReceber && !bolsa" style="display: inline-block">
								<pacto-cat-button
									(click)="abrirAutorizacao('receber')"
									[disabled]="blockButtons"
									i18n-label="@@label-receber-btn"
									id="btn-receber"
									label="Receber"
									size="LARGE"
									type="PRIMARY"
									width="100px"></pacto-cat-button>
							</div>

							<pacto-cat-button
								(click)="abrirAutorizacao('receber')"
								*ngIf="bolsa"
								[disabled]="blockButtons"
								i18n-label="@@label-receber-btn"
								id="btn-concluir"
								label="Concluir"
								size="LARGE"
								type="PRIMARY"
								width="100px"></pacto-cat-button>
						</div>
					</div>
				</div>
			</pacto-cat-card-plain>
		</adm-layout>
	</div>
	<div #fixo class="fixed">
		<adm-info-negociacao
			[linha]="linha"
			(alteracaoCliente)="abrirEscolherCliente(null)"
			id="info-fixa"></adm-info-negociacao>
	</div>
</div>

<pacto-traducoes-xingling #infosTexto>
	<span i18n="@inf-contrato:espontaneo" xingling="ESPONTANEO">Espontâneo</span>
	<span i18n="@inf-contrato:agendado" xingling="AGENDADO">Agendado</span>
</pacto-traducoes-xingling>
