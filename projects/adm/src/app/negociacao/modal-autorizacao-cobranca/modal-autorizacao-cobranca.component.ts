import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "sdk";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import {
	ZwPactoPayApiDashService,
	AutorizacaoBoleto,
	AutorizacaoCartao,
	AutorizacaoDebito,
	TipoConvenioEnum,
} from "zw-pactopay-api";
import {
	MsPactoPayApiCobrancaService,
	Autorizacao,
	MsPactopayApiConvenioCobrancaService,
} from "ms-pactopay-api";
import {
	BUTTON_SIZE,
	BUTTON_TYPE,
	PactoModalRef,
	PactoModalSize,
	DialogService,
} from "ui-kit";
import { PactoDualMultiSelectConfig } from "../../shared/dual-multi-select/pacto-dual-multi-select.model";
import { ModalNotificacaoComponent } from "./modal-notificacao/modal-notificacao.component";
import { ModalProdutosEspecificosComponent } from "./modal-produtos-especificos/modal-produtos-especificos.component";
import { ModalService } from "@base-core/modal/modal.service";

@Component({
	selector: "adm-modal-autorizacao-cobranca",
	templateUrl: "./modal-autorizacao-cobranca.component.html",
	styleUrls: ["./modal-autorizacao-cobranca.component.scss"],
})
export class ModalAutorizacaoCobrancaComponent implements OnInit {
	@Input()
	public cobranca: Autorizacao;

	@Input() idVindi: number;
	@Input() possuiIdVindi: boolean;

	@Input()
	public pessoa: number;

	@Input()
	public editar = false;

	@Output()
	public sendModificacao: EventEmitter<any> = new EventEmitter();

	public listaProdutosSelecionados = [];

	public parcelas = [];

	public convenios = [];

	public formulario: FormGroup;

	public tiposDeAutorizacao: any = {
		cartaoCredito: false,
		boleto: false,
		debitoConta: false,
	};
	tipoString: string;
	tiposId: Array<{ codigo: string; descricao: string }> = new Array<{
		codigo: string;
		descricao: string;
	}>();

	constructor(
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService,
		private readonly msPactoPayApiCobranca: MsPactoPayApiCobrancaService,
		private readonly msPactopayApiConvenioCobranca: MsPactopayApiConvenioCobrancaService,
		private readonly session: SessionService,
		private readonly cd: ChangeDetectorRef,
		private readonly modalService: ModalService,
		private readonly openModal: NgbActiveModal,
		private readonly notify: SnotifyService,
		private readonly formBuilder: FormBuilder
	) {}

	ngOnInit() {
		this.formulario = this.formBuilder.group({
			convenio: this.formBuilder.control(""),
			tipo: this.formBuilder.control(""),
			tipoCobrar: this.formBuilder.control(1),
			titular: this.formBuilder.control(""),
			documento: this.formBuilder.control(""),
			card: this.formBuilder.control(""),
			cvv: this.formBuilder.control(""),
			vencimento: this.formBuilder.control(""),
			clienteTitular: this.formBuilder.control(""),
			agencia: this.formBuilder.control(""),
			contacorrente: this.formBuilder.control(""),
			autorizarclientedebito: this.formBuilder.control(""),
			bandeira: this.formBuilder.control(""),
			idVindi: this.formBuilder.control(null),
			tipoId: this.formBuilder.control(null),
		});

		this.formulario.get("idVindi").disable();
		this._populateTiposId();

		this.loadForm();
		this.obterTiposDeAutorizacao();
	}

	private _populateTiposId() {
		this.tiposId.push({
			codigo: "idVindi",
			descricao: "IdVindi",
		});
	}

	private loadForm() {
		if (this.editar) {
			this.formulario.patchValue(this.cobranca);
			this.escolherTipoDeAutorizacao(this.cobranca.tipo);
			this.aplicarMascaraPeloTipo(this.cobranca.tipo);
		}

		if (this.possuiIdVindi) {
			this.formulario.get("idVindi").setValue(this.idVindi);
		}

		this.msPactoPayApiCobranca.getAllParcelasCobrar().subscribe((parcelas) => {
			this.parcelas = parcelas;
			this.cd.detectChanges();
		});
	}

	public salvar() {
		this.formulario.markAllAsTouched();
		if (this.formulario.valid) {
			switch (this.formulario.get("tipo").value) {
				case 1: // cartão
					this.salvarCartao(this.formulario.value, true);
					break;
				case 2: // débito
					this.salvarDebito(this.formulario.value);
					break;
				case 3: // boleto
					this.salvarBoleto(this.formulario.value);
					break;
			}
		}
	}

	private salvarCartao(formulario: AutorizacaoCartao, verificar: boolean) {
		if (
			formulario.tipoCobrar === 3 &&
			this.listaProdutosSelecionados.length === 0
		) {
			this.notify.error(
				"Você não selecionou os produtos específicados. Por favor, refaça a autorização ou selecione outro tipo de parcela."
			);
		} else {
			if (this.editar) {
				formulario.codigo = this.cobranca.codigo;
			}
			formulario.pessoa = this.pessoa;
			formulario.verificar = verificar;
			formulario.produtos = this.listaProdutosSelecionados.map(
				(produto) => produto.codigo
			);
			this.sendAutorizacao(formulario);
		}
	}

	private salvarDebito(formulario: AutorizacaoDebito) {
		if (this.editar) {
			formulario.codigo = this.cobranca.codigo;
		}
		formulario.pessoa = this.pessoa;
		this.sendAutorizacao(formulario);
	}

	private salvarBoleto(formulario: AutorizacaoBoleto) {
		if (this.editar) {
			formulario.codigo = this.cobranca.codigo;
		}
		formulario.pessoa = this.pessoa;
		this.sendAutorizacao(formulario);
	}

	private sendAutorizacao(
		formulario: AutorizacaoCartao | AutorizacaoDebito | AutorizacaoBoleto
	) {
		formulario.usuario = this.session.loggedUser.id;
		formulario.username = this.session.loggedUser.username;
		if (this.tipoString === "idVindi") {
			formulario.usarIdVindi = true;
		}
		this.zwPactoPayApiDash.sendAutorizacao(formulario).subscribe((res) => {
			const modal: PactoModalRef = this.modalService.open(
				"Informativo",
				ModalNotificacaoComponent,
				PactoModalSize.MEDIUM
			);
			if (res.error) {
				modal.componentInstance.type = "pct-alert-triangle";
				modal.componentInstance.title = "Atenção!";
				modal.componentInstance.subtitle =
					res.message ||
					"As alterações feitas não foram salvas. Tente novamente.";
				modal.componentInstance.actions = [
					{
						clickHandler() {
							modal.close(true);
						},
						type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
						size: BUTTON_SIZE.LARGE,
						label: "Tentar novamente",
						full: true,
					},
				];
			} else {
				if (
					res.hasOwnProperty("apresentarConfirmar") &&
					res.hasOwnProperty("permiteAdicionar")
				) {
					this.tratarVerificacaoNegada(res, formulario, modal);
				} else {
					modal.componentInstance.type = "pct-check";
					modal.componentInstance.title = "Salvo com sucesso!";
					modal.componentInstance.subtitle = res.mensagem;
					modal.componentInstance.actions = [
						{
							clickHandler: () => {
								this.sendModificacao.emit({
									status: "Salvo com sucesso!",
									codigoConvenio: this.formulario.get("convenio").value,
								});
								modal.close(true);
								this.openModal.close("salvo");
							},
							type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
							size: BUTTON_SIZE.LARGE,
							label: "Ok",
							full: true,
						},
					];
				}
			}
		});
	}

	private tratarVerificacaoNegada(
		{ permiteAdicionar, mensagem },
		formulario: AutorizacaoCartao,
		modal: PactoModalRef
	) {
		modal.componentInstance.type = "pct-x-circle";
		modal.componentInstance.title = "Transação não autorizada!";
		modal.componentInstance.subtitle = mensagem;
		const actions = [];
		if (permiteAdicionar) {
			modal.componentInstance.body = "Deseja salvar o cartão mesmo assim?";
			actions.push(
				{
					clickHandler: () => {
						this.salvarCartao(formulario, false);
						modal.close(true);
					},
					type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
					size: BUTTON_SIZE.LARGE,
					label: "Salvar",
					full: true,
				},
				{
					clickHandler() {
						modal.close(true);
					},
					type: BUTTON_TYPE.OUTLINE_ACTION,
					size: BUTTON_SIZE.LARGE,
					label: "Cancelar",
					full: true,
				}
			);
		} else {
			modal.componentInstance.body =
				"Não será possível prosseguir, revise as informações do cartão e tente novamente.";
			actions.push({
				clickHandler() {
					modal.close(true);
				},
				type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
				size: BUTTON_SIZE.LARGE,
				label: "Ok",
				full: true,
			});
		}
		modal.componentInstance.actions = actions;
	}

	public cancelar() {
		this.ngOnInit();
		this.openModal.close("cancelar");
	}

	public confirmarExclusao() {
		const modal: PactoModalRef = this.modalService.open(
			"Informativo",
			ModalNotificacaoComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.type = "pct-alert-triangle";
		modal.componentInstance.title = "Atenção!";
		modal.componentInstance.subtitle =
			"Tem certeza que deseja excluir essa autorização?";
		modal.componentInstance.actions = [
			{
				clickHandler: this.excluir(modal),
				type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
				size: BUTTON_SIZE.LARGE,
				label: "Excluir",
				full: true,
			},
			{
				clickHandler() {
					modal.close(true);
				},
				type: BUTTON_TYPE.OUTLINE_ACTION,
				size: BUTTON_SIZE.LARGE,
				label: "Cancelar",
				full: true,
			},
		];
	}

	private excluir(modalInstance) {
		return () => {
			this.msPactoPayApiCobranca
				.excluirAutorizacao(this.cobranca.codigo)
				.subscribe(
					(result) => {
						modalInstance.close(true);
						const modal: PactoModalRef = this.modalService.open(
							"Informativo",
							ModalNotificacaoComponent,
							PactoModalSize.MEDIUM
						);
						if (result.error) {
							modal.componentInstance.type = "pct-alert-triangle";
							modal.componentInstance.title = "Atenção!";
							modal.componentInstance.subtitle =
								"Erro ao tentar excluir. Tente novamente.";
							modal.componentInstance.actions = [
								{
									clickHandler() {
										modal.close(true);
									},
									type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
									size: BUTTON_SIZE.LARGE,
									label: "Tentar novamente",
									full: true,
								},
								{
									clickHandler() {
										modal.close(true);
									},
									type: BUTTON_TYPE.OUTLINE_ACTION,
									size: BUTTON_SIZE.LARGE,
									label: "Cancelar",
									full: true,
								},
							];
						} else {
							modal.componentInstance.type = "pct-check";
							modal.componentInstance.title = "Excluído com sucesso!";
							modal.componentInstance.subtitle = result.content;
							modal.componentInstance.actions = [
								{
									clickHandler: () => {
										this.sendModificacao.emit({
											status: "Excluído com sucesso!",
										});
										modal.close(true);
										this.openModal.close(true);
									},
									type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
									size: BUTTON_SIZE.LARGE,
									label: "Ok",
									full: true,
								},
							];
						}
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						}
					}
				);
		};
	}

	public limparDados() {
		const modal: PactoModalRef = this.modalService.open(
			"Informativo",
			ModalNotificacaoComponent,
			PactoModalSize.SMALL
		);
		modal.componentInstance.type = "pct-alert-triangle";
		modal.componentInstance.title = "Atenção!";
		modal.componentInstance.subtitle =
			"Tem certeza que deseja limpar os dados da cobrança?";
		modal.componentInstance.actions = [
			{
				clickHandler: () => {
					this.formulario.reset();
					if (this.editar) {
						this.formulario.patchValue({
							tipo: this.cobranca.tipo,
						});
						this.configurarValidacoes(this.cobranca.tipo);
					}
					modal.close(true);
				},
				type: BUTTON_TYPE.SUCCESS,
				size: BUTTON_SIZE.LARGE,
				label: "Sim, limpar dados",
				full: true,
			},
			{
				clickHandler() {
					modal.close(true);
				},
				type: BUTTON_TYPE.OUTLINE_ACTION,
				size: BUTTON_SIZE.LARGE,
				label: "Cancelar",
				full: true,
			},
		];
	}

	public escolherTipoDeAutorizacao(tipo, tipoString?: string) {
		this.tipoString = tipoString;
		this.formulario.patchValue({ tipo });
		this.configurarValidacoes(tipo);
		this.zwPactoPayApiDash
			.getConvenioPorTipo(tipo, Number(this.session.empresaId))
			.subscribe((convenios) => {
				this.convenios = convenios;
				const optionTipoId = this.tiposId.find((v) => v.codigo === tipoString);
				if (optionTipoId) {
					this.formulario.get("tipoId").setValue(optionTipoId.codigo);
				}
				this._populateConveniosByTipoString();
				if (this.editar) {
					this.formulario.patchValue({
						convenio: this.convenios.find(
							(item) => item.descricao === this.cobranca.convenio
						).codigo,
					});
				} else if (convenios.length === 1) {
					this.formulario.patchValue({ convenio: convenios[0].codigo });
				}
				this.cd.detectChanges();
			});
	}

	private _populateConveniosByTipoString() {
		if (this.tipoString === "idVindi") {
			this.convenios = this.convenios.filter(
				(c) => c.tipoConvenioCodigo === TipoConvenioEnum.DCC_VINDI
			);
		}
	}

	public produtosEspecificosHandler() {
		if (this.formulario.get("tipoCobrar").value === "3") {
			this.formulario.get("tipoCobrar").markAsTouched();
			this.getProdutosEspecificos();
		}
	}

	private getProdutosEspecificos() {
		this.msPactoPayApiCobranca.getProdutosEspecificos().subscribe((data) => {
			const prodEspecif: PactoModalRef = this.modalService.open(
				"Escolha os produtos",
				ModalProdutosEspecificosComponent,
				PactoModalSize.LARGE
			);
			prodEspecif.componentInstance.dataConfig = new PactoDualMultiSelectConfig(
				{
					data,
					id: "codigo",
					value: "descricao",
				}
			);
			prodEspecif.result.then((result) => {
				this.listaProdutosSelecionados = result;
				this.cd.detectChanges();
			});
		});
	}

	public editarProdutos() {
		this.msPactoPayApiCobranca.getProdutosEspecificos().subscribe((data) => {
			const editarProdEspecif: PactoModalRef = this.modalService.open(
				"Escolha os produtos",
				ModalProdutosEspecificosComponent,
				PactoModalSize.LARGE
			);

			editarProdEspecif.componentInstance.dataConfig =
				new PactoDualMultiSelectConfig({
					data,
					id: "codigo",
					value: "descricao",
					selectedData: this.listaProdutosSelecionados,
				});

			editarProdEspecif.result.then((produtosAtualizados) => {
				if (produtosAtualizados) {
					this.listaProdutosSelecionados = produtosAtualizados;
					this.cd.detectChanges();
				}
			});
		});
	}

	public get formatoDoGrid() {
		switch (this.formulario.get("tipo").value) {
			case 1:
				if (this.tipoString && this.tipoString === "idVindi") {
					return "grid-id-vindi";
				}
				return "grid-credito";
			case 2:
				return "grid-debito";
			case 3:
				return "grid-boleto";
			default:
				return "grid-nenhum";
		}
	}

	public mascaraParaCartao() {
		let value = this.formulario.get("card").value;
		value = value.replace(/\s|[a-z]/g, ""); // remove todo espaço em branco
		value = value.slice(0, 16);
		if (value.length > 15) {
			value = value.replace(/(.{4})(.{4})(.{4})(\d{4})/, "$1 $2 $3 $4");
		} else if (value.length > 14) {
			value = value.replace(/(.{4})(.{6})(\d{5})/, "$1 $2 $3");
		} else {
			value = value.replace(/(.{4})(.{6})(\d{4})/, "$1 $2 $3");
		}
		this.formulario.get("card").setValue(value);
	}

	public mascaraParaNome() {
		const nomeCompleto = this.formulario
			.get("titular")
			.value.replace(/\d/g, "")
			.toLowerCase()
			.split(" ");
		const formatado = nomeCompleto
			.map((palavra) => palavra.charAt(0).toUpperCase() + palavra.substring(1))
			.join(" ");
		this.formulario.get("titular").setValue(formatado);
	}

	public mascaraParaDocumento() {
		let value = this.formulario.get("documento").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 14);
		if (value.length > 11) {
			value = value.replace(
				/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g,
				"$1.$2.$3/$4-$5"
			);
		} else {
			value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3-$4");
		}
		this.formulario.get("documento").setValue(value);
	}

	public mascaraParaVencimento() {
		let value = this.formulario.get("vencimento").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 6);
		value = value.replace(/(\d{2})(\d{2})?(\d{2})/, "$1/$3");
		this.formulario.get("vencimento").setValue(value);
	}

	public mascaraParaCVV() {
		let value = this.formulario.get("cvv").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 4);
		this.formulario.get("cvv").setValue(value);
	}

	public mascaraParaAgencia() {
		let value = this.formulario.get("agencia").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 6);
		value = value.replace(/(\d)(\d{2})$/g, "$1-$2");
		this.formulario.get("agencia").setValue(value);
	}

	public mascaraParaContaCorrente() {
		let value = this.formulario.get("contacorrente").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 16);
		value = value.replace(/(\d)(\d{2})$/g, "$1-$2");
		this.formulario.get("contacorrente").setValue(value);
	}

	private configurarValidacoes(tipo) {
		this.formulario.clearValidators();
		switch (tipo) {
			case 1: // cartão
				this.formulario.get("convenio").setValidators([Validators.required]);
				this.formulario.get("tipoCobrar").setValidators([Validators.required]);
				if (!this.tipoString) {
					this.formulario
						.get("titular")
						.setValidators([Validators.required, Validators.minLength(3)]);
					this.formulario
						.get("documento")
						.setValidators([Validators.minLength(11)]);
					this.formulario
						.get("card")
						.setValidators([Validators.required, Validators.minLength(16)]);
					this.formulario
						.get("cvv")
						.setValidators([Validators.minLength(3), Validators.maxLength(4)]);
					this.formulario
						.get("vencimento")
						.setValidators([Validators.required, Validators.minLength(4)]);
				}
				break;
			case 2: // débito
				this.formulario.get("convenio").setValidators([Validators.required]);
				this.formulario.get("tipoCobrar").setValidators([Validators.required]);
				this.formulario
					.get("titular")
					.setValidators([Validators.required, Validators.minLength(3)]);
				this.formulario
					.get("documento")
					.setValidators([Validators.minLength(11)]);
				this.formulario.get("agencia").setValidators([Validators.required]);
				this.formulario
					.get("contacorrente")
					.setValidators([Validators.required]);
				break;
			case 3: // boleto
				this.formulario.get("convenio").setValidators([Validators.required]);
				break;
		}
	}

	private aplicarMascaraPeloTipo(tipo) {
		switch (tipo) {
			case 1: // cartão
				this.mascaraParaNome();
				this.mascaraParaDocumento();
				this.mascaraParaCartao();
				this.mascaraParaVencimento();
				break;
			case 2: // débito
				this.mascaraParaNome();
				this.mascaraParaDocumento();
				this.mascaraParaAgencia();
				this.mascaraParaContaCorrente();
				break;
		}
	}

	public validarCartao(evt) {
		this.zwPactoPayApiDash.validarCartao(evt.target.value).subscribe((res) => {
			if (res.valido) {
				this.formulario.get("bandeira").setValue(res.bandeira);
				this.cd.detectChanges();
			} else {
				this.formulario.get("card").setValue("");
				this.notify.error(res.mensagem);
			}
		});
	}
	private obterTiposDeAutorizacao() {
		this.msPactopayApiConvenioCobranca
			.obterTiposDeAutorizacao(
				Number(this.session.empresaId),
				Number(0),
				Number(1)
			)
			.subscribe((res) => {
				this.tiposDeAutorizacao = res.content;
				this.cd.detectChanges();
			});
	}
}
