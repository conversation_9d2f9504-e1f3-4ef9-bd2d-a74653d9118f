<div class="form">
	<div class="row">
		<div *ngIf="editar" class="col-lg-4 titulo">
			Editar autorização de cobrança
		</div>
		<div *ngIf="!editar" class="col-lg-4 titulo">Nova forma de cobrança</div>
	</div>

	<div *ngIf="!editar" class="row tabs-container">
		<ng-container
			*ngIf="
				(tiposDeAutorizacao.hasOwnProperty('cartaoCredito') &&
					tiposDeAutorizacao['cartaoCredito']) ||
					(tiposDeAutorizacao.hasOwnProperty('debitoConta') &&
						tiposDeAutorizacao['debitoConta']) ||
					(tiposDeAutorizacao.hasOwnProperty('boleto') &&
						tiposDeAutorizacao['boleto']) ||
					(possuiIdVindi &&
						tiposDeAutorizacao.hasOwnProperty('id') &&
						tiposDeAutorizacao['id']);
				else emptyRadios
			">
			<span class="titulo-autorizacao">Tipo de autorização:</span>
			<ng-container
				*ngIf="
					tiposDeAutorizacao.hasOwnProperty('cartaoCredito') &&
					tiposDeAutorizacao['cartaoCredito']
				">
				<input
					type="radio"
					name="tipo"
					(click)="escolherTipoDeAutorizacao(1)"
					class="tabs"
					id="tab_credito" />
				<label for="tab_credito">
					<i class="pct pct-credit-card"></i>
					<span>Cartão de crédito</span>
				</label>
			</ng-container>
			<ng-container
				*ngIf="
					tiposDeAutorizacao.hasOwnProperty('debitoConta') &&
					tiposDeAutorizacao['debitoConta']
				">
				<input
					type="radio"
					name="tipo"
					(click)="escolherTipoDeAutorizacao(2)"
					class="tabs"
					id="tab_debito-em-conta" />
				<label for="tab_debito-em-conta">
					<i class="pct pct-dollar-sign"></i>
					<span>Débito em conta</span>
				</label>
			</ng-container>
			<ng-container
				*ngIf="
					tiposDeAutorizacao.hasOwnProperty('boleto') &&
					tiposDeAutorizacao['boleto']
				">
				<input
					type="radio"
					name="tipo"
					(click)="escolherTipoDeAutorizacao(3)"
					class="tabs"
					id="tab_boleto-bancario" />
				<label for="tab_boleto-bancario">
					<i class="pct pct-unpin"></i>
					<span>Boleto bancário</span>
				</label>
			</ng-container>
			<ng-container
				*ngIf="
					possuiIdVindi &&
					tiposDeAutorizacao.hasOwnProperty('id') &&
					tiposDeAutorizacao['id']
				">
				<input
					(click)="escolherTipoDeAutorizacao(1, 'idVindi')"
					class="tabs"
					id="tab_id"
					name="tipo"
					type="radio" />
				<label for="tab_id">
					<i class="pct pct-key"></i>
					<span>Id</span>
				</label>
			</ng-container>
		</ng-container>
	</div>
	<ng-template #emptyRadios>Não há autorizações configuradas</ng-template>

	<!-- ------------------------------------------------------------------------------------------------- -->

	<div class="row" *ngIf="formatoDoGrid !== 'grid-nenhum'">
		<div [class.col-4]="tipoString === 'idVindi'" [class.col-6]="!tipoString">
			<pacto-cat-form-select
				[id]="'parcelas-a-cobrar'"
				[label]="'Parcelas a cobrar'"
				[items]="parcelas"
				idKey="codigo"
				labelKey="descricao"
				(change)="produtosEspecificosHandler()"
				[errorMsg]="'Informe o tipo de parcelas'"
				[control]="formulario.get('tipoCobrar')"></pacto-cat-form-select>
		</div>
		<div [class.col-4]="tipoString === 'idVindi'" [class.col-6]="!tipoString">
			<pacto-cat-form-select
				[id]="'convenio-da-cobranca'"
				[label]="'Convênio de Cobrança'"
				[items]="convenios"
				idKey="codigo"
				labelKey="descricao"
				[errorMsg]="'Informe o convênio'"
				[control]="formulario.get('convenio')"></pacto-cat-form-select>
		</div>
		<div class="col-4" *ngIf="tipoString === 'idVindi'">
			<pacto-cat-form-select
				[disabled]="formulario.get('tipoId').disabled"
				[control]="formulario.get('tipoId')"
				[errorMsg]="'Informe o tipo do id'"
				[id]="'tipo-id'"
				[items]="tiposId"
				[label]="'Tipo de Id'"
				idKey="codigo"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
	</div>

	<div
		*ngIf="
			(formatoDoGrid === 'grid-credito' || formatoDoGrid === 'grid-debito') &&
			formulario.get('tipoCobrar').value === '3'
		"
		class="row produtos-especificados">
		<span class="typography-title-4">Produtos especificados</span>

		<div class="row produtos-especificados">
			<div class="produtos-lista">
				<span
					class="typography-body-2"
					*ngFor="let produto of listaProdutosSelecionados; let last = last">
					{{ produto.descricao }}
					<span *ngIf="!last">,</span>
				</span>
			</div>
			<button ds3-text-button (click)="editarProdutos()">Editar</button>
		</div>
	</div>

	<hr />

	<div
		[class]="formatoDoGrid"
		[style.width]="editar && tipoString !== 'idVindi' ? '80%' : '100%'">
		<pacto-cat-form-input
			*ngIf="!tipoString && formatoDoGrid === 'grid-credito'"
			class="celula-card"
			[id]="'numero-do-cartao'"
			[label]="'Número do cartão'"
			[placeholder]="'Digite o número do cartão'"
			(keyup)="mascaraParaCartao()"
			(focusout)="validarCartao($event)"
			[errorMsg]="'Informe o número do cartão'"
			[control]="formulario.get('card')"></pacto-cat-form-input>
		<div
			class="card celula-cartao"
			*ngIf="!tipoString && formatoDoGrid === 'grid-credito'">
			<div class="dados-card">
				<div class="nome-titular">
					{{
						formulario.value.titular
							? formulario.value.titular
							: "Nome do titular"
					}}
				</div>
				<div class="info">
					<span>
						{{
							formulario.value.card
								? formulario.value.card
								: "0000 0000 0000 0000"
						}}
					</span>
					<span>
						{{
							formulario.value.vencimento
								? formulario.value.vencimento
								: "00/00"
						}}
					</span>
				</div>
				<div class="bandeira">
					<img
						[src]="
							formulario.value.bandeira
								? 'assets/images/bandeiras/' +
								  formulario.value.bandeira +
								  '.png'
								: ''
						" />
				</div>
			</div>
			<img class="card-detail" [src]="'assets/images/cartao.png'" />
		</div>
		<pacto-cat-form-input
			*ngIf="
				!tipoString &&
				(formatoDoGrid === 'grid-credito' || formatoDoGrid === 'grid-debito')
			"
			class="celula-titular"
			[id]="'nome-do-titular'"
			[label]="'Nome do titular do cartão'"
			[placeholder]="'Digite o nome do titular do cartão'"
			(focusout)="mascaraParaNome()"
			[errorMsg]="'Informe o nome assim como está impresso no cartão'"
			[control]="formulario.get('titular')"></pacto-cat-form-input>
		<pacto-cat-form-input
			*ngIf="!tipoString && formatoDoGrid === 'grid-credito'"
			class="celula-vencimento"
			[id]="'validade'"
			[label]="'Validade'"
			[placeholder]="'MM/AA'"
			(keyup)="mascaraParaVencimento()"
			[errorMsg]="'Informe a validade'"
			[control]="formulario.get('vencimento')"></pacto-cat-form-input>
		<pacto-cat-form-input
			*ngIf="!tipoString && formatoDoGrid === 'grid-credito'"
			class="celula-cvv"
			[id]="'cvv'"
			[label]="'CVV'"
			[placeholder]="'***'"
			(keyup)="mascaraParaCVV()"
			[type]="'password'"
			[errorMsg]="'Informe o CVV'"
			[control]="formulario.get('cvv')"></pacto-cat-form-input>
		<pacto-cat-form-input
			*ngIf="
				!tipoString &&
				(formatoDoGrid === 'grid-credito' || formatoDoGrid === 'grid-debito')
			"
			class="celula-documento"
			[id]="'documento'"
			[label]="'CPF ou CNPJ do titular'"
			[placeholder]="'Forneça o CPF ou CNPJ do titular'"
			(keyup)="mascaraParaDocumento()"
			[errorMsg]="'Informe o CPF ou CNPJ do titular'"
			[control]="formulario.get('documento')"></pacto-cat-form-input>
		<pacto-cat-form-input
			*ngIf="!tipoString && formatoDoGrid === 'grid-debito'"
			class="celula-agencia"
			[id]="'n-da-agencia'"
			[label]="'N° da agência'"
			[placeholder]="'0000-00'"
			(keyup)="mascaraParaAgencia()"
			[errorMsg]="'Informe o N° da agência assim como está impresso no cartão'"
			[control]="formulario.get('agencia')"></pacto-cat-form-input>
		<pacto-cat-form-input
			*ngIf="!tipoString && formatoDoGrid === 'grid-debito'"
			class="celula-conta-corrente"
			[id]="'n-da-conta-corrente'"
			[label]="'N° da conta corrente'"
			[placeholder]="'00.000.000-00'"
			(keyup)="mascaraParaContaCorrente()"
			[errorMsg]="
				'Informe o N° da conta corrente assim como está impresso no cartão'
			"
			[control]="formulario.get('contacorrente')"></pacto-cat-form-input>
		<pacto-cat-form-input
			*ngIf="tipoString"
			[control]="formulario.get('idVindi')"
			[id]="'id-vindi'"
			label="IdVindi"></pacto-cat-form-input>
		<!-- <div *ngIf="formatoDoGrid === 'grid-credito' || formatoDoGrid === 'grid-debito'" class="clear celula-limpar"
            (click)="limparDados()">
            <i class="pct pct-trash-2"></i>
            <span class="text-limpar">Limpar dados</span>
        </div> -->
		<div class="form-group celula-flag">
			<div *ngIf="!tipoString && formatoDoGrid === 'grid-credito'">
				<pacto-cat-checkbox
					[control]="formulario.get('clienteTitular')"
					id="chk-titular"
					label="O cliente é o titular do cartão"></pacto-cat-checkbox>
			</div>
			<div *ngIf="!tipoString && formatoDoGrid === 'grid-debito'">
				<pacto-cat-checkbox
					[control]="formulario.get('autorizarclientedebito')"
					id="chk-autorizar-debito"
					label="Autorizar débito cliente no banco"></pacto-cat-checkbox>
			</div>
		</div>
	</div>
	<!-- ------------------------------------------------------------------------------------------------- -->
	<div class="row button-row">
		<div>
			<pacto-cat-button
				style="margin-right: 10px"
				[type]="'PRIMARY_NO_TEXT_TRANSFORM'"
				[label]="'Salvar'"
				id="btn-save"
				[size]="'LARGE'"
				(click)="salvar()"></pacto-cat-button>
			<pacto-cat-button
				style="margin-right: 10px"
				[type]="'OUTLINE_ACTION'"
				[label]="'Cancelar'"
				[size]="'LARGE'"
				id="btn-cancel"
				(click)="cancelar()"></pacto-cat-button>
		</div>
		<div>
			<pacto-cat-button
				*ngIf="editar"
				[type]="'ALERT'"
				[label]="'Excluir autorização'"
				[size]="'LARGE'"
				id="btn-delete"
				(click)="confirmarExclusao()"></pacto-cat-button>
		</div>
	</div>
</div>
