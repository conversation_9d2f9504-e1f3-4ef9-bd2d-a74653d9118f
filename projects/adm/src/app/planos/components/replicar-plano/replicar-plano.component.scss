.container-massage {
	padding-top: 20px;
	min-width: 200px;
}

.container-summary {
	padding-left: 30px;
}

.box-title {
	padding-bottom: 27px;
}

.summary-title {
	font-style: normal;
	font-weight: normal;
	font-size: 20px;
	line-height: 20px;
	color: #51555a;
}

.container-amount {
	display: flex;
}

.box-amount {
	display: flex;
	flex-direction: column;
	justify-content: space-evenly;
	align-items: center;
	width: 120px;
	height: 80px;
}

.number {
	font-weight: bold;
	font-size: 24px;
	line-height: 33px;
	color: #5f6369;
}

.number-title {
	font-style: normal;
	font-weight: normal;
	font-size: 14px;
	line-height: 12px;
	color: #6f747b;
}

.status {
	width: 124px;
	height: 24px;
	border-radius: 100px;
	color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-style: normal;
	font-weight: bold;
	font-size: 12px;
	line-height: 14px;
}

.vinculo {
	text-align: center;
	width: 146px;
	::ng-deep span.lbl.ng-star-inserted {
		width: 100px;
	}
}

.REPLICADA {
	background: #28ab45;
}

.REPLICANDA {
	background: #0380e3;
}

.NAO_REPLICADA {
	background: #ff4b2b;
}
