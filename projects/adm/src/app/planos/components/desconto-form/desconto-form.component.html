<adm-layout
	(goBack)="voltarListagem()"
	i18n-modulo="@@adm:moduleName"
	i18n-pageTitle="@@planos:desconto:mainTitle"
	i18n-subtitle="@@adm:planos:subtitle"
	modulo="Administrativo"
	pageTitle="Desconto"
	subtitle="Informe os dados abaixo">
	<pacto-cat-card-plain>
		<div class="table-wrapper pacto-shadow">
			<div class="row">
				<!-- CODIGO -->
				<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
					<pacto-cat-form-input-number
						[formControl]="codigoControl"
						i18n-label="@@planos:desconto:codigoLabel"
						id="plano-novo-desconto-codigo"
						label="Código"
						placeholder="000"
						readonly="true"></pacto-cat-form-input-number>
				</div>
				<!-- DESCRIÇÃO -->
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
					<pacto-cat-form-input
						[control]="form.get('descricao')"
						errorMsg="Insira uma descrição"
						i18n-errorMsg="@@planos:desconto:inputErrorMsg"
						i18n-label="@@planos:desconto:inputLabel"
						i18n-placeholder="@@planos:desconto:inputPlaceholder"
						id="plano-novo-desconto-descricao"
						label="Descrição*"
						placeholder="Descrição"></pacto-cat-form-input>
				</div>
				<!-- SELECT PRODUTO-->
				<div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5">
					<pacto-cat-form-select
						[control]="form.get('tipoProduto')"
						[items]="getTipoProdutoText"
						errorMsg="Selecione um tipo de produto"
						i18n-errorMsg="@@planos:desconto:selectErrorMsg"
						i18n-label="@@planos:desconto:selectLabel"
						i18n-placeholder="@@planos:desconto:selectPlaceholder"
						id="plano-novo-desconto-tipo-produto"
						label="Tipo de produto*"
						placeholder="Desconto - Renovação antecipada"></pacto-cat-form-select>
				</div>
			</div>

			<div class="row">
				<!-- CHECKBOX ATIVO-->
				<pacto-cat-checkbox
					[control]="form.get('ativo')"
					class="checkboxAtivo"
					i18n-label="@@planos:desconto:checkboxAtivoLabel"
					id="plano-novo-desconto-ativo"
					label="Ativo"></pacto-cat-checkbox>
				<!-- CHECKBOX EMPRESAS-->
				<pacto-cat-checkbox
					[control]="form.get('aplicarEmpresas')"
					class="checkboxEmpresas"
					i18n-label="@@planos:desconto:checkboxEmpresasLabel"
					id="plano-novo-desconto-aplicar-desconto"
					label="Selecionar empresas para aplicar o desconto"></pacto-cat-checkbox>
			</div>

			<!--DESCONTO RENOVAÇÃO-->
			<div
				*ngIf="
					form.get('tipoProduto').value !== null &&
					form.get('tipoProduto').value === 'DR'
				"
				class="row">
				<!-- TABELA -->
				<div class="tabelaRenov col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
					<div class="table-wrapper pacto-shadow">
						<pacto-cat-table-editable
							#tableRenovacaoComponent
							(confirm)="confirmDescontoRenovacao($event)"
							(delete)="deleteDescontoRenovacao($event)"
							(isEditingOrAddingItem)="
								isEditingOrAddingDescontoRenovacao($event)
							"
							[isEditable]="true"
							[showAddRow]="true"
							[showDelete]="true"
							[table]="tableRenov"
							idSuffix="plano-novo-desconto-renovacao"></pacto-cat-table-editable>
					</div>
				</div>
			</div>
		</div>

		<!-- != DESCONTO RENOVAÇÃO-->
		<div
			*ngIf="
				form.get('tipoProduto').value !== null &&
				form.get('tipoProduto').value !== 'DR'
			"
			class="row">
			<!-- TIPO DE DESCONTO -->
			<div class="tipoDesconto col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
				<pacto-cat-select
					[control]="form.get('tipoDesconto')"
					[items]="getTipoDescontoText"
					errorMsg="Selecione um tipo de desconto"
					i18n-errorMsg="@@planos:desconto:selectDescontoErrorMsg"
					i18n-label="@@planos:desconto:selectDescontoLabel"
					i18n-placeholder="@@planos:desconto:selectDescontoPlaceholder"
					id="plano-novo-desconto-tipo-desconto"
					label="Tipo de desconto*"
					placeholder="Valor"></pacto-cat-select>
			</div>
			<!-- VALOR -->
			<div class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2">
				<pacto-cat-form-input-number
					[decimalPrecision]="form.get('tipoDesconto').value === 'PE' ? 3 : 2"
					[formControl]="form.get('valor')"
					[label]="
						form.get('tipoDesconto').value === 'PE'
							? traducaoDesconto.getLabel('PE')
							: traducaoDesconto.getLabel('VA')
					"
					[max]="form.get('tipoDesconto').value === 'PE' ? 100 : undefined"
					decimal="true"
					id="plano-novo-desconto-tipo-desconto-valor"
					placeholder="000,00"></pacto-cat-form-input-number>
			</div>
		</div>

		<!-- APLICAR EMPRESAS -->
		<div *ngIf="form.get('aplicarEmpresas').value">
			<div class="empresasDesconto">
				<h6 i18n="@@planos:desconto:h6Label">
					Empresas onde se aplicam o desconto
				</h6>
			</div>
			<!-- TABELA -->
			<div class="table-wrapper pacto-shadow">
				<pacto-cat-table-editable
					#tableComponent
					(confirm)="confirm($event)"
					(edit)="edit($event)"
					(isEditingOrAddingItem)="isEditingOrAddingDescontoRenovacao($event)"
					[isEditable]="true"
					[showAddRow]="false"
					[showDelete]="false"
					[table]="table"
					idSuffix="plano-novo-desconto-empresas"></pacto-cat-table-editable>
			</div>
		</div>

		<!--BOTÕES-->
		<div class="row justify-content-end">
			<pacto-cat-button
				(click)="voltarListagem()"
				i18n-label="@@planos:cancelBttn"
				id="plano-novo-desconto-btn-cancelar"
				label="Cancelar"
				style="margin-right: 10px"
				type="OUTLINE_DARK"></pacto-cat-button>
			<pacto-cat-button
				(click)="salvarDesconto()"
				[disabled]="isEdittingTableRenovacao"
				i18n-label="@@planos:saveBttn"
				id="plano-novo-desconto-btn-salvar"
				label="Salvar"
				style="margin-right: 10px"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</pacto-cat-card-plain>
</adm-layout>

<!--TRADUÇÕES GERAIS-->
<pacto-traducoes-xingling #traducoes>
	<span i18n="@@planos:desconto:saveMsg" xingling="SAVE_MSG">
		Desconto cadastrado com sucesso!
	</span>
	<span i18n="@@planos:desconto:simValue" xingling="YES_VALUE">Sim</span>
	<span i18n="@@planos:desconto:naoValue" xingling="NO_VALUE">Não</span>
	<span i18n="@@planos:desconto:empresaTitle" xingling="EMPRESA_LABEL">
		Empresa
	</span>
	<span i18n="@@planos:desconto:estadoTitle" xingling="ESTADO_LABEL">
		Estado
	</span>
	<span i18n="@@planos:desconto:cidadeTitle" xingling="CIDADE_LABEL">
		Cidade
	</span>
	<span i18n="@@planos:desconto:bairroTitle" xingling="BAIRRO_LABEL">
		Bairro
	</span>
	<span i18n="@@planos:desconto:simValue" xingling="TIPO_INTERVALO">
		Tipo de Intervalo:
	</span>
	<span i18n="@@planos:desconto:naoValue" xingling="INTERVALO_DE">
		Intervalo de:
	</span>
	<span i18n="@@planos:desconto:empresaTitle" xingling="INTERVALO_ATE">
		Intervalo até:
	</span>
	<span i18n="@@planos:desconto:estadoTitle" xingling="TIPO_DESCONTO">
		Tipo de desconto:
	</span>
	<span i18n="@@planos:desconto:bairroTitle" xingling="JUSTIFICATIVA_BONUS">
		Justificativa do bônus:
	</span>
	<span i18n="@@planos:desconto:tabelaDado" xingling="DADO_TABELA">
		O mesmo dado já existe na tabela
	</span>
	<span i18n="@@planos:desconto:diasLabel" xingling="DIAS">Dias</span>
	<span i18n="@@planos:desconto:validJustOp" xingling="VALIDA_JUSTOP">
		Informe uma justificativa de bônus
	</span>
</pacto-traducoes-xingling>

<!--TRADUÇÃO TIPO PRODUTO-->
<pacto-traducoes-xingling #traducao>
	<span i18n="@@desconto:matricula" xingling="NA">
		Selecione um tipo de produto
	</span>

	<span i18n="@@desconto:geralva" xingling="VG">
		Desconto geral para venda avulsa
	</span>
	<span i18n="@@desconto:matricula" xingling="MA">Matrícula</span>
	<span i18n="@@desconto:rematricula" xingling="RE">Rematrícula</span>
	<span i18n="@@desconto:renovacao" xingling="RN">Renovação</span>
	<span i18n="@@desconto:produtoEstoque" xingling="PE">Produto estoque</span>
	<span i18n="@@desconto:bioTotem" xingling="BT">Bio totem</span>
	<span i18n="@@desconto:mesRefPlano" xingling="PM">
		Mês de referência plano
	</span>
	<span i18n="@@desconto:servico" xingling="SE">Serviço</span>
	<span i18n="@@desconto:convenioDesconto" xingling="CD">
		Convênio de desconto
	</span>
	<span i18n="@@desconto:descontoDE" xingling="DE">Desconto</span>
	<span i18n="@@desconto:devolucao" xingling="DV">Devolução</span>
	<span i18n="@@desconto:trancamento" xingling="TR">Trancamento</span>
	<span i18n="@@desconto:retornoTrancamento" xingling="RT">
		Retorno trancamento
	</span>
	<span i18n="@@desconto:aulaAvulsa" xingling="AA">Aula avulsa</span>
	<span i18n="@@desconto:diaria" xingling="DI">Diária</span>
	<span i18n="@@desconto:freepass" xingling="FR">Freepass</span>
	<span i18n="@@desconto:alterarHorario" xingling="AH">Alterar - Horário</span>
	<span i18n="@@desconto:manutModal" xingling="MM">Manutenção modalidade</span>
	<span i18n="@@desconto:manutCC" xingling="MC">Manutenção conta corrente</span>
	<span i18n="@@desconto:descontoRenovAnt" xingling="DR">
		Desconto em renovação antecipada
	</span>
	<span i18n="@@desconto:taxaPersonal" xingling="TP">Taxa de personal</span>
	<span i18n="@@desconto:sessao" xingling="SS">Sessão</span>
	<span i18n="@@desconto:devolucaoCredito" xingling="DC">
		Devolução de crédito da conta corrente do cliente
	</span>
	<span i18n="@@desconto:atestado" xingling="AT">Atestado</span>
	<span i18n="@@desconto:taxaAdesao" xingling="TD">
		Taxa de adesão plano recorrência
	</span>
	<span i18n="@@desconto:taxaReneg" xingling="TN">Taxa de renegociação</span>
	<span i18n="@@desconto:creditoPersonal" xingling="CP">
		Crédito de personal
	</span>
	<span i18n="@@desconto:taxaAnuidade" xingling="TA">
		Taxa de anuidade plano recorrência
	</span>
	<span i18n="@@desconto:devReceb" xingling="RD">Devolução de recebíveis</span>
	<span i18n="@@desconto:depositoCCAl" xingling="CC">
		Depósito conta corrente do aluno
	</span>
	<span i18n="@@desconto:acertoCC" xingling="AC">
		Acerto conta corrente do aluno
	</span>
	<span i18n="@@desconto:quitDinh" xingling="QU">
		Quitação de dinheiro - Cancelamento
	</span>
	<span i18n="@@desconto:armario" xingling="AR">Armário</span>
	<span i18n="@@desconto:multaJuros" xingling="MJ">Multa e juros</span>
	<span i18n="@@desconto:chequesDev" xingling="CH">Cheques devolvidos</span>
	<span i18n="@@desconto:desafio" xingling="DS">Desafio</span>
	<span i18n="@@desconto:appHomeFit" xingling="HM">App Home Fit</span>
</pacto-traducoes-xingling>

<!--TRADUÇÃO TIPO INTERVALO-->
<pacto-traducoes-xingling #traducaoIntervalo>
	<span i18n="@@desconto:tipoIntervaloAT" xingling="AT">Atrasado</span>
	<span i18n="@@desconto:tipoIntervaloAN" xingling="AN">Antecipado</span>
</pacto-traducoes-xingling>

<!--TRADUÇÃO TIPO DESCONTO-->
<pacto-traducoes-xingling #traducaoDesconto>
	<span i18n="@@desconto:tipoDescontoPE" xingling="PE">Percentual</span>
	<span i18n="@@desconto:tipoDescontoVA" xingling="VA">Valor</span>
	<span i18n="@@desconto:tipoDescontoBO" xingling="BO">Bônus</span>
</pacto-traducoes-xingling>
