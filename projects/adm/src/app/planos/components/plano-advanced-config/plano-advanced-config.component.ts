import { Component, Input, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { BUTTON_TYPE, SelectFilterParamBuilder } from "ui-kit";
import { Plano, TipoPlano } from "../../plano.model";
import { PlanoStateService } from "../cadastrar-plano/plano-state.service";

@Component({
	selector: "adm-plano-advanced-config",
	templateUrl: "./plano-advanced-config.component.html",
	styleUrls: ["./plano-advanced-config.component.scss"],
})
export class PlanoAdvancedConfigComponent implements OnInit {
	@Input() plano = new Plano();
	@Input() configRecorrenciaDadosContratuais: boolean;
	@Input() mostrarAbaFerias: boolean;

	formNormal: FormGroup;
	formRecorrenciaPersonal: FormGroup;
	formRecorrenciaDadosContratuais: FormGroup;
	buttonType = BUTTON_TYPE;
	parcelas: Array<{ id: number; label: string }> = new Array<{
		id: number;
		label: string;
	}>();
	mask = { mask: [/[0-3]/, /[0-9]?/], guide: false };

	produtoSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	constructor(
		public dialog: NgbActiveModal,
		public planoState: PlanoStateService,
		public notificationService: SnotifyService
	) {}

	ngOnInit() {
		if (
			this.plano.tipoPlano === TipoPlano.PLANO_NORMAL ||
			this.plano.tipoPlano === TipoPlano.PLANO_AVANCADO
		) {
			this.formNormal = new FormGroup({
				termoAceite: new FormControl(""),
				parcelamentoOperadora: new FormControl(),
				parcelamentoOperadoraDuracao: new FormControl(),
				maximoVezesParcelar: new FormControl(),
				site: new FormControl(""),
				permitirCompartilharPLanoNoSite: new FormControl(false),
				permitirVendaPlanoSiteNoBalcao: new FormControl(false),
				inicioMinimoContrato: new FormControl(""),
				bolsa: new FormControl(true),
				convidadosPorMes: new FormControl(),
				quantidadeMaximaFrequencia: new FormControl(""),
				diasVencimentoProrata: new FormControl(""),
				prorataObrigatorio: new FormControl(""),
				obrigatorioInformarCartaoCreditoVenda: new FormControl(false),
				renovavelAutomaticamente: new FormControl(""),
				renovarAutomaticamenteComDesconto: new FormControl(""),
				renovarProdutoObrigatorio: new FormControl(""),
				renovarAutomaticamenteUtilizandoValorBaseContrato: new FormControl(""),
				naoRenovarContratoParcelaVencidaAberto: new FormControl(""),
				renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia:
					new FormControl(""),
				permitePagarComBoleto: new FormControl(""),
				diaDoMesDescontoBoletoPagAntecipado: new FormControl(""),
				porcentagemDescontoBoletoPagAntecipado: new FormControl(""),
				aceitaDescontoPorPlano: new FormControl(false),
				cobrarAdesaoSeparada: new FormControl(""),
				nrVezesParcelarAdesao: new FormControl(""),
				cobrarProdutoSeparado: new FormControl(""),
				nrVezesParcelarProduto: new FormControl(""),
				descontoAntecipado: new FormControl(""),
				comissao: new FormControl(false),
				aceitaDescontoExtra: new FormControl(true),
				permitirAcessoRedeEmpresa: new FormControl(),
				permitirTurmasVendasOnline: new FormControl(false),
				videoSiteUrl: new FormControl(),
				observacaoSite: new FormControl(),
				apresentarPactoFlow: new FormControl(false),
				planoDiferenteRenovacao: new FormControl(),
				modalidadesPlanoDiferenteRenovacao: new FormControl(),
				horarioPlanoDiferenteRenovacao: new FormControl(),
				duracaoPlanoDiferenteRenovacao: new FormControl(),
				condicaoPagPlanoDiferenteRenovacao: new FormControl(),
				bloquearRecompra: new FormControl(false),
				observacao1: new FormControl(""),
				observacao2: new FormControl(""),
				contratosEncerramDia: new FormControl(null),
			});
			this.formNormal.get("site").valueChanges.subscribe((value) => {
				if (value) {
					this.formNormal.get("termoAceite").setValidators(Validators.required);
					this.formNormal.get("termoAceite").updateValueAndValidity();
				} else {
					this.formNormal.get("termoAceite").clearValidators();
					this.formNormal.get("termoAceite").updateValueAndValidity();
				}
			});
		} else if (
			this.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA ||
			this.plano.tipoPlano === TipoPlano.PLANO_CREDITO
		) {
			if (!this.configRecorrenciaDadosContratuais) {
				this.formRecorrenciaPersonal = new FormGroup({
					quantidadeMaximaFrequencia: new FormControl(""),
					convidadosPorMes: new FormControl(""),
					site: new FormControl(false),
					permitirCompartilharPLanoNoSite: new FormControl(false),
					permitirVendaPlanoSiteNoBalcao: new FormControl(false),
					apresentarVendaRapida: new FormControl(false),
					bolsa: new FormControl(false),
					comissao: new FormControl(false),
					bloquearRecompra: new FormControl(false),
					contratosEncerramDia: new FormControl(null),
					gerarValorCreditoExtra: new FormControl(""),
					produtoCreditoExtra: new FormControl(""),
					valorCreditoExtra: new FormControl(""),
				});
			} else {
				this.formRecorrenciaDadosContratuais = new FormGroup({
					descontoAntecipado: new FormControl(),
					gerarParcelasValorDiferente: new FormControl(),
					anuidadeNaParcela: new FormControl(),
					valorAnuidade: new FormControl(),
					diaAnuidade: new FormControl(),
					mesAnuidade: new FormControl(),
					diasVencimentoProrata: new FormControl(),
					prorataObrigatorio: new FormControl(),
					obrigatorioInformarCartaoCreditoVenda: new FormControl(false),
					renovavelAutomaticamente: new FormControl(),
					renovarAutomaticamenteComDesconto: new FormControl(),
					renovarProdutoObrigatorio: new FormControl(),
					renovarAutomaticamenteUtilizandoValorBaseContrato: new FormControl(),
					naoRenovarParcelaVencida: new FormControl(),
					parcelamentoOperadora: new FormControl(),
					parcelamentoOperadoraDuracao: new FormControl(),
					maximoVezesParcelar: new FormControl(),
					cobrarAdesaoSeparada: new FormControl(),
					nrVezesParcelarAdesao: new FormControl(),
					cobrarProdutoSeparado: new FormControl(),
					nrVezesParcelarProduto: new FormControl(),
					cancelamentoProporcional: new FormControl(),
					qtdDiasCobrarProximaParcela: new FormControl(),
					qtdDiasCobrarAnuidadeTotal: new FormControl(),
					bloquearRecompra: new FormControl(false),
					contratosEncerramDia: new FormControl(null),
				});
			}
		} else if (this.plano.tipoPlano === TipoPlano.PLANO_PERSONAL) {
			if (!this.configRecorrenciaDadosContratuais) {
				this.formRecorrenciaPersonal = new FormGroup({
					quantidadeMaximaFrequencia: new FormControl(""),
					site: new FormControl(false),
					permitirCompartilharPLanoNoSite: new FormControl(false),
					permitirVendaPlanoSiteNoBalcao: new FormControl(false),
					apresentarVendaRapida: new FormControl(false),
					bolsa: new FormControl(false),
					comissao: new FormControl(false),
					bloquearRecompra: new FormControl(false),
					contratosEncerramDia: new FormControl(null),
				});
			} else {
				this.formRecorrenciaDadosContratuais = new FormGroup({
					descontoAntecipado: new FormControl(),
					gerarParcelasValorDiferente: new FormControl(),
					anuidadeNaParcela: new FormControl(),
					valorAnuidade: new FormControl(),
					diaAnuidade: new FormControl(),
					mesAnuidade: new FormControl(),
					diasVencimentoProrata: new FormControl(),
					prorataObrigatorio: new FormControl(),
					renovavelAutomaticamente: new FormControl(),
					renovarAutomaticamenteComDesconto: new FormControl(),
					renovarProdutoObrigatorio: new FormControl(),
					renovarAutomaticamenteUtilizandoValorBaseContrato: new FormControl(),
					naoRenovarParcelaVencida: new FormControl(),
					parcelamentoOperadora: new FormControl(),
					parcelamentoOperadoraDuracao: new FormControl(),
					maximoVezesParcelar: new FormControl(),
					cobrarAdesaoSeparada: new FormControl(),
					nrVezesParcelarAdesao: new FormControl(),
					cancelamentoProporcional: new FormControl(),
					qtdDiasCobrarProximaParcela: new FormControl(),
					qtdDiasCobrarAnuidadeTotal: new FormControl(),
					bloquearRecompra: new FormControl(false),
					contratosEncerramDia: new FormControl(null),
				});
			}
		}
	}

	salvar() {
		if (!this.configRecorrenciaDadosContratuais) {
			const form = this.isPlanoNormalOuAvancado()
				? this.formNormal
				: this.formRecorrenciaPersonal;
			if (this.isPlanoNormalOuAvancado()) {
				const now = new Date();
				now.setHours(0, 0, 0, 0);
				if (
					(!this.plano.inicioMinimoContrato ||
						(this.plano.inicioMinimoContrato &&
							this.formNormal.get("inicioMinimoContrato").value !==
								this.plano.inicioMinimoContrato)) &&
					this.formNormal.get("inicioMinimoContrato").value !== "" &&
					this.formNormal.get("inicioMinimoContrato").value < now.getTime()
				) {
					this.notificationService.error(
						"Informe uma data não retroativa!",
						"Vendas online"
					);
					return;
				}
			}
			Object.keys(form.controls).forEach((controlKey) => {
				if (controlKey === "diasVencimentoProrata") {
					let diasVencimentoProrata: Array<{ label: string; id: number }> =
						this.formNormal.get("diasVencimentoProrata").value;
					let diasVencimentoProrataStr = "";
					diasVencimentoProrata = diasVencimentoProrata.sort((a, b) => {
						if (a.id < b.id) {
							return -1;
						} else if (a.id > b.id) {
							return 1;
						}
						return 0;
					});
					diasVencimentoProrata.forEach((dvp, index) => {
						diasVencimentoProrataStr += dvp.label;
						if (index + 1 < diasVencimentoProrata.length) {
							diasVencimentoProrataStr += ",";
						}
					});
					this.plano[controlKey] = diasVencimentoProrataStr;
				} else {
					this.plano[controlKey] = form.get(controlKey).value;
				}
			});
			if (
				form.get("planoDiferenteRenovacao").value &&
				form.get("planoDiferenteRenovacao").value.codigo
			) {
				if (
					this.plano.tipoPlano == "PLANO_NORMAL" &&
					!(
						form.get("planoDiferenteRenovacao").value.tipoPlano ==
						"PLANO_NORMAL"
					)
				) {
					this.notificationService.error(
						"O plano selecionado não é do mesmo tipo do plano atual: Normal"
					);
					return;
				}
				if (!form.get("horarioPlanoDiferenteRenovacao").value) {
					this.notificationService.error(
						"Campo obrigatório para renovação plano diferente: Horário"
					);
					return;
				} else if (!form.get("duracaoPlanoDiferenteRenovacao").value) {
					this.notificationService.error(
						"Campo obrigatório para renovação plano diferente: Duração"
					);
					return;
				} else if (!form.get("condicaoPagPlanoDiferenteRenovacao").value) {
					this.notificationService.error(
						"Campo obrigatório para renovação plano diferente: Condição de Pagamento"
					);
					return;
				} else if (
					!form.get("planoDiferenteRenovacao").value.regimeRecorrencia &&
					!form.get("modalidadesPlanoDiferenteRenovacao").value
				) {
					this.notificationService.error(
						"Campo obrigatório para renovação plano diferente: Modalidade"
					);
					return;
				} else {
					this.plano.planoDiferenteRenovacao = form.get(
						"planoDiferenteRenovacao"
					).value.codigo;
					this.plano.horarioPlanoDiferenteRenovacao = form.get(
						"horarioPlanoDiferenteRenovacao"
					).value;
					this.plano.duracaoPlanoDiferenteRenovacao = form.get(
						"duracaoPlanoDiferenteRenovacao"
					).value;
					this.plano.condicaoPagPlanoDiferenteRenovacao = form.get(
						"condicaoPagPlanoDiferenteRenovacao"
					).value;
					if (form.get("modalidadesPlanoDiferenteRenovacao").value) {
						this.plano.modalidadesPlanoDiferenteRenovacao = form
							.get("modalidadesPlanoDiferenteRenovacao")
							.value.map((item) => item.id.toString())
							.join(", ");
					}
				}
			} else {
				this.plano.planoDiferenteRenovacao = null;
				this.plano.horarioPlanoDiferenteRenovacao = null;
				this.plano.duracaoPlanoDiferenteRenovacao = null;
				this.plano.condicaoPagPlanoDiferenteRenovacao = null;
				this.plano.modalidadesPlanoDiferenteRenovacao = null;
				form.get("planoDiferenteRenovacao").setValue(null);
				form.get("modalidadesPlanoDiferenteRenovacao").setValue(null);
				form.get("horarioPlanoDiferenteRenovacao").setValue(null);
				form.get("duracaoPlanoDiferenteRenovacao").setValue(null);
				form.get("condicaoPagPlanoDiferenteRenovacao").setValue(null);
			}
		} else {
			this.plano.planoRecorrencia.gerarParcelasValorDiferente =
				this.formRecorrenciaDadosContratuais.get(
					"gerarParcelasValorDiferente"
				).value;
			this.plano.planoRecorrencia.anuidadeNaParcela =
				this.formRecorrenciaDadosContratuais.get("anuidadeNaParcela").value;
			this.plano.planoRecorrencia.valorAnuidade =
				this.formRecorrenciaDadosContratuais.get("valorAnuidade").value;
			this.plano.planoRecorrencia.diaAnuidade =
				+this.formRecorrenciaDadosContratuais.get("diaAnuidade").value;
			this.plano.planoRecorrencia.mesAnuidade =
				+this.formRecorrenciaDadosContratuais.get("mesAnuidade").value;
			let diasVencimentoProrata: Array<{ label: string; id: number }> =
				this.formRecorrenciaDadosContratuais.get("diasVencimentoProrata").value;
			let diasVencimentoProrataStr = "";
			diasVencimentoProrata = diasVencimentoProrata.sort((a, b) => {
				if (a.id < b.id) {
					return -1;
				} else if (a.id > b.id) {
					return 1;
				}
				return 0;
			});
			diasVencimentoProrata.forEach((dvp, index) => {
				diasVencimentoProrataStr += dvp.label;
				if (index + 1 < diasVencimentoProrata.length) {
					diasVencimentoProrataStr += ",";
				}
			});
			this.plano.diasVencimentoProrata = diasVencimentoProrataStr;
			this.plano.descontoAntecipado =
				this.formRecorrenciaDadosContratuais.get("descontoAntecipado").value;
			this.plano.prorataObrigatorio =
				this.formRecorrenciaDadosContratuais.get("prorataObrigatorio").value;
			this.plano.obrigatorioInformarCartaoCreditoVenda =
				this.formRecorrenciaDadosContratuais.get(
					"obrigatorioInformarCartaoCreditoVenda"
				).value;
			this.plano.planoRecorrencia.renovavelAutomaticamente =
				this.formRecorrenciaDadosContratuais.get(
					"renovavelAutomaticamente"
				).value;
			this.plano.renovarAutomaticamenteComDesconto =
				this.formRecorrenciaDadosContratuais.get(
					"renovarAutomaticamenteComDesconto"
				).value;
			this.plano.renovarProdutoObrigatorio =
				this.formRecorrenciaDadosContratuais.get(
					"renovarProdutoObrigatorio"
				).value;
			this.plano.renovarAutomaticamenteUtilizandoValorBaseContrato =
				this.formRecorrenciaDadosContratuais.get(
					"renovarAutomaticamenteUtilizandoValorBaseContrato"
				).value;
			this.plano.planoRecorrencia.naoRenovarParcelaVencida =
				this.formRecorrenciaDadosContratuais.get(
					"naoRenovarParcelaVencida"
				).value;
			this.plano.parcelamentoOperadora =
				this.formRecorrenciaDadosContratuais.get("parcelamentoOperadora").value;
			this.plano.parcelamentoOperadoraDuracao =
				this.formRecorrenciaDadosContratuais.get(
					"parcelamentoOperadoraDuracao"
				).value;
			this.plano.maximoVezesParcelar = this.formRecorrenciaDadosContratuais.get(
				"maximoVezesParcelar"
			).value;
			this.plano.cobrarAdesaoSeparada =
				this.formRecorrenciaDadosContratuais.get("cobrarAdesaoSeparada").value;
			this.plano.nrVezesParcelarAdesao =
				this.formRecorrenciaDadosContratuais.get("nrVezesParcelarAdesao").value;
			if (this.plano.tipoPlano === TipoPlano.PLANO_RECORRENCIA) {
				this.plano.cobrarProdutoSeparado =
					this.formRecorrenciaDadosContratuais.get(
						"cobrarProdutoSeparado"
					).value;
				this.plano.nrVezesParcelarProduto =
					this.formRecorrenciaDadosContratuais.get(
						"nrVezesParcelarProduto"
					).value;
			}
			this.plano.planoRecorrencia.cancelamentoProporcional =
				this.formRecorrenciaDadosContratuais.get(
					"cancelamentoProporcional"
				).value;
			this.plano.planoRecorrencia.qtdDiasCobrarProximaParcela =
				this.formRecorrenciaDadosContratuais.get(
					"qtdDiasCobrarProximaParcela"
				).value;
			this.plano.planoRecorrencia.qtdDiasCobrarAnuidadeTotal =
				this.formRecorrenciaDadosContratuais.get(
					"qtdDiasCobrarAnuidadeTotal"
				).value;
			this.plano.planoRecorrencia.parcelas =
				this.planoState.plano.planoRecorrencia.parcelas;
		}

		if (
			this.plano.permitirAcessoRedeEmpresa &&
			this.plano.acessoRedeEmpresasEspecificas &&
			(!this.plano.empresasRede || this.plano.empresasRede.length === 0)
		) {
			this.notificationService.error(
				"Nenhuma empresa foi especificada (Plano Vip)"
			);
			return;
		}
		this.dialog.close(this.plano);
	}

	isPlanoNormalOuAvancado(): boolean {
		return (
			this.plano.tipoPlano === TipoPlano.PLANO_NORMAL ||
			this.plano.tipoPlano === TipoPlano.PLANO_AVANCADO
		);
	}
}
