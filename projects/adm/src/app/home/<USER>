import { TefService } from "@adm/tef/tef.service";
import { HttpClient } from "@angular/common/http";
import {
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
	Optional,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { CampanhaService } from "marketing-api";
import { ModalObrigatoriosCallerService } from "pacto-layout";
import { Subject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { SessionService } from "sdk";
import { IpService } from "../services/ip.service";
import { TefComponent } from "../tef/tef.component";

enum TagsEnum {
	CAIXAS_PEQUENAS = "CAIXAS_PEQUENAS",
	CAIXAS_GRANDES = "CAIXAS_GRANDES",
	SLIDER = "SLIDER",
	SLIDER_LINK = "SLIDER_LINK",
	CONTEUDO_BLOG = "CONTEUDO_BLOG",
	IFRAME = "IFRAME",
}

@Component({
	selector: "adm-pacto-home",
	templateUrl: "./home.component.html",
	styleUrls: ["./home.component.scss"],
})
export class HomeComponent implements OnInit {
	itemsCampanha: Array<any> = new Array<any>();
	tagsEnum = TagsEnum;
	selectedItem = 0;
	_destroyedInterval$: Subject<null> = new Subject();

	constructor(
		private ipService: IpService,
		private httpClient: HttpClient,
		private campanhaService: CampanhaService,
		@Inject(LOCALE_ID) private locale,
		private sessionService: SessionService,
		private cd: ChangeDetectorRef,
		private modaisObrigatoriosService: ModalObrigatoriosCallerService,
		private dialog: MatDialog,
		private tefService: TefService
	) {}

	ngOnInit() {
		this.loadBanner();
		this.modaisObrigatoriosService.modaisObrigatorios();
	}

	// tslint:disable-next-line:use-lifecycle-interface
	ngAfterViewInit() {
		this.abrirModalVerificacaoPagamentoPendentesFiserv();
	}

	loadBanner() {
		this.ipService
			.getIp()
			.pipe(
				switchMap((ipNumber) => {
					return this.httpClient
						.get(`https://ipwho.is/${ipNumber}`, {
							params: { lang: "pt-BR" },
						})
						.pipe(
							switchMap((ipData) => {
								const currentEmpresa = this.sessionService.currentEmpresa;

								const chaveEmpresa = this.sessionService.chave;
								const redeEmpresa = this.getValueFromObject(
									"codigo",
									currentEmpresa
								);
								const loggedUser = this.getValueFromObject(
									"perfilUsuario",
									this.sessionService
								);
								const estado = this.getValueFromObject(
									"estado",
									currentEmpresa
								);
								const pais = this.getValueFromObject("pais", currentEmpresa);
								const idioma = this.getValueFromObject(
									"siglaNovaPlataforma",
									currentEmpresa
								);

								const campanhaparams = {
									redeEmpresa,
									chaveEmpresa: chaveEmpresa ? chaveEmpresa : null,
									siglaEstado: estado ? estado : ipData["region_code"],
									tipoPerfil: this.getValueFromObject("nome", loggedUser),
									nomePais: pais ? pais : ipData["country"],
									modulo: "NZW",
									linguagem: idioma
										? (idioma as string).toUpperCase()
										: this.locale,
									page: 0,
									size: 100,
									orderBy: "codigo",
									ativa: true,
								};

								return this.campanhaService.getCurrentCampanha(campanhaparams);
							})
						);
				})
			)
			.subscribe((data: any) => {
				if (data.result) {
					const campanhas = [];
					(data.result as Array<any>).forEach(
						(campanha: { itens: Array<any> }) => {
							campanha.itens.forEach((item) => {
								campanhas.push(item);
							});
						}
					);
					this.itemsCampanha = campanhas.filter(
						(campanha: any) =>
							campanha.tag === TagsEnum.SLIDER_LINK ||
							campanha.tag === TagsEnum.IFRAME
					);
				}
				if (this.itemsCampanha.length === 0) {
					this.itemsCampanha.unshift({
						tag: TagsEnum.SLIDER_LINK,
						urlImagem: "pacto-ui/images/banners/adm.png",
					});
				}
				this.cd.detectChanges();
			});
	}

	openLinkInNewTab(link: string) {
		window.open(link, "_blank");
	}

	getValueFromObject(fieldValue: string, obj: any): any {
		if (obj && obj[fieldValue]) {
			return obj[fieldValue];
		}

		return null;
	}

	abrirModalVerificacaoPagamentoPendentesFiserv(): void {
		// A pedido da Fiserv, só verifica uma vez por sessão. Se já verificou, não precisa seguir o fluxo
		const jaVerificou = sessionStorage.getItem(
			"usuarioLogadoJaVerificouPagamentosPendentesFiserv"
		);
		if (jaVerificou === "true") {
			return;
		}

		sessionStorage.setItem(
			"usuarioLogadoJaVerificouPagamentosPendentesFiserv",
			"true"
		);

		const dialogConfig = {
			width: "500px",
			height: "375px",
			panelClass: "modal-centralizado",
			disableClose: true,
			autoFocus: false,
		};

		this.tefService.processarTransacaoVerificacao(
			this.dialog,
			TefComponent,
			dialogConfig
		);
	}
}
