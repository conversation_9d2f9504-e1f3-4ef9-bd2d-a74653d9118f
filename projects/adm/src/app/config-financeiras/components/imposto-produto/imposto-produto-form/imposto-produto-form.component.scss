@import "projects/ui/assets/import.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/colors.var.scss";

.titulo {
	position: static;
	width: 311px;
	height: 42px;
	left: 0px;
	top: 0px;

	font-family: Nunito Sans;
	font-style: normal;
	font-weight: bold;
	font-size: 24px;
	line-height: 16px;
	/* identical to box height, or 67% */

	display: flex;
	align-items: center;
	color: #51555a;
}

#informe-os-dados {
	width: 100%;
	height: 88px;
	margin: 0px 16px 32px 16px;
	background: #f7f7f7;
	border: 1px solid #d3d5d7;
	box-sizing: border-box;
	border-radius: 8px;
}

#informe-os-dados h1,
#informe-os-dados p {
	font-family: "Nunito Sans";
	font-style: normal;
	font-size: 14px;
	line-height: 16px;
	align-items: center;
	margin-left: 16px;
	margin-right: 16px;
}

#informe-os-dados h1 {
	margin-top: 16px;
	font-weight: 600;
	color: #000000;
}

#informe-os-dados p {
	margin-top: 8px;
	color: #6f747b;
}

.situacaoTitle {
	margin-bottom: 15px;
	margin-top: 20px;
	font-size: 14px;
	font-family: "Nunito Sans", sans-serif;
	font-weight: bold;
	color: #a6aab1;
}

.dflex {
	margin-bottom: 40px;
}

/* Estilo para o botão Excluir (vermelho) */
.btn-excluir {
	color: rgb(241, 0, 0) !important;
	border: 1px solid #c0392b !important;
}

.btn-excluir:hover {
	background-color: #c0392b !important;
	border-color: #c0392b !important;
}

button {
	margin-right: 10px !important;
}

.h7 {
	color: $typeDefaultText;
	@extend .pct-title4;
}

.right {
	display: flex !important;
	justify-content: flex-end !important;
	width: 100%;
	/* Garante que a div ocupe todo o espaço disponível */
}
