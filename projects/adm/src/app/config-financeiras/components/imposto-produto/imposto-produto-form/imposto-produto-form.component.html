<adm-layout
	(goBack)="voltarListagem()"
	i18n-pageTitle="@@impostoproduto:title"
	modulo="Configurações"
	[pageTitle]="
		isEditMode
			? 'Editar imposto padrão para produtos '
			: 'Adicionar imposto padrão para produtos '
	">
	<pacto-cat-card-plain>
		<form class="imposto-produto-form" [formGroup]="form">
			<!-- CFOP -->
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>CFOP*</ds3-field-label>
						<input type="text" ds3Input formControlName="cfop" />
					</ds3-form-field>
				</div>
			</div>

			<!-- NCM -->
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>NCM</ds3-field-label>
						<input type="text" ds3Input formControlName="ncm" />
					</ds3-form-field>
				</div>
			</div>

			<p></p>
			<ds3-diviser></ds3-diviser>
			<p></p>
			<!-- Configuração NFS-e -->
			<h5 i18n="@@impostoproduto:configuracao-nfse">Configuração NFS-e</h5>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Configuração emissão NFS-e</ds3-field-label>
						<ds3-select
							formControlName="configuracaoNfse"
							[options]="nfseOptions"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>

			<p></p>
			<ds3-diviser></ds3-diviser>
			<p></p>

			<!-- Configuração NFC-e -->
			<h5 i18n="@@impostoproduto:configuracao-nfc">Configuração NFC-e</h5>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Configuração emissão NFC-e</ds3-field-label>
						<ds3-select
							formControlName="configuracaoNfce"
							[options]="nfceOptions"
							ds3Input></ds3-select>
					</ds3-form-field>
				</div>
			</div>
			<p></p>
			<ds3-diviser></ds3-diviser>
			<p></p>
			<p class="h7" i18n="@@impostoproduto:nota-fiscal">Nota Fiscal</p>

			<!-- Código Lista Serviço -->
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Código lista serviço</ds3-field-label>
						<input type="text" ds3Input formControlName="codListaServico" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Código Tributação Município -->
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Código tributação município</ds3-field-label>
						<input
							type="text"
							ds3Input
							formControlName="codTributacaoMunicipio" />
					</ds3-form-field>
				</div>
			</div>

			<!-- Descrição Serviço Município -->
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Descrição serviço município</ds3-field-label>
						<input type="text" ds3Input formControlName="desServicoMunicipio" />
					</ds3-form-field>
				</div>
			</div>

			<p></p>
			<!-- Enviar Percentual Impostos -->
			<div class="row">
				<div class="col-6">
					<ds3-checkbox formControlName="enviarPercentualImposto">
						Enviar percentual impostos (federal, estadual, municipal)
					</ds3-checkbox>
				</div>
			</div>

			<!-- Percentual Impostos -->
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Percentual Federal</ds3-field-label>
						<input
							type="text"
							ds3Input
							formControlName="perFederal"
							placeholder="0,0" />
						<div ds3Suffix>%</div>
					</ds3-form-field>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Percentual Estatual</ds3-field-label>
						<input
							type="text"
							ds3Input
							formControlName="perEstadual"
							placeholder="0,0" />
						<div ds3Suffix>%</div>
					</ds3-form-field>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Percentual Municipal</ds3-field-label>
						<input
							type="text"
							ds3Input
							formControlName="perMunicipal"
							placeholder="0,0" />
						<div ds3Suffix>%</div>
					</ds3-form-field>
				</div>
			</div>

			<p></p>
			<ds3-diviser></ds3-diviser>
			<p></p>

			<!-- ICMS -->
			<h5 i18n="@@impostoproduto:icms">ICMS</h5>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Situação tributária ICMS</ds3-field-label>
						<input
							type="text"
							ds3Input
							formControlName="situacaoTributariaICMS" />
					</ds3-form-field>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Aliquota ICMS</ds3-field-label>
						<input
							type="number"
							ds3Input
							formControlName="aliquotaICMS"
							placeholder="0,0" />
					</ds3-form-field>
				</div>
			</div>
			<p></p>
			<!-- Isento ICMS -->
			<div class="row">
				<div class="col-6">
					<ds3-checkbox formControlName="isentoICMS">Isento ICMS</ds3-checkbox>
				</div>
			</div>

			<!-- Enviar Aliquota ICMS -->
			<div class="row">
				<div class="col-6">
					<ds3-checkbox formControlName="enviarAliquotaICMS">
						Enviar aliquota ICMS no JSON(NFe)
					</ds3-checkbox>
				</div>
			</div>
			<p></p>
			<ds3-diviser></ds3-diviser>
			<p></p>
			<h5 i18n="@@impostoproduto:cbnef">CBNEF</h5>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Código de benefício fiscal</ds3-field-label>
						<input type="text" ds3Input formControlName="codBeneficioFiscal" />
					</ds3-form-field>
				</div>
			</div>

			<p></p>
			<ds3-diviser></ds3-diviser>
			<p></p>

			<!-- PIS -->
			<h5 i18n="@@impostoproduto:pis">PIS</h5>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Situação tributária PIS</ds3-field-label>
						<input
							type="text"
							ds3Input
							formControlName="situacaoTributariaPIS" />
					</ds3-form-field>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Aliquota PIS</ds3-field-label>
						<input
							type="number"
							ds3Input
							formControlName="aliquotaPIS"
							placeholder="0,0" />
					</ds3-form-field>
				</div>
			</div>
			<p></p>
			<ds3-diviser></ds3-diviser>
			<p></p>
			<!-- Isento PIS -->
			<div class="row">
				<div class="col-6">
					<ds3-checkbox formControlName="isentoPIS">Isento PIS</ds3-checkbox>
				</div>
			</div>

			<!-- Enviar Aliquota PIS -->
			<div class="row">
				<div class="col-6">
					<ds3-checkbox formControlName="enviarAliquotaPIS">
						Enviar aliquota PIS no JSON(NFe)
					</ds3-checkbox>
				</div>
			</div>
			<p></p>
			<ds3-diviser></ds3-diviser>
			<p></p>

			<!-- Confins -->
			<h5 i18n="@@impostoproduto:confins">COFINS</h5>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Situação tributária COFINS</ds3-field-label>
						<input
							type="text"
							ds3Input
							formControlName="situacaoTributariaCOFINS" />
					</ds3-form-field>
				</div>
			</div>
			<div class="row">
				<div class="col-6">
					<ds3-form-field>
						<ds3-field-label>Aliquota COFINS</ds3-field-label>
						<input
							type="number"
							ds3Input
							formControlName="aliquotaCOFINS"
							placeholder="0,0" />
					</ds3-form-field>
				</div>
			</div>
			<p></p>
			<!-- Isento CONFINS -->
			<div class="row">
				<div class="col-6">
					<ds3-checkbox formControlName="isentoCOFINS">
						Isento COFINS
					</ds3-checkbox>
				</div>
			</div>

			<!-- Enviar Aliquota COMNFINS -->
			<div class="row">
				<div class="col-6">
					<ds3-checkbox formControlName="enviarAliquotaCOFINS">
						Enviar aliquota COFINS no JSON(NFe)
					</ds3-checkbox>
				</div>
			</div>

			<p></p>
			<p></p>

			<div class="row right">
				<pacto-log
					[table]="true"
					[titulo]="tableTitle"
					[url]="urlLog"
					*ngIf="isEditMode"
					class="mr-2"></pacto-log>

				<button
					*ngIf="!isEditMode"
					ds3-outlined-button
					(click)="voltarListagem()">
					Cancelar
				</button>
				<button ds3-flat-button (click)="salvar()">
					{{ isEditMode ? "Salvar alterações" : "Salvar" }}
				</button>
			</div>
		</form>
	</pacto-cat-card-plain>
	<pacto-traducoes-xingling #traducao>
		<span i18n="@@impostoproduto:saved-success" xingling="saved-success">
			Imposto produto salvo com sucesso!
		</span>
	</pacto-traducoes-xingling>
</adm-layout>
