import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { CadastroAuxApiImxpostoProdutoService } from "cadastro-aux-api";
import { SnotifyService } from "ng-snotify";
import { Api, SessionService } from "sdk";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	TraducoesXinglingComponent,
} from "ui-kit";
import { AdmRestService } from "../../../../adm-rest.service";
import { ExclusaoImpostoModalComponent } from "../exclusao-imposto-modal/exclusao-imposto-modal.component";
import { Observable } from "rxjs";
import { LoaderService } from "../../../../../../../../src/app/loader/loader.service";

@Component({
	selector: "adm-imposto-produto",
	templateUrl: "./imposto-produto.component.html",
	styleUrls: ["./imposto-produto.component.scss"],
})
export class ImpostoProdutoComponent implements OnInit, AfterViewInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnCFOP", { static: true }) columnCFOP: TemplateRef<any>;
	@ViewChild("columnNCM", { static: true }) columnNCM: TemplateRef<any>;
	//@ViewChild("columnSituacao", { static: true }) columnSituacao: TemplateRef<any>;
	@ViewChild("tableImpostoProduto", { static: false })
	tableImpostoProduto: RelatorioComponent;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig = { filters: [] };

	constructor(
		private router: Router,
		private cd: ChangeDetectorRef,
		private session: SessionService,
		private admRest: AdmRestService,
		private notificationService: SnotifyService,
		private impostoService: CadastroAuxApiImxpostoProdutoService,
		private ngbModal: NgbModal,
		private loaderService: LoaderService
	) {}

	voltarHome() {
		this.router.navigate(["adm"]);
	}

	novoImpostoProduto() {
		this.router.navigate(["adm", "config-financeiras", "novo-imposto-produto"]);
	}

	iconClickFn(event: { row: any; iconName: string }) {
		if (event.iconName === "editImpostoProduto") {
			this.editImpostoProduto(event.row);
		} else if (event.iconName === "deleteImpostoProduto") {
			this.openModalConfirmacaoExclusao(event);
		}
	}

	openModalConfirmacaoExclusao(event) {
		const modalConfirmacao = this.ngbModal.open(ExclusaoImpostoModalComponent, {
			windowClass: "modal-confirmacao",
		});

		modalConfirmacao.result
			.then((excluir) => {
				if (excluir) {
					this.deleteImpostoProduto(event.row);
				}
			})
			.catch((error) => {});

		this.cd.detectChanges();
	}

	ngOnInit() {
		this.initTable();
		this.cd.detectChanges();
	}

	ngAfterViewInit() {
		this.initFilter();
	}

	editImpostoProduto(imposto) {
		this.router.navigate([
			"adm",
			"config-financeiras",
			"imposto-produto",
			imposto.codigo,
		]);
	}

	deleteImpostoProduto(row: any) {
		this.impostoService.delete(row.codigo).subscribe(
			(response) => {
				this.notificationService.success(this.traducao.getLabel("DELETED"));
				this.tableImpostoProduto.reloadData();
			},
			(httpErrorResponse) => {
				const err = httpErrorResponse.error;
				if (err.meta && err.meta.messageValue) {
					this.notificationService.error(err.meta.messageValue);
				}
			}
		);
	}

	private initFilter() {
		setTimeout(() => {
			this.filterConfig = {
				filters: [
					{
						name: "situacao",
						label: this.traducao.getLabel("filtro-situacao"),
						type: GridFilterType.DS3_CHIPS,
						options: [
							{ value: "true", label: this.traducao.getLabel("ativo") },
							{ value: "false", label: this.traducao.getLabel("inativo") },
						],
						initialValue: ["true"],
					},
				],
			};
		});
	}

	private initTable() {
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.admRest.buildFullUrl(
					"/imposto-produto",
					false,
					Api.MSCADAUX
				),
				quickSearch: true,
				ghostLoad: true,
				ghostAmount: 5,
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.columnCodigo,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "cfop",
						titulo: this.columnCFOP,
						visible: true,
						ordenavel: true,
					},
					{
						nome: "ncm",
						titulo: this.columnNCM,
						visible: true,
						ordenavel: true,
					},
				],
				actions: [
					{
						nome: "editImpostoProduto",
						iconClass: "pct pct-edit cor-action-default-able04",
						tooltipText: this.traducao.getLabel("TOOLTIP_EDIT"),
						actionFn: (row) => this.editImpostoProduto(row),
					},
					{
						nome: "deleteImpostoProduto",
						iconClass: "pct pct-trash-2 cor-action-default-risk04",
						tooltipText: this.traducao.getLabel("TOOLTIP_DELETE"),
						actionFn: (row) => this.deleteImpostoProduto(row),
					},
				],
			});
			this.cd.detectChanges();
		});
	}
	processarTodosImpostos(): void {
		this.loaderService.show();
		this.impostoService.processarTodosImpostos().subscribe({
			next: (response) => {
				this.loaderService.hide();
				this.notificationService.success(
					this.traducao.getLabel("DADOS_ATUALIZADOS")
				);
				this.tableImpostoProduto.reloadData();
			},
			error: (httpErrorResponse) => {
				this.loaderService.hide();
				console.error("Erro no processamento:", httpErrorResponse);

				const err = httpErrorResponse.error;
				const mensagem =
					(err.meta && err.meta.messageValue) ||
					err.message ||
					"Erro durante o processamento dos impostos";

				this.notificationService.error(mensagem);
			},
		});
	}

	get urlLog() {
		return this.admRest.buildFullUrlCadAux("imposto-produto/logs");
	}
}
