import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { AlunoPactoPay } from "./aluno.model";
import { ApiResponseSingle, ApiResponseList } from "./base.model";
import { MsPactopayApiBaseService } from "./ms-pactopay-api-base.service";
import { MsPactopayApiModule } from "./ms-pactopay-api.module";

@Injectable({
	providedIn: "root",
})
export class MsPactoPayApiCobrancaService {
	constructor(private restService: MsPactopayApiBaseService) {}

	public bloquearAutorizacao({ pessoa, tipo, data }): Observable<any> {
		const params = { tipo, data };
		return this.restService
			.post(`cobranca/bloquear-cobrancas/${pessoa}`, null, { params })
			.pipe(
				map((result: ApiResponseSingle<any>) => {
					return result.content;
				})
			);
	}

	public desbloquearAutorizacao({ pessoa }): Observable<any> {
		return this.restService
			.post(`cobranca/desbloquear-cobrancas/${pessoa}`, null)
			.pipe(
				map((result: ApiResponseSingle<any>) => {
					return result.content;
				})
			);
	}

	public excluirAutorizacao(codigo: number): Observable<any> {
		return this.restService
			.delete(`autorizacao-cliente/${codigo}`)
			.pipe(map((result: any) => result));
	}

	public getAllTipoDeAutorizacao(): Observable<any> {
		return this.restService.get("uteis/autorizacao-tipo").pipe(
			map((response: any) => {
				return response.content;
			})
		);
	}

	public getAllParcelasCobrar(): Observable<any> {
		return this.restService.get("uteis/autorizacao-tipo-objetos").pipe(
			map((response: any) => {
				return response.content;
			})
		);
	}

	public getProdutosEspecificos(): Observable<any> {
		return this.restService.get("uteis/autorizacao-tipo-produto").pipe(
			map((response: any) => {
				return response.content;
			})
		);
	}

	public obterInformacoesPessoa(matricula: string): Observable<AlunoPactoPay> {
		return this.restService
			.get(`cobranca/informacoes-pessoa-by-matricula/${matricula}`)
			.pipe(
				map((result: ApiResponseSingle<AlunoPactoPay>) => {
					return result.content;
				})
			);
	}

	eficienciaTotalizador(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/eficiencia/totalizador",
			bodySend,
			{
				params: {
					op: "eficiencia",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	eficienciaFormaRecebimento(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "formaRecebimento",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/eficiencia/forma-recebimento",
			bodySend,
			{
				params: {
					op: "eficiencia",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	eficienciaComunicacao(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "comunicacao",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/eficiencia/comunicacao",
			bodySend,
			{
				params: {
					op: "eficiencia",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	retentativaTotalizador(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/retentativa/totalizador",
			bodySend,
			{
				params: {
					op: "retentativa",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	retentativaHistorico(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/retentativa/historico-dia",
			bodySend,
			{
				params: {
					op: "retentativa",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	retentativaRetentativas(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/retentativa/retentativas",
			bodySend,
			{
				params: {
					op: "retentativa",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	comunicacaoTotalizador(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/comunicacao/totalizador",
			bodySend,
			{
				params: {
					op: "comunicacao",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	comunicacaoHistoricoDia(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/comunicacao/historico-dia",
			bodySend,
			{
				params: {
					op: "comunicacao",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	comunicacaoCanais(chave: string, filtros: any, body: any): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/comunicacao/canais",
			bodySend,
			{
				params: {
					op: "comunicacao",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	comunicacaoDetalhe(chave: string, filtros: any, body: any): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/comunicacao/detalhe",
			bodySend,
			{
				params: {
					op: "comunicacao",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	comunicacaoFormasPagamento(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/comunicacao/forma-recebimento",
			bodySend,
			{
				params: {
					op: "comunicacao",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	comunicacaoTentativa(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/comunicacao/tentativa",
			bodySend,
			{
				params: {
					op: "comunicacao",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	antecipacaoTotalizador(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/antecipacao/totalizador",
			bodySend,
			{
				params: {
					op: "antecipacao",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	antecipacaoFormasPagamento(
		chave: string,
		filtros: any,
		body: any
	): Observable<any> {
		const bodySend = {
			tipo: "totalizador",
			...body,
		};
		return this.restService.post(
			"regua-cobranca-bi/antecipacao/forma-recebimento",
			bodySend,
			{
				params: {
					op: "antecipacao",
					key: chave,
					filters: JSON.stringify(filtros),
				},
			}
		);
	}

	public buscarCobrancasPixAutomatico(matricula: string): Observable<any> {
		return this.restService
			.get(`cobranca/buscarCobrancasPixAutomatico/${matricula}`)
			.pipe(
				map((result: ApiResponseList<any>) => {
					return result;
				})
			);
	}
}
