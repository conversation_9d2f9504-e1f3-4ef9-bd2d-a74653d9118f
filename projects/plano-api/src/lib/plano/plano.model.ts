import { ModeloContrato } from "../modelo-contrato/modelo-contrato.model";
import { Produto } from "../produto/produto.model";
import { Descon<PERSON> } from "../desconto/desconto.model";
import { PlanoProdutoSugerido } from "./plano-produto-sugerido.model";
import { PlanoTipoTipoProduto } from "../tipo-plano/tipo-plano.model";
import { CondicaoPagamento } from "../condicao-pagamento/condicao-pagamento.model";
import { Pacote } from "../pacote/pacote.model";
import { Modalidade } from "../modalidade/modalidade.model";
import { Horario } from "../horario/horario.model";
import { Empresa } from "../base/empresa.model";

export interface Plano {
	codigo: number;
	descricao: string;
	tipoPlano: string;
	vigenciaDe: number;
	vigenciaAte: number;
	ingressoAte: number;
	permitirAcessoSomenteNaEmpresaVendeuContrato: boolean;
	status: string;
	inicioMinimoContrato: Date;
	modeloContrato: ModeloContrato;
	termoAceite: ModeloContrato;
	produtoContrato: Produto;
	empresa: number; // EmpresaVO;
	descontoAntecipado: Desconto; // DescontoVO;
	comissao: boolean;
	bolsa: boolean;
	planoPersonal: boolean;
	cobrarProdutoSeparado: boolean;
	nrVezesParcelarProduto: number;
	pontos: number;
	convidadosPorMes: number;
	apresentarVendaRapida: boolean;
	permiteSituacaoAtestadoContrato: boolean;
	dividirManutencaoParcelasEA: boolean;
	aceitaDescontoExtra: boolean;
	permitirAcessoRedeEmpresa: boolean;
	planoTipo: PlanoTipo;
	cobrarAdesaoSeparada: boolean;
	nrVezesParcelarAdesao: number;
	percentualMultaCancelamento: number;
	produtoTaxaCancelamento: Produto;
	quantidadeMaximaFrequencia: number;
	tipoFrequencia: number;
	restringirMarcacaoAulasColetivas: boolean;
	restringirQtdMarcacaoPorDia: number;
	restringirQtdMarcacaoPorDiaGeral: number;
	permitePagarComBoleto: boolean;
	parcelamentoOperadora: boolean;
	parcelamentoOperadoraDuracao: boolean;
	diaDoMesDescontoBoletoPagAntecipado: number;
	porcentagemDescontoBoletoPagAntecipado: number;
	vendaCreditoTreino: boolean;
	creditoTreinoNaoCumulativo: boolean;
	creditoSessao: boolean;
	qtdSemanasAno: number;
	prorataObrigatorio: boolean;
	diasVencimentoProrata: string;
	regimeRecorrencia: boolean;
	planoRecorrencia: PlanoRecorrencia;
	quantidadeCompartilhamentos: number;
	renovavelAutomaticamente: boolean;
	renovarProdutoObrigatorio: boolean;
	renovarAutomaticamenteUtilizandoValorBaseContrato: boolean;
	renovarAnuidadeAutomaticamente: boolean;
	renovarAutomaticamenteComDesconto: boolean;
	renovarAutomaticamenteApenasCondicaoPagamentoRecorrencia: boolean;
	naoRenovarContratoParcelaVencidaAberto: boolean;
	site: boolean;
	permitirCompartilharPLanoNoSite: boolean;
	permitirVendaPlanoSiteNoBalcao: boolean;
	permitirVendaPlanoTotemNoBalcao: boolean;
	totem: boolean;
	renovarComDescontoTotem: boolean;
	descricaoEncantamento: string;
	maximoVezesParcelar: number;
	duracoes: Array<PlanoDuracao>;
	pacotes: Array<PlanoPacote>;
	modalidades: Array<PlanoModalidade>;
	horarios: Array<PlanoHorario>;
	produtosSugeridos: Array<PlanoProdutoSugerido>;
	excecoes: Array<PlanoExcecao>;
	empresas: Array<PlanoEmpresa>;
	replicar: boolean;
	replicarRedeEmpresa: boolean;
	restringirMarcacaoAulaPorNrVezesModalidade: boolean;
	gerarValorCreditoExtra: boolean;
	produtoCreditoExtra: Produto;
	valorCreditoExtra: number;
}

export interface PlanoTipo {
	codigo: number;
	nome: string;
	tipo: string;
	ativo: boolean;
	tiposProduto: Array<PlanoTipoTipoProduto>;
}

export interface PlanoRecorrencia {
	codigo: number;
	taxaAdesao: number;
	valorAnuidade: number;
	valorMensal: number;
	diaAnuidade: number;
	mesAnuidade: number;
	duracaoPlano: number;
	renovavelAutomaticamente: boolean;
	naoRenovarParcelaVencida: boolean;
	naoCobrarAnuidadeProporcional: boolean;
	parcelaAnuidade: number;
	cancelamentoProporcional: boolean;
	qtdDiasCobrarProximaParcela: number;
	qtdDiasCobrarAnuidadeTotal: number;
	gerarParcelasValorDiferente: boolean;
	parcelarAnuidade: boolean;
	parcelas: Array<PlanoRecorrenciaParcela>;
	parcelasAnuidade: Array<PlanoAnuidadeParcela>;
	anuidadeNaParcela: boolean;
	qtdDiasAposVencimentoCancelamentoAutomatico: number;
	aplicarDiasVencimentoContratosAtivos: boolean;
	aplicarCancelamentoProporcionalContratosAtivos: boolean;
}

export interface PlanoRecorrenciaParcela {
	codigo: number;
	numero: number;
	valor: number;
}

export interface PlanoAnuidadeParcela {
	codigo: number;
	numero: number;
	valor: number;
	parcela: number;
}

export interface PlanoDuracao {
	codigo: number;
	numeroMeses: number;
	nrMaximoParcelasCondPagamento: number;
	carencia: number;
	quantidadeDiasExtra: number;
	pontos: number;
	situacao: boolean;
	tipoValor: string;
	tipoOperacao: string;
	valorDesejado: number;
	valorDesejadoParcela: number;
	valorDesejadoMensal: number;
	duracoesCreditoTreino: Array<PlanoDuracaoCreditoTreino>;
	condicoesPagamento: Array<PlanoCondicaoPagamento>;
	replicar: boolean;
	valorEspecifico: number;
	percentualdesconto: number;
	totalDias: number;
}

export interface PlanoDuracaoCreditoTreino {
	codigo: number;
	tipoHorarioCreditoTreino: number;
	numeroVezesSemana: number;
	quantidadeCreditoCompra: number;
	valorUnitario: number;
	quantidadeCreditoMensal: number;
}

export interface PlanoCondicaoPagamento {
	codigo: number;
	condicaoPagamento: CondicaoPagamento;
	qtdParcela: number;
	tipoOperacao: string;
	tipoValor: string;
	percentualDesconto: number;
	valorEspecifico: number;
}

export interface PlanoPacote {
	codigo: number;
	pacote: Pacote;
}

export interface PlanoModalidade {
	codigo: number;
	modalidade: Modalidade;
	listaVezesSemana?: string;
	vezesSemana: Array<PlanoModalidadeVezesSemana>;
}

export interface PlanoModalidadeVezesSemana {
	codigo: number;
	tipoOperacao: string;
	tipoValor: string;
	nrVezes: number;
	valorEspecifico: number;
	percentualDesconto: number;
	referencia: boolean;
}

export interface PlanoHorario {
	codigo: number;
	horario: Horario;
	tipoOperacao: string;
	tipoValor: string;
	valorEspecifico: number;
	percentualDesconto: number;
	situacaoPlano: boolean;
}

export interface PlanoEmpresa {
	codigo: number;
	plano: Plano;
	empresa: Empresa;
	venda: boolean;
	acesso: boolean;
}

export interface PlanoExcecao {
	codigo: number;
	pacote: Pacote;
	modalidade: Modalidade;
	horario: Horario;
	duracao: number;
	vezesSemana: number;
	valor: number;
}
