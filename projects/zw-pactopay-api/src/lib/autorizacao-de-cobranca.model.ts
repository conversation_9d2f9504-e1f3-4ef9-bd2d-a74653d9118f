export interface AutorizacaoCartao {
  codigo?: number;
  pessoa?: number;
  convenio?: string;
  usuario?: number;
  username?: string;
  tipo?: number;
  titular?: string;
  cvv?: string;
  card?: string;
  vencimento?: string;
  documento?: string;
  tipoCobrar?: number;
  produtos?: Array<string>;
  verificar?: boolean;
  clienteTitular?: boolean;
  usarIdVindi?: boolean;
}

export interface AutorizacaoDebito {
  codigo?: number;
  pessoa?: number;
  convenio?: string;
  usuario?: number;
  username?: string;
  tipo?: number;
  tipoCobrar?: number;
  titular?: string;
  documento?: string;
  agencia?: string;
  agenciadv?: string;
  contacorrente?: string;
  contacorrentedv?: string;
  autorizarclientedebito?: boolean;
  usarIdVindi?: boolean;
}

export interface AutorizacaoBoleto {
  codigo?: number;
  pessoa?: number;
  convenio?: string;
  usuario?: number;
  username?: string;
  tipo?: number;
  usarIdVindi?: boolean;
}

export interface AutorizacaoPixAutomatico {
  codigo?: number;
  pessoa?: number;
  convenio?: string;
  usuario?: number;
  username?: string;
  tipo?: number;
  vencimento?: string;
  documento?: string;
  tipoCobrar?: number;
}
