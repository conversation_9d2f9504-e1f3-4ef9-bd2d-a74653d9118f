import { TraducoesXinglingComponent } from "ui-kit";
import { CategoriaProduto } from "../categoria-produto/categoria-produto.model";

export class Produto {
	codigo: number;
	descricao: string;
	categoriaProduto?: CategoriaProduto;
	valorFinal?: number;
	qtdePontos: number;
}

export enum TipoProduto {
	matricula = "MA",
	rematricula = "RE",
	renovacao = "RN",
	produto_estoque = "PE",
	mes_referencia_plano = "PM",
	servico = "SE",
	convenio_desconto = "CD",
	desconto = "DE",
	devolucao = "DV",
	trancamento = "TR",
	retorno_trancamento = "RT",
	aula_avulsa = "AA",
	diaria = "DI",
	freepass = "FR",
	alterar_horario = "AH",
	manutencao_modalidade = "MM",
	manutencao_conta_corrente = "MC",
	desconto_renovacao_antecipada = "DR",
	taxa_personal = "TP",
	sessao = "SS",
	devolucao_credito = "DC",
	atestado = "AT",
	taxa_de_adesao_plano_recorrencia = "TD",
	taxa_renegociacao = "TN",
	credito_personal = "CP",
	taxa_de_anuidade_plano_recorrencia = "TA",
	devolucao_de_recebiveis = "RD",
	deposito_conta_corrente_aluno = "CC",
	acerto_conta_corrente_aluno = "AC",
	quitacao_de_dinheiro = "QU",
	armario = "AR",
	multa_juros = "MJ",
	cheque_devolvido = "CH",
	desafio = "DS",
	homefit = "HM",
	appVitio = "VT",
	bioTotem = "BT",
	consultaNutricional = "CN",
}

export const getTipoProdutoLabelByKey = (
	traducaoId: string,
	traducao: TraducoesXinglingComponent
) => {
	return traducao.getLabel(traducaoId);
};

export const getTipoProdutoText = (traducao: TraducoesXinglingComponent) => {
	return new Array<{ id: string; label: string }>(
		{ id: null, label: traducao.getLabel("NA") },
		{ id: "MA", label: traducao.getLabel("MA") },
		{ id: "RE", label: traducao.getLabel("RE") },
		{ id: "RN", label: traducao.getLabel("RN") },
		{ id: "PE", label: traducao.getLabel("PE") },
		{ id: "BT", label: traducao.getLabel("BT") },
		{ id: "PM", label: traducao.getLabel("PM") },
		{ id: "SE", label: traducao.getLabel("SE") },
		{ id: "CD", label: traducao.getLabel("CD") },
		{ id: "DE", label: traducao.getLabel("DE") },
		{ id: "DV", label: traducao.getLabel("DV") },
		{ id: "TR", label: traducao.getLabel("TR") },
		{ id: "RT", label: traducao.getLabel("RT") },
		{ id: "AA", label: traducao.getLabel("AA") },
		{ id: "DI", label: traducao.getLabel("DI") },
		{ id: "FR", label: traducao.getLabel("FR") },
		{ id: "AH", label: traducao.getLabel("AH") },
		{ id: "MM", label: traducao.getLabel("MM") },
		{ id: "MC", label: traducao.getLabel("MC") },
		{ id: "DR", label: traducao.getLabel("DR") },
		{ id: "TP", label: traducao.getLabel("TP") },
		{ id: "SS", label: traducao.getLabel("SS") },
		{ id: "DC", label: traducao.getLabel("DC") },
		{ id: "AT", label: traducao.getLabel("AT") },
		{ id: "TD", label: traducao.getLabel("TD") },
		{ id: "TN", label: traducao.getLabel("TN") },
		{ id: "CP", label: traducao.getLabel("CP") },
		{ id: "TA", label: traducao.getLabel("TA") },
		{ id: "RD", label: traducao.getLabel("RD") },
		{ id: "CC", label: traducao.getLabel("CC") },
		{ id: "AC", label: traducao.getLabel("AC") },
		{ id: "QU", label: traducao.getLabel("QU") },
		{ id: "AR", label: traducao.getLabel("AR") },
		{ id: "MJ", label: traducao.getLabel("MJ") },
		{ id: "CH", label: traducao.getLabel("CH") },
		{ id: "DS", label: traducao.getLabel("DS") },
		{ id: "HM", label: traducao.getLabel("HM") }
	);
};
