import {
	Component,
	OnInit,
	ViewChild,
	ViewEncapsulation,
	ChangeDetectionStrategy,
	ChangeDetectorRef,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { Router } from "@angular/router";

import { SnotifyService } from "ng-snotify";

import { TreinoApiWodService, WodBase } from "treino-api";
import { MonitorService } from "../../monitor.service";

@Component({
	selector: "pacto-option-monitor",
	templateUrl: "./option-monitor.component.html",
	styleUrls: ["./option-monitor.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
	encapsulation: ViewEncapsulation.None,
})
export class OptionMonitorComponent implements OnInit {
	@ViewChild("mensagemSelecioneItem", { static: true }) mensagemSelecioneItem;
	@ViewChild("pequena", { static: true }) pequena;
	@ViewChild("media", { static: true }) media;
	@ViewChild("grande", { static: true }) grande;
	@ViewChild("muitoGrande", { static: true }) muitoGrande;
	@ViewChild("gigante", { static: true }) gigante;

	estadoDasOpcoes: any = {
		resultados: false,
		workout: false,
		parceiros: false,
		visitantes: false,
		ranking: false,
		todasUnidades: false,
	};

	wods: Array<WodBase> = [];
	fontes = [];
	tamanhoFonte = 0;
	options = {
		resultados: false,
		parceiros: false,
		visitantes: false,
		workout: {
			active: false,
		},
		ranking: {
			active: false,
			organizacao: null,
		},
	};

	opcoesRanking = false;
	opcoesWorkout = false;

	constructor(
		private router: Router,
		private wodService: TreinoApiWodService,
		private cd: ChangeDetectorRef,
		private monitorService: MonitorService,
		private snotifyService: SnotifyService
	) {}

	wodFormControl: FormControl = new FormControl(null);
	fontesControl: FormControl = new FormControl(null);

	ngOnInit() {
		this.loadFontes();
		this.options.ranking.organizacao = "UNISSEX";
		this.carregarWods();
		this.wodFormControl.valueChanges.subscribe(() => {
			this.updateOptions();
		});
	}

	toggleWorkout() {
		if (this.wods.length > 0) {
			this.estadoDasOpcoes.workout = !this.estadoDasOpcoes.workout;
			this.options.workout.active = !this.options.workout.active;
			this.onChangeHandler();
		}
	}

	toggleTodasUnidades() {
		this.estadoDasOpcoes.todasUnidades = !this.estadoDasOpcoes.todasUnidades;
		this.carregarWods();
	}

	toggleRanking() {
		if (this.wods.length > 0) {
			this.estadoDasOpcoes.ranking = !this.estadoDasOpcoes.ranking;
			this.options.ranking.active = !this.options.ranking.active;
			this.onChangeHandler();
		}
	}

	toggleResultados() {
		if (this.wods.length > 0) {
			this.estadoDasOpcoes.resultados = !this.estadoDasOpcoes.resultados;
			this.options.resultados = !this.options.resultados;
			this.onChangeHandler();
		}
	}

	toggleVisitantes() {
		if (this.wods.length > 0) {
			this.estadoDasOpcoes.visitantes = !this.estadoDasOpcoes.visitantes;
			this.options.visitantes = !this.options.visitantes;
			this.onChangeHandler();
		}
	}

	toggleDirection() {
		this.opcoesRanking = !this.opcoesRanking;
	}

	toogleDirectionWorkout() {
		if (this.wods.length > 1) {
			this.opcoesWorkout = !this.opcoesWorkout;
		}
	}

	get sortDirection() {
		if (!this.opcoesRanking) {
			return "fa-caret-right";
		} else {
			return "fa-caret-down";
		}
	}

	onChangeHandler() {
		this.updateOptions();
	}

	private updateOptions() {
		setTimeout(() => {
			this.monitorService.wodId = this.wodFormControl.value;
			this.monitorService.options$.next(this.options);
		});
	}

	clickHandler() {
		if (this.options.workout.active || this.options.ranking.active) {
			const urlTree = this.router.createUrlTree(
				["cross", "monitor", "visualizar"],
				{
					queryParams: {
						resultados: this.options.resultados,
						parceiros: this.options.parceiros,
						visitantes: this.options.visitantes,
						workoutActive: this.options.workout.active,
						workoutId: this.wodFormControl.value,
						rankingActive: this.options.ranking.active,
						rankingOrganizacao: this.options.ranking.organizacao,
						fonte: this.fontesControl.value,
						fullscreen: 1,
					},
				}
			);
			const url = urlTree.toString().slice(1);
			window.open(url, "_blank");
		} else {
			const mensagemSelecioneItem =
				this.mensagemSelecioneItem.nativeElement.innerHTML;
			this.snotifyService.error(mensagemSelecioneItem);
		}
	}

	loadFontes() {
		this.fontes = [
			{ id: 1, nome: this.pequena.nativeElement.innerHTML },
			{ id: 2, nome: this.media.nativeElement.innerHTML },
			{ id: 3, nome: this.grande.nativeElement.innerHTML },
			{ id: 4, nome: this.muitoGrande.nativeElement.innerHTML },
			{ id: 5, nome: this.gigante.nativeElement.innerHTML },
		];
		this.cd.detectChanges();
	}

	private carregarWods() {
		this.wodService
			.obterWodsDia(Date.now(), this.estadoDasOpcoes.todasUnidades)
			.subscribe((result) => {
				this.wods = result;
				if (this.wods.length > 0) {
					this.wodFormControl.setValue(this.wods[0].id);
				}
				if (this.fontes.length > 0) {
					this.fontesControl.setValue(this.fontes[1].id);
				}
				this.cd.detectChanges();
			});
	}
}
