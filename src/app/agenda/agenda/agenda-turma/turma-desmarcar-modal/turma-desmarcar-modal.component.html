<div class="block-info">
	<pacto-cat-person-avatar
		[diameter]="72"
		[uri]="aluno?.imageUri"></pacto-cat-person-avatar>
</div>
<div class="block-info type-h5">
	{{ traducoes.getLabel("realmenteRemover") }} {{ aluno?.nome }}
	{{ traducoes.getLabel("desta") }}
	{{ aulaCheia ? traducoes.getLabel("aula") : traducoes.getLabel("turma") }}?
</div>

<div *ngIf="alunoFixado" class="alert-message">
	<i class="pct pct-alert-triangle"></i>
	<span>
		O aluno {{ aluno?.nome }} continua fixado nesta aula, apenas será
		desmarcado. Para remover completamente, use a opção "Desafixar".
	</span>
</div>

<div class="block-info">
	<div class="buttons-container" [class.single-button]="!alunoFixado">
		<pacto-cat-button
			*ngIf="alunoFixado"
			(click)="cancel()"
			[full]="true"
			[type]="'OUTLINE'"
			label="Cancelar"></pacto-cat-button>
		<pacto-cat-button
			(click)="confirm()"
			[full]="true"
			i18n-label="@@agenda-detalhes-desmarcar-modal:simDesmarcar"
			label="sim, desmarcar"></pacto-cat-button>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@agenda-detalhes-desmarcar-modal:realmenteRemover"
		xingling="realmenteRemover">
		Deseja realmente remover
	</span>
	<span i18n="@@agenda-detalhes-desmarcar-modal:desta" xingling="desta">
		desta
	</span>
	<span i18n="@@agenda-detalhes-desmarcar-modal:aula" xingling="aula">
		aula
	</span>
	<span i18n="@@agenda-detalhes-desmarcar-modal:turma" xingling="turma">
		turma
	</span>
</pacto-traducoes-xingling>
