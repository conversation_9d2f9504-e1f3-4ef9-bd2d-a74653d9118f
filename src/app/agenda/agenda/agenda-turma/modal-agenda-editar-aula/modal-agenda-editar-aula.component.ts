import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	HostBinding,
	OnInit,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { MatDialog } from "@angular/material/dialog";
import {
	Ambiente,
	Aula,
	AulaCreateEdit,
	TreinoApiAmbienteService,
	TreinoApiAulaService,
	TreinoApiColaboradorService,
	UsuarioBase,
	Nivel,
} from "treino-api";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";
import { ModalAgendaEditarAulaSuccessComponent } from "./modal-agenda-editar-aula-success/modal-agenda-editar-aula-success.component";
import { DatePipe } from "@angular/common";

interface AulaFormModel {
	professor: string;
	ambiente: string;
	niveis: number[];
	horarioInicial: string;
	horarioFinal: string;
	diaAula: string;
	capacidade: string;
	limiteAgregados: string;
	idadeMinimaAnos: number;
	idadeMinimaMeses: number;
	idadeMaximaAnos: number;
	idadeMaximaMeses: number;
	toleranciaCheckinMinutos: string;
	toleranciaCheckinTipo: string;
	naoValidarModalidadeCheckin: boolean;
	mensagem: string;
	controlarCheckin: boolean;
	aulaDisponivelGympass: boolean;
	aulaDisponivelTotalpass: boolean;
	// ativo: boolean; // Descomente se necessário
	nome: string;
}

@Component({
	selector: "pacto-modal-agenda-editar-aula",
	templateUrl: "./modal-agenda-editar-aula.component.html",
	styleUrls: ["./modal-agenda-editar-aula.component.scss"],
	encapsulation: ViewEncapsulation.None,
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalAgendaEditarAulaComponent implements OnInit {
	@HostBinding("class.mdl-agenda-editar-aula")
	enableEncapsulation = true;

	aula: Aula;
	aulaCreateEdit: AulaCreateEdit;
	aulaId: number;
	horarioInicial: string;
	horarioFinal: string;

	dataAula;
	formGroup: FormGroup;
	urlNivel: string;
	optionsDiaAula: Array<{ name: string; value: string }> = new Array<{
		name: string;
		value: string;
	}>();
	tiposTolerancia: Array<{ name: string; value: number }> = new Array<{
		name: string;
		value: number;
	}>();
	professores: Array<UsuarioBase> = [];
	ambientes: Array<Ambiente> = [];
	timeMask = [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];

	constructor(
		private aulaService: TreinoApiAulaService,
		private ambienteService: TreinoApiAmbienteService,
		private colaboradorService: TreinoApiColaboradorService,
		private cd: ChangeDetectorRef,
		private modal: NgbActiveModal,
		private matDialog: MatDialog,
		private restService: RestService,
		private datePipe: DatePipe,
		private snotifyService: SnotifyService
	) {}

	ngOnInit() {
		if (!this.aulaId) {
			throw Error("O id da aula deve ser informado!");
		}
		if (!this.dataAula) {
			throw Error("O dia da aula deve ser informado!");
		}
		this.createForm();
		this.configSelects();
		this.loadData();
	}

	private createForm() {
		this.formGroup = new FormGroup({
			nome: new FormControl(null, Validators.required),
			professor: new FormControl(null, Validators.required),
			ambiente: new FormControl(null, Validators.required),
			niveis: new FormControl(null),
			horarioInicial: new FormControl(null, Validators.required),
			horarioFinal: new FormControl(null, Validators.required),
			diaAula: new FormControl(null, Validators.required),
			capacidade: new FormControl(null, Validators.required),
			limiteAgregados: new FormControl(null, Validators.required),
			idadeMinimaAnos: new FormControl(null),
			idadeMinimaMeses: new FormControl(null),
			idadeMaximaAnos: new FormControl(null),
			idadeMaximaMeses: new FormControl(null),
			toleranciaCheckinMinutos: new FormControl(null),
			toleranciaCheckinTipo: new FormControl(null),
			naoValidarModalidadeCheckin: new FormControl(null),
			mensagem: new FormControl(null),
			controlarCheckin: new FormControl(null),
			aulaDisponivelGympass: new FormControl(null),
			aulaDisponivelTotalpass: new FormControl(null),
			ativo: new FormControl(null),
		});
	}

	private configSelects() {
		this.buildUrls();
		this.buildOptions();
	}

	private buildUrls() {
		this.urlNivel = this.restService.buildFullUrl("niveis");
	}

	private buildOptions() {
		this.buildOptionsAula();
		this.buildOptionsTipoCheckin();
		this.loadProfessores();
		this.loadAmbiente();
	}

	private buildOptionsAula() {
		this.optionsDiaAula = [
			{
				name: "Dom",
				value: "domingo",
			},
			{
				name: "Seg",
				value: "segunda",
			},
			{
				name: "Ter",
				value: "tercao",
			},
			{
				name: "Qua",
				value: "quarta",
			},
			{
				name: "Qui",
				value: "quinta",
			},
			{
				name: "Sex",
				value: "sexta",
			},
			{
				name: "Sáb",
				value: "sabado",
			},
		];
	}

	private buildOptionsTipoCheckin() {
		this.tiposTolerancia = new Array<{ name: string; value: number }>(
			{ value: 1, name: "Após o início da aula" },
			{ value: 2, name: "Antes do início da aula" }
		);
	}

	private loadProfessores() {
		this.colaboradorService
			.obterTodosColaboradoresAptosAAula()
			.subscribe((dados) => {
				this.professores = dados.content;
				this.cd.detectChanges();
			});
	}

	private loadAmbiente() {
		this.ambienteService.obterTodosAmbientes().subscribe((dados) => {
			this.ambientes = dados.content;
			this.cd.detectChanges();
		});
	}

	private loadData() {
		this.aulaService
			.obterAulaPorHorario(this.aulaId, this.dataAula)
			.subscribe((dados) => {
				this.aula = dados;
				this.aulaCreateEdit = {
					nome: this.aula.nome,
					ocupacao: this.aula.ocupacao,
					modalidadeId: this.aula.modalidade ? this.aula.modalidade.id : null,
					professorId: this.aula.professor
						? this.aula.professor.id.toString()
						: null,
					ambienteId: this.aula.ambiente ? this.aula.ambiente.id : null,
					toleranciaMin: this.aula.toleranciaMin,
					tipoTolerancia: this.aula.tipoTolerancia,
					diasSemana: this.aula.diasSemana,
					horarios: this.aula.horarios,
					dataInicio: this.aula.dataInicio,
					dataFinal: this.aula.dataFinal,
					capacidade: this.aula.capacidade,
					limiteVagasAgregados: this.aula.limiteVagasAgregados,
					meta: this.aula.meta,
					pontuacaoBonus: this.aula.pontuacaoBonus,
					bonificacao: this.aula.bonificacao,
					mensagem: this.aula.mensagem,
					idClasseGymPass: this.aula.idClasseGymPass,
					image: null,
					manterFotoAnterior: null,
					tipoEscolhaEdicao: null,
					diaLimiteTurmaEdicao: null,
					imageUrl: this.aula.imageUrl,
					linkVideos: this.aula.linkVideos,
					idHorarioTurmaEdicao: this.aulaId,
					diaHorarioTurmaEdicao: this.dataAula,
					idadeMinimaAnos: this.aula.idadeMinimaAnos,
					idadeMinimaMeses: this.aula.idadeMinimaMeses,
					idadeMaximaAnos: this.aula.idadeMaximaAnos,
					idadeMaximaMeses: this.aula.idadeMaximaMeses,
					niveis: this.aula.niveis,
					validarRestricoesMarcacao: this.aula.validarRestricoesMarcacao,
					visualizarProdutosGympass: this.aula.visualizarProdutosGympass,
					visualizarProdutosTotalpass: this.aula.visualizarProdutosTotalpass,
					horarioInicial: this.horarioInicial,
					horarioFinal: this.horarioFinal,
					naoValidarModalidadeContrato: this.aula.naoValidarModalidadeContrato,
				} as AulaCreateEdit;
				this.loadForm();
			});
	}

	private loadForm() {
		this.formGroup.patchValue({
			nome: this.aulaCreateEdit.nome,
			professor: +this.aulaCreateEdit.professorId,
			ambiente: this.aulaCreateEdit.ambienteId,
			niveis: this.aulaCreateEdit.niveis
				? this.aulaCreateEdit.niveis.map((n) => n.id)
				: [],
			horarioInicial: this.aulaCreateEdit.horarioInicial,
			horarioFinal: this.aulaCreateEdit.horarioFinal,
			diaAula: this.getWeekDayFromDataAula(),
			capacidade: this.aulaCreateEdit.capacidade,
			limiteAgregados: this.aulaCreateEdit.limiteVagasAgregados,
			idadeMinimaAnos: this.aulaCreateEdit.idadeMinimaAnos,
			idadeMinimaMeses: this.aulaCreateEdit.idadeMinimaMeses,
			idadeMaximaAnos: this.aulaCreateEdit.idadeMaximaAnos,
			idadeMaximaMeses: this.aulaCreateEdit.idadeMaximaMeses,
			toleranciaCheckinMinutos: this.aulaCreateEdit.toleranciaMin,
			toleranciaCheckinTipo: this.aulaCreateEdit.tipoTolerancia,
			naoValidarModalidadeCheckin:
				this.aulaCreateEdit.naoValidarModalidadeContrato,
			mensagem: this.aulaCreateEdit.mensagem,
			controlarCheckin: this.aulaCreateEdit.validarRestricoesMarcacao,
			aulaDisponivelGympass: this.aulaCreateEdit.visualizarProdutosGympass,
			aulaDisponivelTotalpass: this.aulaCreateEdit.visualizarProdutosTotalpass,
			// ativo: this.aulaCreateEdit.situacao
		});
	}

	cancel() {
		this.modal.close();
	}

	save() {
		if (this.formGroup.invalid) {
			this.snotifyService.error("Há campos obrigatórios não informados!");
			return;
		}
		this.populateAulaCreateEdit();
		this.openModalSuccess();
	}

	private populateAulaCreateEdit() {
		const aulaFormModel: AulaFormModel = this.formGroup.value;

		if (this.aulaCreateEdit.nome !== aulaFormModel.nome) {
			this.aulaCreateEdit.nome = aulaFormModel.nome;
		}
		if (
			this.aulaCreateEdit.professorId &&
			this.aulaCreateEdit.professorId !== aulaFormModel.professor
		) {
			this.aulaCreateEdit.professorId = aulaFormModel.professor;
		}

		if (
			this.aulaCreateEdit.ambienteId &&
			this.aulaCreateEdit.ambienteId !== aulaFormModel.ambiente
		) {
			this.aulaCreateEdit.ambienteId = aulaFormModel.ambiente;
		}

		if (this.aulaCreateEdit.niveis) {
			this.aulaCreateEdit.niveis = aulaFormModel.niveis.map(
				(n) => ({ id: n } as any)
			);
		}

		this.aulaCreateEdit.horarioInicial = aulaFormModel.horarioInicial.replace(
			":",
			""
		);
		this.aulaCreateEdit.horarioFinal = aulaFormModel.horarioFinal.replace(
			":",
			""
		);
		this.aulaCreateEdit.diaSemana = aulaFormModel.diaAula;
		this.aulaCreateEdit.capacidade = aulaFormModel.capacidade;
		this.aulaCreateEdit.limiteVagasAgregados = +aulaFormModel.limiteAgregados;
		this.aulaCreateEdit.idadeMinimaAnos = aulaFormModel.idadeMinimaAnos;
		this.aulaCreateEdit.idadeMinimaMeses = aulaFormModel.idadeMinimaMeses;
		this.aulaCreateEdit.idadeMaximaAnos = aulaFormModel.idadeMaximaAnos;
		this.aulaCreateEdit.idadeMaximaMeses = aulaFormModel.idadeMaximaMeses;
		this.aulaCreateEdit.toleranciaMin = aulaFormModel.toleranciaCheckinMinutos;
		this.aulaCreateEdit.tipoTolerancia = aulaFormModel.toleranciaCheckinTipo;
		this.aulaCreateEdit.naoValidarModalidadeContrato =
			aulaFormModel.naoValidarModalidadeCheckin;
		this.aulaCreateEdit.mensagem = aulaFormModel.mensagem;
		this.aulaCreateEdit.validarRestricoesMarcacao =
			aulaFormModel.controlarCheckin;
		this.aulaCreateEdit.visualizarProdutosGympass =
			aulaFormModel.aulaDisponivelGympass;
		this.aulaCreateEdit.visualizarProdutosTotalpass =
			aulaFormModel.aulaDisponivelTotalpass;
		// this.aulaCreateEdit.ativo = aulaFormModel.ativo;
	}

	private openModalSuccess() {
		const dialogRef = this.matDialog.open(
			ModalAgendaEditarAulaSuccessComponent,
			{
				panelClass: "modal-agenda-editar-aula-success-window",
				disableClose: true,
				data: {
					aulaId: this.aulaId,
					dataAula: this.dataAula,
					aulaCreateEdit: this.aulaCreateEdit,
				},
			}
		);
		dialogRef.afterClosed().subscribe((result) => {
			console.log(result);
			if (result.acao == "aplicarApenasNestaAula") {
				this.aulaService
					.alterarAulaTemporariamente(
						this.aulaId,
						this.aulaCreateEdit,
						this.dataAula
					)
					.subscribe({
						next: (result) => {
							if (result.erro) {
								this.snotifyService.error(result.message);
							} else {
								this.snotifyService.success("Aula alterada com sucesso!!");
							}
						},
						error: (error) => {
							this.snotifyService.error(
								"Ocorreu um erro desconhecido ao tentar alterar a aula"
							);
						},
					});
			} else if (result.acao == "replicarAlteracoesAula") {
			} else if (result.acao == "aplicarAulasFuturas") {
				const car = result.content;
				this.aulaService
					.alterarAulaTemporariamente(
						this.aulaId,
						this.aulaCreateEdit,
						this.dataAula
					)
					.subscribe({
						next: (result) => {
							if (result.erro) {
								this.snotifyService.error(result.message);
							} else {
								this.replicarAlteracoesAula(car);
							}
						},
						error: (error) => {
							this.snotifyService.error(
								"Ocorreu um erro desconhecido ao tentar alterar a aula"
							);
						},
					});
			}
		});
		this.modal.close();
	}

	private getWeekDayFromDataAula(): string {
		let formattedDate = `${this.dataAula.slice(0, 4)}-${this.dataAula.slice(
			4,
			6
		)}-${this.dataAula.slice(6, 8)} 00:00:00`;
		let date = new Date(formattedDate);

		let dayIndex = date.getDay();

		return this.optionsDiaAula[dayIndex].value;
	}

	replicarAlteracoesAula(result) {
		if (result.tipoAula === "DETERMINADA_DATA") {
			this.aulaCreateEdit.diaLimiteTurmaEdicao = this.datePipe.transform(
				result.dataAplicacao,
				"yyyy/MM/dd"
			);
		}
		this.aulaService
			.replicarAlteracoesAula(this.aulaId, this.aulaCreateEdit, this.dataAula)
			.subscribe({
				next: (result) => {
					if (result.erro) {
						this.snotifyService.error(result.message);
					} else {
						this.snotifyService.success("Alterações replicadas com sucesso!");
					}
				},
				error: (error) => {
					this.snotifyService.error(
						"Ocorreu um erro desconhecido ao tentar replicar as alterações."
					);
				},
			});
	}

	validarValorDigitadoVagasAgregados() {
		let limiteVagas = this.formGroup.get("limiteAgregados").value;
		if (
			limiteVagas !== null &&
			limiteVagas !== undefined &&
			Number(limiteVagas) > 0
		) {
			limiteVagas = limiteVagas.toString().replace(".", "");
			limiteVagas = limiteVagas.replace(",", "");
			limiteVagas = Number(limiteVagas);
		} else {
			limiteVagas = 0;
		}

		let capacidade = this.formGroup.get("capacidade").value;
		if (
			capacidade !== null &&
			capacidade !== undefined &&
			Number(capacidade) > 0
		) {
			capacidade = capacidade.toString().replace(".", "");
			capacidade = capacidade.replace(",", "");
			capacidade = Number(capacidade);
		} else {
			capacidade = 0;
		}

		if (limiteVagas > capacidade) {
			this.formGroup.get("limiteAgregados").setValue(capacidade);
		}
	}
}
