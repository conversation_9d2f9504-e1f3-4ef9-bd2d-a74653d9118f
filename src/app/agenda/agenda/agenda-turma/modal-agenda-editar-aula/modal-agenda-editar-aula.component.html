<form [formGroup]="formGroup">
	<div class="row">
		<ds3-form-field
			class="col-12 col-sm-12 col-md-2 col-lg-2 col-xl-2 campo-nome-ajustado">
			<ds3-field-label>Nome*</ds3-field-label>
			<input ds3Input formControlName="nome" required />
		</ds3-form-field>

		<ds3-form-field
			*ngIf="professores?.length > 0"
			class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label>Professor*</ds3-field-label>
			<ds3-select
				[formControl]="formGroup.get('professor')"
				[nameKey]="'nome'"
				[options]="professores"
				[valueKey]="'id'"
				ds3Input></ds3-select>
		</ds3-form-field>
		<ds3-form-field
			*ngIf="ambientes?.length > 0"
			class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label>Ambiente*</ds3-field-label>
			<ds3-select
				[formControl]="formGroup.get('ambiente')"
				[options]="ambientes"
				ds3Input
				nameKey="nome"
				valueKey="id"></ds3-select>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
			<ds3-field-label>Nível</ds3-field-label>
			<ds3-select-multi
				[endpointUrl]="urlNivel"
				[formControl]="formGroup.get('niveis')"
				ds3Input
				nameKey="nome"
				valueKey="id"></ds3-select-multi>
		</ds3-form-field>
	</div>
	<div class="row">
		<ds3-form-field class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label>Horário inicial*</ds3-field-label>
			<input
				[textMask]="{ mask: timeMask, guide: true }"
				ds3Input
				formControlName="horarioInicial"
				required />
			<i class="pct pct-clock" ds3Suffix></i>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label>Horário final*</ds3-field-label>
			<input
				[textMask]="{ mask: timeMask, guide: true }"
				ds3Input
				formControlName="horarioFinal"
				required />
			<i class="pct pct-clock" ds3Suffix></i>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label>Capacidade*</ds3-field-label>
			<input
				ds3Input
				formControlName="capacidade"
				type="number"
				placeholder="0"
				(input)="validarValorDigitadoVagasAgregados()" />
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label>Limite agregados*</ds3-field-label>
			<input
				ds3Input
				formControlName="limiteAgregados"
				type="number"
				placeholder="0"
				(input)="validarValorDigitadoVagasAgregados()" />
		</ds3-form-field>
	</div>
	<div class="row">
		<ds3-form-field class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label>Idade mínima</ds3-field-label>
			<input ds3Input formControlName="idadeMinimaAnos" mask="999" />
			<span class="typography-overline-bold-2" ds3Suffix>Anos</span>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label></ds3-field-label>
			<input ds3Input formControlName="idadeMinimaMeses" mask="99" />
			<span class="typography-overline-bold-2" ds3Suffix>meses</span>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label>Idade máxima</ds3-field-label>
			<input ds3Input formControlName="idadeMaximaAnos" mask="999" />
			<span class="typography-overline-bold-2" ds3Suffix>Anos</span>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-3 col-lg-3 col-xl-3">
			<ds3-field-label></ds3-field-label>
			<input ds3Input formControlName="idadeMaximaMeses" mask="99" />
			<span class="typography-overline-bold-2" ds3Suffix>meses</span>
		</ds3-form-field>

		<ds3-form-field
			*ngIf="false"
			class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
			<ds3-field-label *ngIf="false">Dia aula*</ds3-field-label>
			<ds3-select
				*ngIf="false"
				[formControl]="formGroup.get('diaAula')"
				[options]="optionsDiaAula"
				ds3Input></ds3-select>
		</ds3-form-field>
	</div>

	<div class="row">
		<ds3-form-field class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
			<ds3-field-label>
				Tolerância para o aluno realizar check-in
			</ds3-field-label>
			<input ds3Input formControlName="toleranciaCheckinMinutos" mask="99" />
			<span class="typography-overline-bold-2" ds3Suffix>minutos</span>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
			<ds3-field-label></ds3-field-label>
			<ds3-select
				[formControl]="formGroup.get('toleranciaCheckinTipo')"
				[options]="tiposTolerancia"
				ds3Input></ds3-select>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
			<ds3-field-label></ds3-field-label>
			<ds3-checkbox
				[formControl]="formGroup.get('naoValidarModalidadeCheckin')"
				ds3Input>
				Não validar modalidade do contrato check-in
			</ds3-checkbox>
		</ds3-form-field>
	</div>
	<div class="row">
		<ds3-form-field class="col-12 col-sm-12 col-md-8 col-lg-8 col-xl-8">
			<ds3-field-label>Mensagem</ds3-field-label>
			<input ds3Input formControlName="mensagem" />
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
			<ds3-field-label></ds3-field-label>
			<ds3-checkbox [formControl]="formGroup.get('controlarCheckin')" ds3Input>
				Controlar check-in
			</ds3-checkbox>
		</ds3-form-field>
	</div>
	<div class="row">
		<ds3-form-field class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
			<ds3-checkbox
				[formControl]="formGroup.get('aulaDisponivelGympass')"
				ds3Input>
				Aula disponível para alunos WellHub?
			</ds3-checkbox>
		</ds3-form-field>
		<ds3-form-field class="col-12 col-sm-12 col-md-4 col-lg-4 col-xl-4">
			<ds3-checkbox
				[formControl]="formGroup.get('aulaDisponivelTotalpass')"
				ds3Input>
				Aula disponível para alunos TotalPass?
			</ds3-checkbox>
		</ds3-form-field>
	</div>
	<div class="mdl-agenda-editar-aula-action-row">
		<!--<ds3-switch class="ml-0 pr-1" formControlName="ativo" labelPosition="after"
					[checked]="formGroup.get('ativo').value">
			Aula ativa
		</ds3-switch>-->
		<button (click)="cancel()" ds3-outlined-button type="button">
			Cancelar
		</button>
		<button (click)="save()" ds3-flat-button>Salvar alterações</button>
	</div>
</form>
