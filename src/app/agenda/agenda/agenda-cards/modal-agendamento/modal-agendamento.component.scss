@import "src/assets/scss/pacto/plataforma-import.scss";

:host {
	display: block;
	padding: 0 30px 30px 30px;
}

.wrapper {
	.row {
		margin-top: 24px;
	}

	.input-nome {
		font-size: 14px;
		font-weight: 700;
		line-height: 18px;
		color: #a1a5aa;
		margin-bottom: 5px;
	}

	.number-input {
		width: 100px !important;
		text-align: center;
		min-width: min-content;
		&::-webkit-outer-spin-button,
		&::-webkit-inner-spin-button {
			-webkit-appearance: none;
		}

		&[type="number"] {
			-moz-appearance: textfield;
			width: 100px !important;
		}
	}
}

.wrapper.validating {
	opacity: 0.2;
	position: relative;
	pointer-events: none;
}

pacto-cat-form-datepicker {
	margin: 0;
}

.actions {
	text-align: right;
	display: block;
	margin-top: 30px;

	span {
		display: inline-block;
	}
}

.error-block {
	width: 100%;
	background-color: #fcedee;
	padding: 14px;
	border-radius: 4px;
	margin-top: 20px;
	margin-bottom: 20px;

	.upper {
		display: flex;
		align-items: center;
		color: #db2c3d;
		font-size: 14px;
		font-weight: 600;
		margin-bottom: 7px;

		i {
			font-size: 20px;
			padding-right: 8px;
		}
	}

	.error-msg {
		font-size: 12px;
		font-weight: 400;

		.professor {
			font-weight: 700;
		}
	}
}

input {
	width: 100%;
	border-radius: 3px;
	border: 1px solid $gelo03;
	@extend .type-p-small-rounded;
	padding: 0px 30px 0px 10px;
	line-height: 40px;
	color: $gelo05;

	outline: 0px !important;

	&:focus {
		box-shadow: 0 0 0 0.2rem rgba($azulimPri, 0.5);
	}

	&.error {
		border-color: $hellboyPri;

		&:focus {
			box-shadow: 0 0 0 0.2rem rgba($hellboyPri, 0.5);
		}
	}

	&:disabled {
		border-color: $cinzaClaroPri !important;
		background-color: $cinzaClaroPri !important;
	}
}

::ng-deep .modal-agendamento .modal-dialog {
	width: 1000px !important;
	max-width: 1000px !important;
}
.radio_dias {
	margin-bottom: 10px;
	margin-right: 3px;
}
.label_dias {
	margin-right: 15px;
}

.number-input-grid {
	grid-template-columns: 26px 0.4fr 26px !important;
}

::ng-deep #repetir-a-cada-select {
	margin-top: 20px !important;
}

::ng-deep .number-input {
	-moz-appearance: textfield;
	width: 100px !important;
	border-color: $cinzaClaroPri !important;
	min-width: 100px !important;

	display: grid !important;
	grid-template-columns: 10px 50px 100px !important;
	gap: 1rem !important;
}

::ng-deep #-increase {
	color: #0380e3 !important;
}

.margin-left-15 {
	margin-left: 15px;
}

.toast {
	padding: 10px;
	border-radius: 4px;

	i {
		margin-right: 6px;
	}
}

.row-dias {
	display: flex;
	margin-top: 8px;

	.item-dia {
		margin-right: 10px;
	}
}
