<pacto-cat-layout-v2>
	<div class="row mb-4" [formGroup]="agendamentoFg">
		<ds3-form-field class="col-md-6">
			<ds3-field-label>Aluno*</ds3-field-label>
			<ds3-select
				ds3Input
				[id]="'alunos-adicionar-agendamento'"
				[formControl]="agendamentoFg.get('aluno')"
				[useFullOption]="true"
				[endpointUrl]="alunoUrl"
				[nameKey]="'nome'"
				[valueKey]="'codigoCliente'"
				(valueChanges)="onValueChange()"
				[responseParser]="resposeParser"
				[paramBuilder]="alunoParamBuilder"></ds3-select>
		</ds3-form-field>

		<ds3-form-field class="col-md-6">
			<ds3-field-label>Professor*</ds3-field-label>
			<ds3-select
				ds3Input
				[nameKey]="'nome'"
				[valueKey]="'id'"
				[id]="'professor-adicionar-agendamento'"
				[formControl]="profFc"
				[options]="professoresDisponiveis"></ds3-select>
		</ds3-form-field>
	</div>
	<div class="row mb-4">
		<ds3-form-field
			class="col-md-4"
			style="outline: none !important"
			[ngClass]="{
				error:
					!agendamentoFg.get('horarioInicial').valid &&
					agendamentoFg.get('horarioInicial').touched
			}">
			<ds3-field-label>Horário inicial</ds3-field-label>
			<input
				ds3Input
				type="text"
				style="outline: none !important; border: none !important"
				[id]="'hora-inicial-adicionar-agendamento'"
				[formControl]="agendamentoFg.get('horarioInicial')"
				[textMask]="{ guide: true, mask: timeMask }" />
		</ds3-form-field>

		<ds3-form-field class="col-md-4">
			<ds3-field-label>Duração (minutos)</ds3-field-label>
			<input
				type="number"
				ds3Input
				[id]="'duracao-adicionar-agendamento'"
				[formControl]="agendamentoFg.get('duracao')"
				required />
			<i class="pct pct-clock" ds3Suffix></i>
		</ds3-form-field>

		<ds3-form-field class="col-md-4">
			<ds3-field-label>Horário final</ds3-field-label>
			<input
				ds3Input
				[textMask]="{ mask: timeMask, guide: true }"
				[formControl]="agendamentoFg.get('horarioFinal')"
				placeholder="00h00"
				required />
		</ds3-form-field>
	</div>
	<div class="row mb-4">
		<ds3-form-field class="col-md-6">
			<ds3-field-label>Comportamento</ds3-field-label>
			<input
				ds3Input
				type="text"
				disabled="true"
				[id]="'comportamento-agendamento'"
				[formControl]="comportamentoFC" />
		</ds3-form-field>

		<ds3-form-field class="col-md-3">
			<ds3-field-label>Data</ds3-field-label>
			<ds3-input-date
				dateType="datepicker"
				disabled="true"
				[control]="diaFC"></ds3-input-date>
		</ds3-form-field>

		<ds3-form-field class="col-md-3" *ngIf="renderRecorrentes">
			<ds3-field-label>Periodicidade</ds3-field-label>
			<ds3-select
				[id]="'tipotolerancia-select'"
				[formControl]="agendamentoFg.get('opcPeriodicidade')"
				[options]="tiposPeriodicidade"
				ds3Input
				[nameKey]="'nome'"
				[valueKey]="'id'"
				(valueChanges)="escolhaPeriodicidade()"></ds3-select>
		</ds3-form-field>
	</div>

	<div class="row">
		<ds3-form-field class="col-md-3 mt-2" *ngIf="showRepetirACada">
			<ds3-field-label>Repetir a cada (Intervalo)</ds3-field-label>
			<ds3-number-field
				ds3Input
				[formControl]="agendamentoFg.get('nrVezes')"
				(decrease)="decreaseValue()"
				[min]="1"
				(increase)="increaseValue()"></ds3-number-field>
		</ds3-form-field>

		<ds3-form-field class="col-md-3 mt-2" *ngIf="showSemanaMes">
			<ds3-field-label>Semana/Mês</ds3-field-label>
			<ds3-select
				[id]="'semana-mes'"
				[formControl]="agendamentoFg.get('opcSemanaOuMes')"
				[options]="semanaMes"
				ds3Input
				[nameKey]="'nome'"
				[valueKey]="'id'"
				(valueChanges)="escolhaSemanaMes()"></ds3-select>
		</ds3-form-field>

		<ds3-form-field class="col-md-3 mt-2" *ngIf="showOpcSemanaMes">
			<ds3-field-label>Semana do mês</ds3-field-label>
			<ds3-select
				[id]="'semana-do-mes'"
				[formControl]="agendamentoFg.get('opcSemanaMes')"
				[options]="tiposSemanaDoMes"
				ds3Input
				[nameKey]="'nome'"
				[valueKey]="'id'"></ds3-select>
		</ds3-form-field>

		<div class="col-md-6 mt-2" *ngIf="showOpcSemanaMes">
			<ds3-field-label>
				Sempre às (dia da semana que irá ocorrer)
			</ds3-field-label>

			<div class="row">
				<div class="col-12 row-dias">
					<ds3-form-field class="item-dia">
						<ds3-checkbox
							[id]="'dia-aula-seg-check'"
							[formControl]="agendamentoFg.get('seg')"
							[title]="'Segunda'"
							(click)="escolherTipoDeAutorizacao('seg')">
							Seg
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field class="item-dia">
						<ds3-checkbox
							[id]="'dia-aula-ter-check'"
							[formControl]="agendamentoFg.get('ter')"
							[title]="'Terça'"
							(click)="escolherTipoDeAutorizacao('ter')">
							Ter
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field class="item-dia">
						<ds3-checkbox
							[id]="'dia-aula-qua-check'"
							[formControl]="agendamentoFg.get('qua')"
							[title]="'Quarta'"
							(click)="escolherTipoDeAutorizacao('qua')">
							Qua
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field class="item-dia">
						<ds3-checkbox
							[id]="'dia-aula-qui-check'"
							[formControl]="agendamentoFg.get('qui')"
							[title]="'Quinta'"
							(click)="escolherTipoDeAutorizacao('qui')">
							Qui
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field class="item-dia">
						<ds3-checkbox
							[id]="'dia-aula-sex-check'"
							[formControl]="agendamentoFg.get('sex')"
							[title]="'Sexta'"
							(click)="escolherTipoDeAutorizacao('sex')">
							Sex
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field class="item-dia">
						<ds3-checkbox
							[id]="'dia-aula-sab-check'"
							[formControl]="agendamentoFg.get('sab')"
							[title]="'Sábado'"
							(click)="escolherTipoDeAutorizacao('sab')">
							Sab
						</ds3-checkbox>
					</ds3-form-field>

					<ds3-form-field class="m-dia">
						<ds3-checkbox
							[id]="'dia-aula-dom-check'"
							[formControl]="agendamentoFg.get('dom')"
							[title]="'Domingo'"
							(click)="escolherTipoDeAutorizacao('dom')">
							Dom
						</ds3-checkbox>
					</ds3-form-field>
				</div>
			</div>
		</div>
	</div>

	<div class="row mt-3" *ngIf="renderTerminaEm">
		<ds3-form-field class="col-md-6">
			<ds3-field-label class="m-0">Terminar em</ds3-field-label>
			<div class="form-check form-check-inline">
				<input
					(click)="escolhaTerminarEm(1)"
					class="radio_dias"
					id="radio_ate"
					name="escolha"
					type="radio" />
				<label for="radio_ate" style="white-space: nowrap">
					<span class="label_dias">Até o final do contrato</span>
				</label>
				<input
					(click)="escolhaTerminarEm(2)"
					class="radio_dias"
					id="radio_em"
					name="escolha"
					type="radio" />
				<label for="radio_em">
					<span class="label_dias">Em</span>
				</label>
			</div>
		</ds3-form-field>

		<ds3-form-field>
			<ds3-field-label>Data término</ds3-field-label>
			<ds3-input-date
				dateType="datepicker"
				[disabled]="showDataTerminar"
				[control]="agendamentoFg.get('dataFim')"
				(keyup)="changeDate()"
				(click)="changeDate()"></ds3-input-date>
		</ds3-form-field>
	</div>
	<div class="row">
		<div class="col-md-12">
			<div class="actions">
				<span>
					<button
						[disabled]="error || !valid || !erroFimContrato"
						id="salvar-adicionar-agendamento"
						ds3-flat-button
						class="margin-left-10px"
						(click)="salvarHandlerRecorrente()">
						Salvar agendamento
					</button>
				</span>
			</div>
		</div>
	</div>

	<div>
		<div *ngIf="msgError(error)" class="error-block">
			<div *ngIf="msgError(error)" class="upper">
				<i class="pct pct-alert-triangle"></i>
				Erro
			</div>
			<div *ngIf="msgError(error)" class="error-msg">
				<span>
					{{ msgError(error) }}
				</span>
			</div>
		</div>

		<div
			*ngIf="
				!msgError(error) &&
				erroDuracao(agendamentoFg.get('duracao').value) &&
				agendamentoFg.get('duracao').touched
			"
			class="error-block">
			<div class="upper">
				<i class="pct pct-alert-triangle"></i>
				Erro
			</div>
			<div class="error-msg">
				<span>
					· Esse tipo de atividade deve ter entre
					{{ tipoAgendamentoDuracao.min }} e {{ tipoAgendamentoDuracao.max }}
					minutos !
				</span>
			</div>
		</div>
	</div>
</pacto-cat-layout-v2>

<ng-template #ambienteIndisponivel>
	<div class="toast bg-light-warning mt-4">
		<i class="pct pct-alert-triangle"></i>
		<span>
			Você não pode agendar este serviço, pois o ambiente onde ele seria
			realizado já está ocupado.
		</span>
	</div>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span
		i18n="@@agendamento-modal-mensagens:create:validacaoalunoagendamento"
		xingling="validacaoalunoagendamento">
		O aluno já realizou o número limite de agendamentos dentro do período de
		dias informado no Tipo de Evento.
	</span>
	<span
		i18n="
			@@agendamento-modal-mensagens:create:validacaoalunoagendamentofaltaemintervalodias"
		xingling="validacaoalunoagendamentofaltaemintervalodias">
		O aluno possui uma falta dentro do intervalo de dias configurado no Tipo de
		Evento.
	</span>
	<span
		i18n="@@agendamento:create:aguardando-confirmacao"
		xingling="AGUARDANDO_CONFIRMACAO">
		Aguardando Confirmação
	</span>
	<span i18n="@@agendamento:create:confirmado" xingling="CONFIRMADO">
		Confirmado
	</span>
	<span i18n="@@agendamento:create:executado" xingling="EXECUTADO">
		Executado
	</span>
	<span i18n="@@agendamento:create:faltou" xingling="FALTOU">Faltou</span>
	<span
		i18n="@@agendamento:valid:plano"
		xingling="VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO">
		O cliente não possui o plano de validação
	</span>
	<span
		i18n="@@agendamento:valid:produto"
		xingling="VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO">
		O cliente não possui o produto de validação
	</span>
	<span
		i18n="@@agendamento:valid:sem:disponibilidade:horario"
		xingling="SEM_DISPONIBILIDADE_HORARIO">
		O professor {{ professor }} não possui disponibilidade neste horário
	</span>
	<span
		i18n="@@agendamento:valid:intervalo:dias"
		xingling="VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS">
		Não foi possivel realizar o agendamento, intervalo de dias atingido
	</span>
	<span
		i18n="@@agendamento:valid:intervalo:dias:faltou"
		xingling="VALIDACAO_AGENDA_ALUNO_INTERVALO_DIAS_FALTOU">
		Não foi possivel realizar o agendamento, intervalo de dias por falta
		atingido
	</span>
	<span i18n="@@agendamento:erro:salvar" xingling="erro_salvar_agendamento">
		Erro ao salvar agendamento. Verifique os dados informados e tente novamente.
	</span>
</pacto-traducoes-xingling>
