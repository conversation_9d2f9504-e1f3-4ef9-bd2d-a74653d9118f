import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import {
	AgendaDisponibilidade,
	AgendamentoTipoDuracao,
	ProfessorSim<PERSON>,
	TreinoApiAgendamentoService,
	ValidacaoAgendamentoErro,
} from "treino-api";
import { merge, Subscription } from "rxjs";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { debounceTime } from "rxjs/operators";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { RestService } from "@base-core/rest/rest.service";
import { AgendaUtilsService } from "@base-core/agenda/agenda-utils.service";
import { AgendaServicosStateService } from "../../agenda-servicos/agenda-servicos-state.service";
import { AgendaCardsStateService } from "../../services/agenda-cards-state.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ModalListaDataIndisponibilidadeComponent } from "../modal-lista-data-indisponibilidade/modal-lista-data-indisponibilidade.component";

declare var moment;

@Component({
	selector: "pacto-modal-agendamento",
	templateUrl: "./modal-agendamento.component.html",
	styleUrls: ["./modal-agendamento.component.scss"],
})
export class ModalAgendamentoComponent implements OnInit {
	@Input() disponibilidade: AgendaDisponibilidade;
	@ViewChild("traducoes", { static: true }) traducoes;

	error: ValidacaoAgendamentoErro;
	step = 0;
	dia;
	professoresDisponiveis: ProfessorSimples[];
	origemDisponibilidade = false;
	isSaving = false;
	placeholder_repetir_a_cada;
	private horarioSub: Subscription;
	private validateTriggerSub: Subscription;
	private validateSub: Subscription;
	private tipoSub: Subscription;
	private profSub: Subscription;
	horarioDisponivelFc = new FormControl();
	diaFC = new FormControl();
	comportamentoFC = new FormControl();
	agendamentoFg = new FormGroup({
		duracao: new FormControl("", [
			Validators.required,
			(fc) => {
				if (this.erroDuracao(fc.value)) {
					return { duracao: true };
				} else {
					return null;
				}
			},
		]),
		horarioInicial: new FormControl("", [
			Validators.required,
			Validators.pattern(/^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/),
		]),
		horarioFinal: new FormControl(),
		aluno: new FormControl("", [Validators.required]),
		observacao: new FormControl(),
		nrVezes: new FormControl(),
		opcPeriodicidade: new FormControl(0),
		opcSemanaMes: new FormControl(),
		opcSemanaOuMes: new FormControl(),
		opcTiposSemanaDoMes: new FormControl(),
		dom: new FormControl(false),
		seg: new FormControl(false),
		ter: new FormControl(false),
		qua: new FormControl(false),
		qui: new FormControl(false),
		sex: new FormControl(false),
		sab: new FormControl(false),
		dataFim: new FormControl(),
	});

	tiposPeriodicidade = [
		{ id: "0", nome: "Não se repete" },
		{ id: "1", nome: "Todos os dias" },
		{ id: "2", nome: "Semanal" },
		{ id: "3", nome: "Mensal" },
		{ id: "4", nome: "Personalizado" },
	];
	tiposSemanaDoMes = [
		{ id: "1", nome: "1° Semana" },
		{ id: "2", nome: "2° Semana" },
		{ id: "3", nome: "3° Semana" },
		{ id: "4", nome: "4° Semana" },
		{ id: "5", nome: "5° Semana" },
	];
	semanaMes = [
		{ id: "1", nome: "Semana (s)" },
		{ id: "2", nome: "Mês (s)" },
	];
	public steps: number = 1;
	public min: number = 1;
	showObservacao = false;
	showDataTerminar = true;
	showRepetirACada = false;
	showSemanaMes = false;
	showOpcSemanaMes = false;
	showDiasDaSemana = false;
	renderRecorrentes = false;
	renderTerminaEm = false;
	erroFimContrato = true;
	tipoFc = new FormControl();
	profFc = new FormControl();
	fimContrato;
	tipoAgendamentoDuracao;
	ambienteId;
	inicio;
	fim;
	resposeParser = (result) => result.content;

	alunoParamBuilder = (filter) => {
		return {
			filters: JSON.stringify({
				quicksearchFields: ["nome"],
				quicksearchValue: filter,
			}),
		};
	};

	erroDuracao(value) {
		if (!this.tipoAgendamentoDuracao) {
			return false;
		}
		if (
			this.tipoAgendamentoDuracao.tipo ===
			AgendamentoTipoDuracao.INTERVALO_DE_TEMPO
		) {
			return (
				value < this.tipoAgendamentoDuracao.min ||
				value > this.tipoAgendamentoDuracao.max
			);
		} else {
			return false;
		}
	}

	get tipoUrl() {
		return this.rest.buildFullUrl("tipos-agendamento");
	}

	get alunoUrl() {
		return this.rest.buildFullUrl("alunos");
	}

	get day() {
		return moment(this.disponibilidade.dia, "YYYYMMDD");
	}

	get professor() {
		if (this.disponibilidade && this.disponibilidade.professor) {
			return this.disponibilidade.professor.nome;
		}
		return "";
	}

	msgError(error: string) {
		if (error) {
			if (error === "SEM_DISPONIBILIDADE_DATA") {
				return (
					"· O professor " +
					this.professor +
					" não possui disponibilidade nesta data"
				);
			} else if (error === "SEM_DISPONIBILIDADE_HORARIO") {
				return (
					"· O professor " +
					this.professor +
					" não possui disponibilidade neste horário"
				);
			} else if (error === "VALIDACAO_AGENDA_ALUNO_PLANO_INVALIDO") {
				return "· O cliente não possui o plano de validação";
			} else if (error === "VALIDACAO_AGENDA_ALUNO_PRODUTO_INVALIDO") {
				return "· O cliente não possui o produto de validação";
			} else {
				return "";
			}
		} else {
			return "";
		}
	}

	get selectedTipoNome() {
		if (this.tipoFc.value) {
			return this.tipoFc.value.nome;
		} else {
			return "";
		}
	}

	get valid() {
		return this.agendamentoFg.valid;
	}

	get timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	get duracaoMask() {
		return [/[0-9]/, /[0-9]/, /[0-9]/];
	}

	ngOnInit() {
		this.setDiaSemanaAtual();
		if (this.disponibilidade) {
			this.fillOutForm();
			this.setupEvents();
		}
		this.placeholder_repetir_a_cada = "Semana (s)";
	}

	ngOnDestroy() {
		this.horarioSub.unsubscribe();
		this.validateTriggerSub.unsubscribe();
		this.tipoSub.unsubscribe();
		if (this.validateSub) {
			this.validateSub.unsubscribe();
		}
	}

	private fillOutForm() {
		this.horarioDisponivelFc.setValue(
			`${this.disponibilidade.horarioInicial} - ${this.disponibilidade.horarioFinal}`,
			{ emitEvent: false }
		);
		this.agendamentoFg
			.get("horarioInicial")
			.setValue(this.disponibilidade.horarioInicial);
		this.updateHorarioFinal();
		this.horarioDisponivelFc.disable({ emitEvent: false });
		this.agendamentoFg.get("horarioFinal").disable({ emitEvent: false });
	}

	private setupTipo(tiposAtividade) {
		const params: any = {
			inicio: this.inicio,
			fim: this.fim,
		};
		if (this.ambienteId !== null) {
			params.ambienteId = this.ambienteId;
		}
		this.agendamentoService
			.tipoAgendamentoDuracao(
				tiposAtividade.id,
				tiposAtividade.tipoEvento,
				params
			)
			.subscribe({
				next: (tipoDuracao) => {
					if (tipoDuracao) {
						this.tipoAgendamentoDuracao = tipoDuracao;
						if (
							tipoDuracao.tipo === AgendamentoTipoDuracao.DURACAO_PREDEFINIDA
						) {
							this.agendamentoFg.get("duracao").disable({ emitEvent: false });
							this.agendamentoFg
								.get("duracao")
								.setValue(tipoDuracao.fixo, { emitEvent: false });
							this.updateHorarioFinal();
						}
					}
					this.updateHorarioFinal();
					this.cd.detectChanges();
				},
				error: (err) => console.log(err),
			});
	}

	private setupEvents() {
		if (
			this.disponibilidade &&
			this.disponibilidade.tiposAtividades &&
			this.disponibilidade.tiposAtividades.length === 1
		) {
			this.tipoFc.setValue(this.disponibilidade.tiposAtividades[0]);
			this.setupTipo(this.disponibilidade.tiposAtividades[0]);
		}
		this.tipoSub = this.tipoFc.valueChanges.subscribe((tipo) => {
			if (tipo) {
				this.setupTipo(tipo);
			}
		});
		this.horarioSub = merge(
			this.agendamentoFg.get("horarioInicial").valueChanges,
			this.agendamentoFg.get("duracao").valueChanges
		).subscribe(() => {
			this.updateHorarioFinal();
		});

		this.validateTriggerSub = merge(
			this.agendamentoFg.get("horarioInicial").valueChanges,
			this.agendamentoFg.get("duracao").valueChanges
		)
			.pipe(debounceTime(750))
			.subscribe(() => {
				const fg = this.agendamentoFg;
				if (fg.get("horarioInicial").valid) {
					this.validate();
				}
			});
	}

	private validate() {
		const dto = this.getDto();
		if (this.validateSub) {
			this.validateSub.unsubscribe();
		}
		this.cd.detectChanges();
		this.validateSub = this.agendamentoService
			.validarCriarAgendamentoV2(dto)
			.subscribe((result) => {
				this.error = result ? result.error : null;
				this.cd.detectChanges();
			});
	}

	private getDto() {
		const dto = this.agendamentoFg.getRawValue();

		if (this.isTreinoIndependente()) {
			dto.professor = this.disponibilidade.professor
				? this.disponibilidade.professor.id
				: null;
		} else {
			dto.professor = this.disponibilidade.professor
				? this.disponibilidade.professor.codigoColaborador
				: null;
		}

		dto.data = this.disponibilidade.dia;
		dto.tipo = this.tipoFc.value.id;
		dto.tipoEvento = this.tipoFc.value.tipoEvento;
		dto.alunoId = dto.aluno ? dto.aluno.id : null;
		dto.horarioDisponibilidadeCod =
			this.disponibilidade.horarioDisponibilidadeCod;
		return dto;
	}

	private updateHorarioFinal() {
		const inicioLabel = this.agendamentoFg.get("horarioInicial").value;
		const duracao = parseInt(this.agendamentoFg.get("duracao").value, 10);
		const inicio = this.agendaUtil.convertTimeLabelIntoMinutes(inicioLabel);
		if (!isNaN(inicio) && !isNaN(duracao)) {
			const final = inicio + duracao;
			const finalLabel = this.agendaUtil.convertMinutesIntoTimeLabel(final);
			this.agendamentoFg
				.get("horarioFinal")
				.setValue(finalLabel, { emitEvent: false });
		}
	}

	constructor(
		private openModal: NgbActiveModal,
		private agendamentoService: TreinoApiAgendamentoService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private rest: RestService,
		private agendaUtil: AgendaUtilsService,
		private state: AgendaServicosStateService,
		private modalService: ModalService,
		private sessionService: SessionService,
		private agendaStateService: AgendaCardsStateService
	) {}

	adicionarObsHandler() {
		this.showObservacao = true;
	}

	cancelarHandler() {
		this.step = 0;
		this.error = null;
		this.tipoFc.reset(null, { emitEvent: false });
	}

	salvarHandler() {
		if (this.isSaving) {
			return;
		}
		this.isSaving = true;

		if (this.valid) {
			const dto = this.getDto();

			this.agendamentoService.criarAgendamento(dto).subscribe(
				(retorno) => {
					this.isSaving = false;
					if (retorno.status === true) {
						if (retorno.error === "obrigatorio.alunoSemSituacaoCompativel") {
							this.snotifyService.error(
								'Aluno(a) inativo/visitante  - Verifique a configuração "Prescrição de treino para aluno".'
							);
						} else if (retorno.error && retorno.error.trim()) {
							this.snotifyService.error(retorno.error);
						} else {
							this.snotifyService.error(
								this.traducoes.getLabel("erro_salvar_agendamento")
							);
						}
						this.cd.detectChanges();
					} else {
						this.openModal.close(dto);
						this.snotifyService.success("Agendamento criado com sucesso");
						this.agendaStateService.forceLoad$.next(true);
					}
					this.isSaving = false;
				},
				(error) => {
					this.isSaving = false;
				}
			);
		} else {
			this.isSaving = false;
			this.agendamentoFg.get("duracao").markAsTouched();
			this.agendamentoFg.get("horarioInicial").markAsTouched();
			this.agendamentoFg.get("aluno").markAsTouched();
			this.agendamentoFg.get("status").markAsTouched();
		}
	}

	salvarHandlerRecorrente() {
		if (!this.renderTerminaEm) {
			this.salvarHandler();
		} else {
			if (this.agendamentoFg.get("opcPeriodicidade").value != 0) {
				if (
					this.agendamentoFg.get("opcPeriodicidade").value == 4 &&
					this.agendamentoFg.get("opcSemanaOuMes").value == null
				) {
					this.isSaving = false;
					this.snotifyService.error("Informe o campo 'Semana/Mês'");
					return;
				}
				if (this.fimContrato == "fimContrato") {
				} else if (this.fimContrato == "dataFim") {
					if (this.agendamentoFg.get("dataFim").value == null) {
						this.isSaving = false;
						this.snotifyService.error("Informe término da recorrência.");
						return;
					}
				} else {
					this.isSaving = false;
					this.snotifyService.error("Preencha o campo 'Terminar em'");
					return;
				}
			}

			if (this.valid) {
				const dto = this.getDto();

				this.agendamentoService.criarAgendamentoRecorrente(dto).subscribe(
					(retorno) => {
						if (!retorno.error) {
							let resultado = "";
							retorno.forEach((dataretorno) => {
								if (
									this.agendamentoFg.get("opcPeriodicidade").value == 3 ||
									this.agendamentoFg.get("opcPeriodicidade").value == 4
								) {
									resultado +=
										", (" +
										moment(dataretorno).format("MMMM") +
										moment(dataretorno).format("/YYYY") +
										")";
								} else {
									resultado +=
										", (" + moment(dataretorno).format("DD/MM/YYYY") + ")";
								}
							});
							if (resultado.length > 0) {
								this.openModal.close(dto);

								const titulo = "Agendar serviço";
								const modal = this.modalService.open(
									titulo,
									ModalListaDataIndisponibilidadeComponent,
									PactoModalSize.LARGE
								);
								modal.componentInstance.listaDatas = resultado.replace(/,/, "");
							} else {
								if (retorno.status === true) {
									// Há erro - o serviço já capturou e formatou
									if (
										retorno.error === "obrigatorio.alunoSemSituacaoCompativel"
									) {
										this.snotifyService.error(
											'Aluno(a) inativo/visitante  - Verifique a configuração "Prescrição de treino para aluno".'
										);
									} else if (retorno.error && retorno.error.trim()) {
										// Exibir a mensagem de erro diretamente (já vem formatada do backend)
										this.snotifyService.error(retorno.error);
									} else {
										// Fallback para mensagem genérica traduzida
										this.snotifyService.error(
											this.traducoes.getLabel("erro_salvar_agendamento")
										);
									}
									this.cd.detectChanges();
								} else {
									this.openModal.close(dto);
									this.snotifyService.success("Agendamento criado com sucesso");
									this.agendaStateService.forceLoad$.next(true);
								}
							}
							this.isSaving = false;
						} else {
							// Exibir a mensagem de erro diretamente (já vem formatada do backend)
							if (retorno.error && retorno.error.trim()) {
								this.snotifyService.error(retorno.error);
							} else {
								this.snotifyService.error(
									this.traducoes.getLabel("erro_salvar_agendamento")
								);
							}
							this.cd.detectChanges();
						}
					},
					(error) => {
						this.isSaving = false;
						// Tratamento de erros HTTP para agendamento recorrente
						console.error("Erro ao criar agendamento recorrente:", error);
						if (error.error && error.error.meta) {
							if (error.error.meta.message) {
								this.snotifyService.error(error.error.meta.message);
							} else if (error.error.meta.error) {
								this.snotifyService.error(
									this.traducoes.getLabel(error.error.meta.error)
								);
							} else {
								this.snotifyService.error(
									this.traducoes.getLabel("erro_salvar_agendamento")
								);
							}
						} else {
							this.snotifyService.error(
								this.traducoes.getLabel("erro_salvar_agendamento")
							);
						}
						this.cd.detectChanges();
					}
				);
			} else {
				this.isSaving = false;
				this.agendamentoFg.get("duracao").markAsTouched();
				this.agendamentoFg.get("horarioInicial").markAsTouched();
				this.agendamentoFg.get("aluno").markAsTouched();
				this.agendamentoFg.get("status").markAsTouched();
			}
		}
	}

	public escolherTipoDeAutorizacao(tipo) {
		if (
			this.agendamentoFg.get("opcPeriodicidade").value == 30 ||
			this.agendamentoFg.get("opcPeriodicidade").value == 40
		) {
			if (tipo == "dom") {
				this.agendamentoFg.get("seg").setValue(false);
				this.agendamentoFg.get("ter").setValue(false);
				this.agendamentoFg.get("qua").setValue(false);
				this.agendamentoFg.get("qui").setValue(false);
				this.agendamentoFg.get("sex").setValue(false);
				this.agendamentoFg.get("sab").setValue(false);
				this.agendamentoFg.get("dom").setValue(true);
			} else if (tipo == "seg") {
				this.agendamentoFg.get("seg").setValue(true);
				this.agendamentoFg.get("ter").setValue(false);
				this.agendamentoFg.get("qua").setValue(false);
				this.agendamentoFg.get("qui").setValue(false);
				this.agendamentoFg.get("sex").setValue(false);
				this.agendamentoFg.get("sab").setValue(false);
				this.agendamentoFg.get("dom").setValue(false);
			} else if (tipo == "ter") {
				this.agendamentoFg.get("seg").setValue(false);
				this.agendamentoFg.get("ter").setValue(true);
				this.agendamentoFg.get("qua").setValue(false);
				this.agendamentoFg.get("qui").setValue(false);
				this.agendamentoFg.get("sex").setValue(false);
				this.agendamentoFg.get("sab").setValue(false);
				this.agendamentoFg.get("dom").setValue(false);
			} else if (tipo == "qua") {
				this.agendamentoFg.get("seg").setValue(false);
				this.agendamentoFg.get("ter").setValue(false);
				this.agendamentoFg.get("qua").setValue(true);
				this.agendamentoFg.get("qui").setValue(false);
				this.agendamentoFg.get("sex").setValue(false);
				this.agendamentoFg.get("sab").setValue(false);
				this.agendamentoFg.get("dom").setValue(false);
			} else if (tipo == "qui") {
				this.agendamentoFg.get("seg").setValue(false);
				this.agendamentoFg.get("ter").setValue(false);
				this.agendamentoFg.get("qua").setValue(false);
				this.agendamentoFg.get("qui").setValue(true);
				this.agendamentoFg.get("sex").setValue(false);
				this.agendamentoFg.get("sab").setValue(false);
				this.agendamentoFg.get("dom").setValue(false);
			} else if (tipo == "sex") {
				this.agendamentoFg.get("seg").setValue(false);
				this.agendamentoFg.get("ter").setValue(false);
				this.agendamentoFg.get("qua").setValue(false);
				this.agendamentoFg.get("qui").setValue(false);
				this.agendamentoFg.get("sex").setValue(true);
				this.agendamentoFg.get("sab").setValue(false);
				this.agendamentoFg.get("dom").setValue(false);
			} else if (tipo == "sab") {
				this.agendamentoFg.get("seg").setValue(false);
				this.agendamentoFg.get("ter").setValue(false);
				this.agendamentoFg.get("qua").setValue(false);
				this.agendamentoFg.get("qui").setValue(false);
				this.agendamentoFg.get("sex").setValue(false);
				this.agendamentoFg.get("sab").setValue(true);
				this.agendamentoFg.get("dom").setValue(false);
			} else if (tipo == "dom") {
				this.agendamentoFg.get("seg").setValue(false);
				this.agendamentoFg.get("ter").setValue(false);
				this.agendamentoFg.get("qua").setValue(false);
				this.agendamentoFg.get("qui").setValue(false);
				this.agendamentoFg.get("sex").setValue(false);
				this.agendamentoFg.get("sab").setValue(false);
				this.agendamentoFg.get("dom").setValue(true);
			}
		}
	}

	escolhaTerminarEm(escolha) {
		if (escolha == 2) {
			this.fimContrato = "dataFim";
			this.showDataTerminar = false;
		} else {
			this.selectAteFimContrato();
			this.fimContrato = "fimContrato";
			this.showDataTerminar = true;
		}
	}

	escolhaPeriodicidade() {
		this.agendamentoFg.get("nrVezes").setValue(0);
		if (this.agendamentoFg.get("opcPeriodicidade").value == 0) {
			// Não se repete
			this.showRepetirACada = false;
			this.showSemanaMes = false;
			this.showDiasDaSemana = false;
			this.renderTerminaEm = false;
			this.showOpcSemanaMes = false;
			this.erroFimContrato = true;
			this.cleanDay();
			this.agendamentoFg.get("dataFim").setValue(null);
		} else if (this.agendamentoFg.get("opcPeriodicidade").value == 1) {
			// Todos os dias
			this.showRepetirACada = false;
			this.showSemanaMes = false;
			this.showDiasDaSemana = false;
			this.showOpcSemanaMes = false;
			this.renderTerminaEm = true;
			this.agendamentoFg.get("seg").setValue(true);
			this.agendamentoFg.get("ter").setValue(true);
			this.agendamentoFg.get("qua").setValue(true);
			this.agendamentoFg.get("qui").setValue(true);
			this.agendamentoFg.get("sex").setValue(true);
			this.agendamentoFg.get("sab").setValue(true);
			this.agendamentoFg.get("dom").setValue(true);
		} else if (this.agendamentoFg.get("opcPeriodicidade").value == 2) {
			// Semanal 2
			this.cleanDay();
			this.setDiaSemanaAtual();
			this.showRepetirACada = false;
			this.showSemanaMes = false;
			this.showDiasDaSemana = true;
			this.agendamentoFg.get("opcSemanaMes").setValue(1);
			this.showOpcSemanaMes = false;
			this.renderTerminaEm = true;
		} else if (this.agendamentoFg.get("opcPeriodicidade").value == 3) {
			// Mensal 3
			this.cleanDay();
			this.setDiaSemanaAtual();
			this.showRepetirACada = false;
			this.showSemanaMes = false;
			this.showDiasDaSemana = true;
			this.showOpcSemanaMes = true;
			this.renderTerminaEm = true;
		} else if (this.agendamentoFg.get("opcPeriodicidade").value == 4) {
			// Personalizado 4
			this.cleanDay();
			this.setDiaSemanaAtual();
			this.agendamentoFg.get("nrVezes").setValue(2);
			this.showRepetirACada = true;
			this.showSemanaMes = true;
			this.showDiasDaSemana = true;
			this.renderTerminaEm = true;
			this.showOpcSemanaMes = false;
		}
	}
	escolhaSemanaMes() {
		if (this.agendamentoFg.get("opcSemanaOuMes").value == 1) {
			// Semana
			this.showOpcSemanaMes = false;
			this.setDiaSemanaAtual();
		} else {
			//Mês
			this.setDiaSemanaAtual();
			this.showOpcSemanaMes = true;
		}
	}

	public increaseValue() {}

	public decreaseValue() {}

	onValueChange() {
		if (this.agendamentoFg.get("aluno").value != "") {
			if (this.agendamentoFg.get("aluno").value.situacaoAluno == "ATIVO") {
				this.renderRecorrentes = true;
				this.cd.detectChanges();
			} else {
				this.renderRecorrentes = false;
			}
		}
	}

	countDiaDaSemana(): number {
		let contador = 0;
		if (this.agendamentoFg.get("dom").value) {
			contador++;
		}
		if (this.agendamentoFg.get("seg").value) {
			contador++;
		}
		if (this.agendamentoFg.get("ter").value) {
			contador++;
		}
		if (this.agendamentoFg.get("qua").value) {
			contador++;
		}
		if (this.agendamentoFg.get("qui").value) {
			contador++;
		}
		if (this.agendamentoFg.get("sex").value) {
			contador++;
		}
		if (this.agendamentoFg.get("sab").value) {
			contador++;
		}
		return contador;
	}

	valdiaQuantodadeEDias(): boolean {
		if (this.countDiaDaSemana() == this.agendamentoFg.get("nrVezes").value) {
			return true;
		} else {
			return false;
		}
	}

	setDiaDaSemana(dia) {
		switch (dia) {
			case 1:
				this.agendamentoFg.get("dom").setValue(true);
				break;
			case 2:
				this.agendamentoFg.get("seg").setValue(true);
				break;
			case 3:
				this.agendamentoFg.get("ter").setValue(true);
				break;
			case 4:
				this.agendamentoFg.get("qua").setValue(true);
				break;
			case 5:
				this.agendamentoFg.get("qui").setValue(true);
				break;
			case 6:
				this.agendamentoFg.get("sex").setValue(true);
				break;
			case 7:
				this.agendamentoFg.get("sab").setValue(true);
				break;
		}
	}

	changeDate() {
		this.erroFimContrato = true;
		const dataFimRecorrencia = new Date(
			this.agendamentoFg.get("dataFim").value
		);
		const dataFimContrato = new Date(
			this.agendamentoFg.getRawValue().aluno.contratoZW.vencimento
		);

		if (dataFimRecorrencia > dataFimContrato) {
			this.snotifyService.error(
				"Data fim da recorrência é maior que o fim do contrato " +
					moment(dataFimContrato).format("DD/MM/YYYY")
			);
			this.agendamentoFg.get("dataFim").setValue(null);
			this.erroFimContrato = false;
		}
	}

	selectAteFimContrato() {
		this.erroFimContrato = true;
		const hoje = new Date();
		const dataFimContrato = new Date(
			this.agendamentoFg.getRawValue().aluno.contratoZW.vencimento
		);

		if (hoje > dataFimContrato) {
			this.snotifyService.error(
				"Data fim da recorrência é maior que o fim do contrato " +
					moment(dataFimContrato).format("DD/MM/YYYY")
			);
			this.agendamentoFg.get("dataFim").setValue(null);
			this.erroFimContrato = false;
		} else {
			this.agendamentoFg.get("dataFim").setValue(dataFimContrato);
		}
	}

	setDiaSemanaAtual() {
		this.cleanDay();
		const today = new Date();
		let dayOfWeek = today.getDay(); // 0 = Domingo, 1 = Segunda, ..., 6 = Sábado
		dayOfWeek = dayOfWeek + 1; // para padronizar

		this.agendamentoFg.get("opcSemanaMes").setValue(this.getWeekOfMonth(today));
		this.setDiaDaSemana(dayOfWeek);
	}

	// Função para obter a semana atual do mês
	getWeekOfMonth(date: Date): number {
		const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
		const dayOfMonth = date.getDate();
		const startDay = startOfMonth.getDay() || 7; // Domingo como 7
		return Math.ceil((dayOfMonth + startDay - 1) / 7);
	}

	cleanDay() {
		this.agendamentoFg.get("seg").setValue(false);
		this.agendamentoFg.get("ter").setValue(false);
		this.agendamentoFg.get("qua").setValue(false);
		this.agendamentoFg.get("qui").setValue(false);
		this.agendamentoFg.get("sex").setValue(false);
		this.agendamentoFg.get("sab").setValue(false);
		this.agendamentoFg.get("dom").setValue(false);
	}

	isTreinoIndependente() {
		return !this.sessionService.integracaoZW;
	}
}
