import { ChangeDetectorRef, Component, OnInit, ViewChild } from "@angular/core";
import { PactoDataGridConfig } from "ui-kit";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ModalAgendamentoComponent } from "../modal-agendamento/modal-agendamento.component";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { Router } from "@angular/router";

@Component({
	selector: "pacto-modal-servicos-disponiveis",
	templateUrl: "./modal-servicos-disponiveis.component.html",
	styleUrls: ["./modal-servicos-disponiveis.component.scss"],
})
export class ModalServicosDisponiveisComponent implements OnInit {
	table: PactoDataGridConfig;
	item;
	@ViewChild("nomeColumn", { static: true }) nomeColumn;
	@ViewChild("comportamentoColumn", { static: true }) comportamentoColumn;
	@ViewChild("professorColumn", { static: true }) professorColumn;
	@ViewChild("horarioColumn", { static: true }) horarioColumn;
	@ViewChild("acoesColumn", { static: true }) acoesColumn;
	@ViewChild("alertaAmbienteOcupadoLocacao", { static: true })
	alertaAmbienteOcupadoLocacao;

	constructor(
		private cd: ChangeDetectorRef,
		private modal: NgbActiveModal,
		private rest: RestService,
		private router: Router,
		private modalService: ModalService
	) {}

	ngOnInit() {
		this.initTable();
	}

	get locacao() {
		return this.item && this.item.tipo === "locacao";
	}

	formatTimestamp(timestamp: number): string {
		const date = new Date(timestamp);

		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, "0");
		const day = String(date.getDate()).padStart(2, "0");
		const hours = String(date.getHours()).padStart(2, "0");
		const minutes = String(date.getMinutes()).padStart(2, "0");
		const seconds = String(date.getSeconds()).padStart(2, "0");

		return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	}

	agendar(item) {
		if (this.locacao) {
			if (item.tipoHorario !== "Play") {
				this.router.navigate(
					["agenda", "painel", "locacao", item.id, item.dia, "0"],
					{
						queryParams: {
							ambiente: item.ambiente,
							horarioInicial: this.item.inicio,
							locacaoNome: item.descricao,
						},
					}
				);
			} else {
				this.router.navigate(
					["agenda", "painel", "detalhamento-locacao-play", item.id, item.dia],
					{
						queryParams: {
							ambienteId: item.ambienteId,
							ambiente: item.ambiente,
							novaLocacao: true,
						},
					}
				);
			}

			this.modal.close();
			return;
		}
		this.modal.close();
		const modal = this.modalService.open(
			"Agendar serviço - " + item.tipo.nome,
			ModalAgendamentoComponent,
			PactoModalSize.LARGE,
			"modal-agendamento"
		);
		modal.componentInstance.disponibilidade = {
			tiposAtividades: item.tiposAtividades,
			horarioInicial: item.horarioInicial,
			professor: item.professor,
			dia: item.dia,
			horarioDisponibilidadeCod: item.horarioDisponibilidadeCod,
		};
		modal.componentInstance.ambienteId = item.ambienteId;
		modal.componentInstance.inicio = this.formatTimestamp(item.inicioDate);
		modal.componentInstance.fim = this.formatTimestamp(item.fimDate);
		const year = parseInt(item.dia.substring(0, 4));
		const month = parseInt(item.dia.substring(4, 6)) - 1; // subtrai 1, pois os meses em Date são 0-based
		const day = parseInt(item.dia.substring(6, 8));
		modal.componentInstance.diaFC.setValue(new Date(year, month, day));
		modal.componentInstance.diaFC.disable();
		modal.componentInstance.profFc.setValue(item.professor);
		modal.componentInstance.comportamentoFC.setValue(
			this.item.servicosDisponiveis[0].comportamento
		);
		modal.componentInstance.comportamentoFC.disable();
		const array = [];
		this.item.servicosDisponiveis.forEach((i) => {
			if (!array.some((item) => item.id === i.professor.id)) {
				array.push(i.professor);
			}
		});
		array.sort((a, b) => a.nome.localeCompare(b.nome));
		modal.componentInstance.professoresDisponiveis = array;
	}

	initTable() {
		let tipo = this.locacao ? 0 : this.item.servicosDisponiveis[0].id;
		let colunas = [];
		let url: string = "";
		if (this.locacao) {
			url = `locacoes/disponibilidades/${
				this.item.diaMes
			}/${this.item.inicio.replace(":", "h")}`;
			colunas = [
				{
					nome: "tipoHorario",
					titulo: "Nome",
					visible: true,
					ordenavel: true,
					celula: this.nomeColumn,
				},
				{
					nome: "ambiente",
					titulo: "Ambiente",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "horario",
					titulo: "Horário",
					visible: true,
					celula: this.horarioColumn,
					ordenavel: true,
				},
				{
					nome: "tipoHorario",
					titulo: "Duração",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "acoes",
					titulo: "Ações",
					visible: true,
					ordenavel: false,
					celula: this.acoesColumn,
				},
			];
		} else {
			tipo = "";
			for (const servico of this.item.servicosDisponiveis) {
				tipo += "," + servico.id;
			}
			tipo = tipo ? tipo.replace(/^,/, "") : "";
			tipo = encodeURIComponent(tipo);
			url = `agenda-cards/disponibilidadesV2/${tipo}/${
				this.item.diaMes
			}/${this.item.inicio.replace(":", "h")}/${this.item.fim.replace(
				":",
				"h"
			)}`;
			colunas = [
				{
					nome: "tipo",
					titulo: "Nome",
					visible: true,
					ordenavel: true,
					celula: this.nomeColumn,
				},
				{
					nome: "comportamento",
					titulo: "Comportamento",
					visible: true,
					ordenavel: true,
					celula: this.comportamentoColumn,
				},
				{
					nome: "professor",
					titulo: "Professor",
					visible: true,
					celula: this.professorColumn,
					ordenavel: true,
				},
				{
					nome: "horario",
					titulo: "Horário",
					visible: true,
					celula: this.horarioColumn,
					ordenavel: true,
				},
				{
					nome: "acoes",
					titulo: "Ações",
					visible: true,
					ordenavel: false,
					celula: this.acoesColumn,
				},
			];
		}

		this.table = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrl(url),
			quickSearch: true,
			pagination: true,
			columns: colunas,
		});
	}
}
