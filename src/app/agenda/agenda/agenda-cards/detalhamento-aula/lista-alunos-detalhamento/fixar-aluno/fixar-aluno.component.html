<div *ngIf="desafixar" class="confirmar-fixar">
	<div class="tipo-desafixacao" *ngIf="!confirm">
		<pacto-cat-select
			[control]="tipoDesafixacao"
			[idKey]="'id'"
			[id]="'select-tipo-desafixar'"
			[items]="tiposDesafixar"
			[labelKey]="'nome'"
			label="Como deseja desafixar o aluno?"></pacto-cat-select>
	</div>

	<span *ngIf="!isDesafixarTodas">
		Tem certeza que deseja desafixar o aluno {{ aluno.nome }}
		<strong>apenas desta aula:</strong>
		{{ aula.nome }}?
	</span>

	<span *ngIf="isDesafixarTodas">
		Tem certeza que deseja desafixar o aluno {{ aluno.nome }}
		<strong>de todas as aulas fixadas</strong>
		?
	</span>

	<div *ngIf="isDesafixarTodas" class="alert-message">
		<i class="pct pct-alert-triangle"></i>
		<span>
			Esta ação removerá o aluno de todas as aulas onde ele está fixado.
		</span>
	</div>

	<div class="btns">
		<pacto-cat-button
			(click)="voltar()"
			[full]="true"
			[id]="'btn-voltar-pagina-aula'"
			[label]="'Não, voltar para página da aula'"
			[type]="'OUTLINE'"></pacto-cat-button>
		<pacto-cat-button
			(click)="confirmar()"
			[full]="true"
			[id]="'btn-fixar-confirmar'"
			[label]="
				isDesafixarTodas
					? 'Sim, desafixar de todas as aulas'
					: 'Sim, confirmar desafixação'
			"></pacto-cat-button>
	</div>
</div>

<div *ngIf="confirm && !desafixar" class="confirmar-fixar">
	<span *ngIf="!dataDeterminada">
		Tem certeza que deseja fixar o aluno na {{ aula.nome }} até o fim do seu
		contrato?
	</span>
	<span *ngIf="dataDeterminada">
		Tem certeza que deseja fixar o aluno na {{ aula.nome }} até
		{{ formDataAte?.value | date : "dd/MM/yyyy" }}?
	</span>

	<div
		*ngIf="validado && aulasCheias && aulasCheias.length > 1"
		class="validado">
		<i class="pct pct-alert-triangle"></i>
		<span>
			Infelizmente, não será possível reservar uma vaga para os dias
			{{ aulasCheias.join(", ") }}
			pois todas as vagas para essas aulas já estão preenchidas.
		</span>
	</div>

	<div
		*ngIf="validado && aulasCheias && aulasCheias.length == 1"
		class="validado">
		<i class="pct pct-alert-triangle"></i>
		<span>
			Infelizmente, não será possível reservar uma vaga para o dia
			{{ aulasCheias }}
			pois todas as vagas para essa aula já estão preenchidas.
		</span>
	</div>

	<div class="btns">
		<pacto-cat-button
			(click)="voltar()"
			[full]="true"
			[id]="'btn-voltar-pagina-aula'"
			[label]="'Não, voltar para página da aula'"
			[type]="'OUTLINE'"></pacto-cat-button>
		<pacto-cat-button
			(click)="confirmar()"
			[full]="true"
			[id]="'btn-fixar-confirmar'"
			[label]="'Sim, confirmar fixação de aluno'"></pacto-cat-button>
	</div>
</div>
<div *ngIf="!confirm && !desafixar" class="como-fixar">
	<pacto-cat-select
		[control]="form"
		[idKey]="'id'"
		[id]="'select-how-fix'"
		[items]="tiposFixar"
		[labelKey]="'nome'"
		i18n-label="@@agenda-detalhes-aula:fixar"
		label="Como deseja fixar esse aluno?"></pacto-cat-select>

	<pacto-cat-datepicker
		*ngIf="dataDeterminada"
		[formControl]="formDataAte"
		[idInput]="'input-data-fixar-ate'"
		[id]="'data-fixar-ate'"
		[label]="'Data de fim'"></pacto-cat-datepicker>

	<div class="btns">
		<pacto-cat-button
			(click)="fixarAluno()"
			[disabled]="notSelected"
			[full]="false"
			[id]="'btn-fixar-aluno'"
			[label]="'Fixar aluno'"></pacto-cat-button>
	</div>
</div>
