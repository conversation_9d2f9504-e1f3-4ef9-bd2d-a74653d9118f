.confirmar-fixar {
	padding: 16px 32px;
	display: grid;
	gap: 16px;

	span {
		font-size: 16px;
		font-weight: 400;
		line-height: 21px;
		color: #000000;
	}

	.tipo-desafixacao {
		margin-bottom: 16px;
	}

	.text-warning {
		color: #f39c12;
		font-style: italic;
		margin-top: 8px;
	}

	.alert-message {
		display: flex;
		align-items: flex-start;
		gap: 8px;
		padding: 12px;
		background-color: #fff3cd;
		border: 1px solid #ffeaa7;
		border-radius: 4px;
		margin-top: 8px;

		i {
			color: #856404;
			font-size: 16px;
			margin-top: 2px;
		}

		span {
			color: #856404;
			font-size: 14px;
			line-height: 1.4;
		}
	}

	.btns {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 16px;
	}
}

.como-fixar {
	padding: 16px 64px;
	display: grid;
	gap: 16px;

	.btns {
		text-align: center;
	}
}

.validado {
	i {
		margin-right: 8px;
		line-height: 16px;
		color: #e10505;
		font-size: 16px;
	}

	span {
		color: #e10505;
	}

	font-size: 12px;
	line-height: 20px;
	padding: 8px 12px;
	background-color: #fee6e6;
	display: flex;
	justify-content: center;
	align-items: center;
}

::ng-deep .modal-fixar .modal-dialog {
	width: 600px !important;
	max-width: 600px !important;
}
