import { DatePipe } from "@angular/common";
import {
	ChangeDetectorRef,
	Component,
	Inject,
	LOCALE_ID,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { SafeHtml } from "@angular/platform-browser";
import { ActivatedRoute, Router } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { LocalizationService } from "@base-core/localization/localization.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { RecursoSistema } from "@base-core/recurso-sistema/recurso-sistema-enum.model";
import { ApiResponseList } from "@base-core/rest/rest.model";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	SnotifyButton,
	SnotifyPosition,
	SnotifyService,
	SnotifyToastConfig,
} from "ng-snotify";
import { SnotifyAnimate } from "ng-snotify/snotify/interfaces/SnotifyAnimate.interface";
import { SnotifyType } from "ng-snotify/snotify/types/snotify.type";
import { DataUrl, NgxImageCompressService } from "ngx-image-compress";
import { Observable, zip } from "rxjs";
import { map } from "rxjs/operators";
import { isNullOrUndefinedOrEmpty } from "sdk";
import {
	Ambiente,
	Aparelho,
	Aula,
	AulaColetiva,
	Horario,
	HorarioTurma,
	Modalidade,
	ModelMap,
	ProductGymPass,
	TipoHorarioLocacaoEnum,
	TreinoApiAmbienteService,
	TreinoApiAparelhoService,
	TreinoApiAulaService,
	TreinoApiColaboradorService,
	TreinoApiModalidadeService,
	TurmaMapaEquipamentoAparelho,
	UsuarioBase,
	Nivel,
} from "treino-api";
import {
	ConfirmDialogDeleteComponent,
	GridFilterConfig,
	GridFilterType,
	PactoDataGridConfig,
	RelatorioComponent,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { TreinoConfigCacheService } from "../../../../base/configuracoes/configuration.service";
import { SeletorImagemAvancadoComponent } from "../../../../treino/atividade/components/seletor-imagem-avancado/seletor-imagem-avancado.component";
import { AgendaCardsStateService } from "../../../agenda/services/agenda-cards-state.service";
import { AmbienteEditModalComponent } from "../../../ambiente/components/ambiente-edit-modal/ambiente-edit-modal.component";
import { ModalidadeEditModalComponent } from "../../../modalidade/components/modalidade-edit-modal/modalidade-edit-modal.component";
import { SelectItemPactoCatSelect } from "../../../turma/components/turma-edit/turma-edit.component";
import { ModalEditHorarioAulaComponent } from "../modal-edit-horario-aula/modal-edit-horario-aula.component";

enum ImageType {
	UPLOAD = "UPLOAD",
}

interface ImagemConfig {
	type?: ImageType;
	id?: string;
	uri: string;
	nome?: string;
	data?: any;
}

@Component({
	selector: "pacto-aula-edit-v2",
	templateUrl: "./aula-edit-v2.component.html",
	styleUrls: ["./aula-edit-v2.component.scss"],
})
export class AulaEditV2Component implements OnInit {
	entity = true;
	operation: string = "create";

	@ViewChild("acaoColumnName", { static: true }) acaoColumnName;
	@ViewChild("buttonName", { static: true }) buttonName;
	@ViewChild("diaColumnName", { static: true }) diaColumnName;
	@ViewChild("codigoColumnName", { static: true }) codigoColumnName;
	@ViewChild("professorColumnName", { static: true }) professorColumnName;
	@ViewChild("duracaoColumnName", { static: true }) duracaoColumnName;
	@ViewChild("horarioColumnName", { static: true }) horarioColumnName;
	@ViewChild("ambienteColumnName", { static: true }) ambienteColumnName;
	@ViewChild("maxAlunosColumnName", { static: true }) maxAlunosColumnName;
	@ViewChild("toleranciaMinColumnName", { static: true })
	toleranciaMinColumnName;
	@ViewChild("tableData", { static: false }) tableData: RelatorioComponent;
	@ViewChild("horarioCelula", { static: true }) horarioCelula;
	@ViewChild("professorCelula", { static: true }) professorCelula;
	@ViewChild("codigoCelula", { static: true }) codigoCelula;
	@ViewChild("ambienteCelula", { static: true }) ambienteCelula;
	@ViewChild("duracaoCelula", { static: true }) duracaoCelula;
	@ViewChild("diaSemanaCelula", { static: true }) diaSemanaCelula;
	@ViewChild("maxAlunosCelula", { static: true }) maxAlunosCelula;
	@ViewChild("toleranciaMinCelula", { static: true }) toleranciaMinCelula;
	@ViewChild("modalidadeLabel", { static: true }) modalidadeLabel;
	@ViewChild("dataInicioLabel", { static: true }) dataInicioLabel;
	@ViewChild("dataFimLabel", { static: true }) dataFimLabel;
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("actionNoUndone", { static: true }) actionNoUndone;

	linkVideos = [];
	modalidades: Array<Modalidade> = [];
	listaNiveis: Array<Nivel> = [];
	productsGymPass: Array<any> = [];
	isProductGymPassLoaded = false;
	professores: Array<UsuarioBase> = [];
	ambientes: Array<Ambiente> = [];
	aparelhosReservaEquipamento: Array<Aparelho> = [];
	tiposTolerancia = [
		{ value: 1, name: "Após o início da aula" },
		{ value: 2, name: "Antes do início da aula" },
	];
	horarios: Array<any> = [];
	showAbasAppPersonalizado: boolean;
	aulaEdit: AulaColetiva;
	horariosNovosAdd: Array<HorarioTurma> = [];
	timerMask = [
		/[0-2]/,
		/[0-9]/,
		":",
		/[0-5]/,
		/[0-9]/,
		" ",
		"-",
		" ",
		/[0-2]/,
		/[0-9]/,
		":",
		/[0-5]/,
		/[0-9]/,
	];
	percentMask = [/[0-9]/, /[0-9]/, "%"];
	textMask = { mask: false };
	textMask1 = { mask: false };
	integracaoZw;
	utilizarSelectProdutoGympass = true;

	images: Array<ImagemConfig> = [];
	loading = false;

	configSnotify: SnotifyToastConfig = new (class implements SnotifyToastConfig {
		animation: SnotifyAnimate;
		backdrop: number;
		bodyMaxLength: number;
		buttons: SnotifyButton[];
		closeOnClick: boolean;
		html: string | SafeHtml;
		icon: string;
		iconClass: string;
		pauseOnHover: boolean;
		placeholder: string;
		position: SnotifyPosition;
		showProgressBar: boolean;
		timeout: number;
		titleMaxLength: number;
		type: SnotifyType;
	})();

	controlHorario: FormControl = new FormControl("");
	formGroup: FormGroup = new FormGroup({
		identificador: new FormControl("", [
			Validators.required,
			Validators.minLength(3),
		]),
		descricao: new FormControl(""),
		cor: new FormControl(null),
		imageUrl: new FormControl(null),
		ocupacao: new FormControl(null),
		modalidadeId: new FormControl(null, [Validators.required]),
		niveis: new FormControl(),
		toleranciaMin: new FormControl(0, [Validators.required]),
		tipoTolerancia: new FormControl(null, [Validators.required]),
		dataInicio: new FormControl(new Date()),
		dataFinal: new FormControl(new Date()),
		meta: new FormControl(null),
		idadeMinimaAnos: new FormControl(0),
		idadeMinimaMeses: new FormControl(0),
		idadeMaximaAnos: new FormControl(0),
		idadeMaximaMeses: new FormControl(0),
		pontuacaoBonus: new FormControl(null),
		bonificacao: new FormControl(null),
		usarBonificacao: new FormControl(null),
		mensagem: new FormControl(null),
		urlVideoYoutube: new FormControl(null),
		validarRestricoesMarcacao: new FormControl(false),
		visualizarProdutosGympass: new FormControl(false),
		visualizarProdutosTotalpass: new FormControl(false),
		permiteFixar: new FormControl(false),
		aulaIntegracaoSelfloops: new FormControl(false),
		naoValidarModalidadeContrato: new FormControl(false),
		produtoGymPass: new FormControl({ value: null, disabled: false }),
		urlTurmaVirtual: new FormControl({ value: null, disabled: false }),
		tipoReservaEquipamento: new FormControl(null, [Validators.required]),
		arquivo: new FormGroup({
			dados: new FormControl(null),
			nome: new FormControl(null),
		}),
		horarios: new FormControl(null),
	});
	formGroupVideosUri: FormGroup = new FormGroup({
		id: new FormControl(),
		linkVideo: new FormControl(""),
		professor: new FormControl(false),
	});
	integradoBookingGympass: any;
	validarModalidadeContrato: any;
	idClasseGymPass: any;
	aplicativo_personalizado_nome: any;
	pactobr = false;
	salvando: boolean = false;
	imgAulaDataUrl: DataUrl = "";
	manterFoto: boolean = true;

	private imagemFoiRemovida = false;

	equipamentos: Array<any> = [
		{ value: "LIVRE", name: "Livre" },
		{ value: "CAPACIDADE_AULA", name: "Mapa de equipamentos" },
	];
	selectedMaps = "";
	showMapaEquipamentos = false;
	mapaReservaEquipaementoSelecionados: Array<ModelMap> = [];
	listaTurmaMapaEquipamentoAparelho: Array<TurmaMapaEquipamentoAparelho> = [];
	filterConfig: GridFilterConfig;
	professoresFiltro: ApiResponseList<UsuarioBase> = {
		content: [],
	};
	ambientesFiltro: ApiResponseList<Ambiente> = {
		content: [],
	};

	table: PactoDataGridConfig;
	page = 1;
	size = 5;
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	horariosData: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};

	constructor(
		@Inject(LOCALE_ID) private locale,
		private localization: LocalizationService,
		private modalidadeService: TreinoApiModalidadeService,
		private aparelhoService: TreinoApiAparelhoService,
		private notificationService: SnotifyService,
		private aulaService: TreinoApiAulaService,
		private route: ActivatedRoute,
		private cd: ChangeDetectorRef,
		private router: Router,
		private datepipe: DatePipe,
		private ambienteService: TreinoApiAmbienteService,
		private colaboradorService: TreinoApiColaboradorService,
		private modal: NgbModal,
		private configCache: TreinoConfigCacheService,
		private snotifyService: SnotifyService,
		private rest: RestService,
		private imageCompress: NgxImageCompressService,
		private session: SessionService,
		private ngbModal: NgbModal,
		private treinoConfigService: TreinoConfigCacheService,
		private agendaStateService: AgendaCardsStateService,
		private modalService: ModalService
	) {}

	backList() {
		this.router.navigate(["agenda", "aula"]);
	}

	ngOnInit() {
		zip(this.getAmbientes(), this.getProfessor()).subscribe(() => {
			this.configFilters();
		});
		if (this.operation === "create") {
			this.formGroup.get("usarBonificacao").setValue("nao");
			this.desabilitarCamposBonificacao();
		}
		this.integracaoZw = this.session.integracaoZW;
		this.configuracoesTreino();
		this.integradoBookingGympass =
			this.configCache.configuracoesIntegracoes.usar_gympass_booking;
		this.validarModalidadeContrato =
			this.configCache.configuracoesAula.validar_modalidade;

		this.setSelects();
		this.textMask.mask = this.localization.getPorcentagemNumberMask();
		this.textMask1.mask = this.localization.getCurrencyMask();
		this.aulaEdit = {
			mapaEquipamentos: "",
			listaMapaEquipamentoAparelho: [],
		};

		this.route.params.subscribe((params) => {
			const id = params.id === "adicionar" ? undefined : params.id;
			this.loadEntities(id);
			if (id) {
				this.operation = "edit";
			} else {
				this.operation = "create";
				this.formGroup.get("produtoGymPass").enable();
			}
		});
		this.pactobr = this.session.loggedUser.username.toLowerCase() === "pactobr";
		this.initTableHorarios();
		this.formGroup.get("arquivo").valueChanges.subscribe((value) => {
			setTimeout(() => {
				const tamanhoMaxImg = 768 * 768 * 1;
				if (
					!this.isTamanhoImagemPermitido(
						this.formGroup.get("arquivo").value.dados,
						tamanhoMaxImg
					)
				) {
					this.obterDataUrlImagem(
						this.formGroup.get("arquivo").value.dados
					).then(() => {
						this.comprimirImagem(tamanhoMaxImg);
					});
				}
			}, 3000);
		});
	}

	handleImageRemoval(): void {
		this.imagemFoiRemovida = true;
		this.formGroup.get("imageUrl").setValue(null);
	}

	private getAmbientes(): Observable<any> {
		const ambientes$ = this.ambienteService.obterTodosAmbientes().pipe(
			map((dados) => {
				// @ts-ignore
				dados.content.forEach((ambiente: any) => {
					ambiente.value = ambiente.id;
					ambiente.label = ambiente.nome;
				});
				this.ambientesFiltro = dados;
				return true;
			})
		);
		return ambientes$;
	}

	private getProfessor(): Observable<any> {
		const professores$ = this.colaboradorService
			.obterTodosColaboradoresAptosAAula()
			.pipe(
				map((dados) => {
					// @ts-ignore
					dados.content.forEach((professor: any) => {
						professor.value = professor.id;
						professor.label = professor.nome;
					});
					this.professoresFiltro = dados;
					return true;
				})
			);
		return professores$;
	}

	private loadEntities(id) {
		if (id) {
			this.aulaService.obterAulaV2(id).subscribe({
				error: (error) => {
					console.error("Erro ao consultar a aula: ", error.error.meta.message);
					this.snotifyService.error(error.error.meta.message);
				},
				next: (aulaResponse) => {
					this.aulaEdit = aulaResponse;
					this.entity = false;

					if (!this.aulaEdit.mapaEquipamentos) {
						this.aulaEdit.mapaEquipamentos = "";
						this.selectedMaps = "";
					} else {
						this.selectedMaps = this.aulaEdit.mapaEquipamentos;
					}

					aulaResponse.linkVideos.forEach((link) => {
						this.linkVideos.push({
							id: link.id,
							linkVideo: link.linkVideo,
							professor: link.professor,
						});
					});

					if (this.aulaEdit.idClasseGymPass != null) {
						this.idClasseGymPass = this.aulaEdit.idClasseGymPass;
					}

					this.loadForm();
					this.initTableHorarios();
					this.cd.detectChanges();
				},
			});
		} else {
			this.formGroup.get("tipoReservaEquipamento").setValue("LIVRE");
			this.changedCapacidade();
			this.initTableHorarios();
			this.cd.detectChanges();
		}
	}

	private getTimeNumber(timeString: string): number {
		const hours = parseInt(timeString.substring(0, 3), 10);
		const minutes = parseInt(timeString.substring(3, 5), 10);

		return hours * 60 + minutes;
	}

	private getEndTime(timeString: string, minutosAdicionar: string): string {
		const hours = parseInt(timeString.substring(0, 3), 10);
		const minutes = parseInt(timeString.substring(3, 5), 10);

		const minutosTotal = hours * 60 + minutes;
		const timeFinal = minutosTotal + this.getTimeNumber(minutosAdicionar);

		const horasFinal = (timeFinal / 60).toFixed(0);
		const minutosFInal = timeFinal % 60;

		const horasFormated =
			horasFinal.length === 1 ? "0" + horasFinal : horasFinal.toString();
		const minutosFormated =
			minutosFInal < 10 ? "0" + minutosFInal : minutosFInal.toString();

		return timeString + " - " + horasFormated + ":" + minutosFormated;
	}

	private loadForm() {
		if (this.aulaEdit) {
			this.formGroup.get("descricao").setValue(this.aulaEdit.descricao);
			this.formGroup.get("identificador").setValue(this.aulaEdit.identificador);
			this.formGroup.get("cor").setValue(this.aulaEdit.cor);
			this.formGroup.get("ocupacao").setValue(this.aulaEdit.ocupacao);
			this.formGroup.get("imageUrl").setValue(this.aulaEdit.imageUrl);
			if (
				this.modalidades.find(
					(item) => this.aulaEdit.modalidadeId === item.id
				) !== undefined
			) {
				this.formGroup.get("modalidadeId").setValue(this.aulaEdit.modalidadeId);
			}
			this.formGroup.get("toleranciaMin").setValue(this.aulaEdit.toleranciaMin);
			this.formGroup
				.get("tipoTolerancia")
				.setValue(this.aulaEdit.tipoTolerancia);
			this.formGroup
				.get("dataInicio")
				.setValue(new Date(this.aulaEdit.dataInicio));
			this.formGroup
				.get("dataFinal")
				.setValue(new Date(this.aulaEdit.dataFinal));
			this.formGroup.get("meta").setValue(this.aulaEdit.meta);
			this.formGroup
				.get("pontuacaoBonus")
				.setValue(this.aulaEdit.pontuacaoBonus);
			this.formGroup.get("bonificacao").setValue(this.aulaEdit.bonificacao);
			this.formGroup.get("mensagem").setValue(this.aulaEdit.mensagem);
			this.formGroup
				.get("validarRestricoesMarcacao")
				.setValue(this.aulaEdit.validarRestricoesMarcacao);
			this.formGroup
				.get("visualizarProdutosGympass")
				.setValue(this.aulaEdit.visualizarProdutosGympass);
			this.formGroup
				.get("visualizarProdutosTotalpass")
				.setValue(this.aulaEdit.visualizarProdutosTotalpass);
			this.formGroup.get("permiteFixar").setValue(this.aulaEdit.permiteFixar);
			this.formGroup
				.get("aulaIntegracaoSelfloops")
				.setValue(this.aulaEdit.aulaIntegracaoSelfloops);
			this.formGroup
				.get("idadeMaximaAnos")
				.setValue(this.aulaEdit.idadeMaximaAnos);
			this.formGroup
				.get("idadeMaximaMeses")
				.setValue(this.aulaEdit.idadeMaximaMeses);
			this.formGroup
				.get("idadeMinimaAnos")
				.setValue(this.aulaEdit.idadeMinimaAnos);
			this.formGroup
				.get("idadeMinimaMeses")
				.setValue(this.aulaEdit.idadeMinimaMeses);
			this.formGroup
				.get("niveis")
				.setValue(this.aulaEdit.niveis.map((a) => a.id));

			if (this.formGroup.get("naoValidarModalidadeContrato") != null) {
				this.formGroup
					.get("naoValidarModalidadeContrato")
					.setValue(this.aulaEdit.naoValidarModalidadeContrato);
			}
			this.formGroup
				.get("produtoGymPass")
				.setValue(
					this.aulaEdit.produtoGymPass === 0 ? "" : this.aulaEdit.produtoGymPass
				);
			this.formGroup
				.get("urlTurmaVirtual")
				.setValue(this.aulaEdit.urlTurmaVirtual);
			this.horarios = this.aulaEdit.horarios;
			if (this.aulaEdit.imageUrl) {
				this.images.push(this.obterImagem());
			}
			if (
				this.aulaEdit.tipoReservaEquipamento !== "LIVRE" &&
				this.aulaEdit.tipoReservaEquipamento !== undefined &&
				this.aulaEdit.tipoReservaEquipamento !== null
			) {
				this.formGroup
					.get("tipoReservaEquipamento")
					.setValue(this.aulaEdit.tipoReservaEquipamento);
			} else {
				this.formGroup.get("tipoReservaEquipamento").setValue("LIVRE");
			}
			this.showMapaEquipamentos =
				this.formGroup.get("tipoReservaEquipamento").value !== "LIVRE";
			if (this.aulaEdit.meta > 0 || this.aulaEdit.bonificacao > 0) {
				this.formGroup.get("usarBonificacao").setValue("sim");
				this.habilitarCamposBonificacao();
			}
			this.changedCapacidade();
			this.cd.detectChanges();
			// this.loadDiasSemana();
		}
	}

	private obterImagem() {
		const img = {
			uri: this.aulaEdit.imageUrl,
		};
		return img;
	}

	private configuracoesTreino() {
		const configuracoes = this.treinoConfigService.configuracoesManutencao;
		this.showAbasAppPersonalizado = configuracoes.aplicativo_personalizado;
		this.aplicativo_personalizado_nome =
			configuracoes.aplicativo_personalizado_nome;
	}

	private setModalidades() {
		this.modalidadeService.obterTodasModalidades().subscribe((dados) => {
			this.modalidades = dados.content;
		});
	}

	private setAmbientes() {
		this.ambienteService.obterTodosAmbientes().subscribe((dados) => {
			this.ambientes = dados.content;
		});
	}

	private setSelects() {
		this.modalidadeService.obterTodasModalidades().subscribe((dados) => {
			const listModalidade = [];
			dados.content.forEach((modalidade) => {
				listModalidade.push({
					value: modalidade.id,
					id: modalidade.id,
					name: modalidade.nome,
				});
			});
			this.modalidades = listModalidade;
			if (this.aulaEdit && this.aulaEdit.modalidadeId) {
				if (
					this.modalidades.find(
						(item) => this.aulaEdit.modalidadeId === item.id
					) !== undefined
				) {
					this.formGroup
						.get("modalidadeId")
						.setValue(this.aulaEdit.modalidadeId);
				}
			}
			this.cd.detectChanges();
		});

		this.colaboradorService
			.obterTodosColaboradoresAptosAAula()
			.subscribe((dados) => {
				this.professores = dados.content;
				this.cd.detectChanges();
			});

		this.ambienteService.obterTodosAmbientes().subscribe((dados) => {
			this.ambientes = dados.content;
			this.cd.detectChanges();
		});

		this.aulaService.obterProdutosGympass().subscribe({
			error: (error) => {
				this.utilizarSelectProdutoGympass = false;
				console.log(
					"Erro ao consultar produtos gympass: ",
					error.error.meta.message
				);
				this.isProductGymPassLoaded = true;
				this.cd.detectChanges();
			},
			next: (dados) => {
				this.povoarProdutoGymPass(dados);
				this.isProductGymPassLoaded = true;
				this.cd.detectChanges();
			},
		});

		this.aparelhoService.obterAparelhosParaReservaEquipamentos().subscribe({
			error: (error) => {
				this.snotifyService.error(error.error.meta.message);
				this.aparelhosReservaEquipamento = [];
				this.cd.detectChanges();
			},
			next: (dados) => {
				this.povoarAparelhosReservaEquipamento(dados);
			},
		});

		this.ambienteService.obterTodosNiveis().subscribe((dados) => {
			const listNiveis = [];
			dados.content.forEach((nivel) => {
				listNiveis.push({
					id: nivel.id,
					nome: nivel.nome,
				});
			});
			this.listaNiveis = listNiveis;
		});
	}

	private povoarProdutoGymPass(produtos) {
		this.productsGymPass.push({ value: 0, name: "" });
		for (const produto of produtos) {
			this.productsGymPass.push({
				value: produto.id,
				name: produto.nome + " (Código: " + produto.id + ")",
			});
		}
	}

	private povoarAparelhosReservaEquipamento(aparelhos) {
		this.aparelhosReservaEquipamento.push({
			id: null,
			nome: "",
			sigla: "",
			icone: "",
			usarEmReservaEquipamentos: true,
			sensorSelfloops: null,
		});

		aparelhos.forEach((aparelho) => {
			this.aparelhosReservaEquipamento.push({
				id: aparelho.id,
				nome: aparelho.nome,
				sigla: aparelho.sigla,
				icone: aparelho.icone,
				usarEmReservaEquipamentos: aparelho.usarEmReservaEquipamentos,
				sensorSelfloops: aparelho.sensorSelfloops,
			});
		});

		this.cd.detectChanges();
	}

	addHorarioHandler(index?: number) {
		if (this.controlHorario.value) {
			this.controlHorario.setValue(
				this.controlHorario.value.replace(/_/g, "0")
			);
			const horario: any = {};

			horario.inicio = this.controlHorario.value.substring(0, 5).trim();
			horario.fim = this.controlHorario.value.substring(8).trim();

			if (this.horarioJaCadastrado(horario.inicio, horario.fim, index)) {
				this.notificationService.error(
					this.notificacoesTranslate.getLabel("validacaoHorarioCadastrado")
				);
			} else if (
				horario.inicio !== horario.fim &&
				this.validarHorario(horario.inicio, horario.fim)
			) {
				if (index !== undefined) {
					this.horarios[index] = horario;
				} else {
					this.horarios.push(horario);
				}
			} else {
				this.notificationService.error(
					this.notificacoesTranslate.getLabel("validacaoHorario")
				);
			}

			this.controlHorario.reset("");
		}
	}

	private horarioJaCadastrado(
		hrInicio: string,
		hrFim: string,
		index?: number
	): boolean {
		const duplicado = this.horarios.some(
			(horario, i) =>
				i !== index &&
				horario.inicio.trim() === hrInicio.trim() &&
				horario.fim.trim() === hrFim.trim()
		);
		return duplicado;
	}

	private validarHorario(hrInicio, hrFim) {
		const primeiroDigitoHorarioInicial = parseInt(hrInicio.substring(0, 1), 10);
		const segundoDigitoHorarioInicial = parseInt(hrInicio.substring(1, 2), 10);

		const primeiroDigitoHorarioFinal = parseInt(hrFim.substring(0, 1), 10);
		const segundoDigitoHorarioFinal = parseInt(hrFim.substring(1, 2), 10);

		if (
			(primeiroDigitoHorarioInicial === 2 && segundoDigitoHorarioInicial > 3) ||
			(primeiroDigitoHorarioFinal === 2 && segundoDigitoHorarioFinal > 3)
		) {
			return false;
		} else if (
			primeiroDigitoHorarioInicial > primeiroDigitoHorarioFinal ||
			(primeiroDigitoHorarioInicial === primeiroDigitoHorarioFinal &&
				segundoDigitoHorarioInicial > segundoDigitoHorarioFinal)
		) {
			return false;
		} else {
			return true;
		}
	}

	removeHorarioHandler(index) {
		this.horarios.splice(index, 1);
	}

	btnClickHandlerModalidade() {
		const modalref = this.modal.open(ModalidadeEditModalComponent);
		modalref.componentInstance.formControl.setValue("");
		modalref.componentInstance.selectedColor = null;
		modalref.result.then((result) => {
			this.modalidadeService
				.criarModalidade(result)
				.subscribe((result1: Modalidade) => {
					this.snotifyService.success(
						this.notificacoesTranslate.getLabel("mensagemCreateSuccess")
					);
					this.formGroup.get("modalidadeId").setValue(result1.id);
					this.setModalidades();
				});
		});
	}

	openLog() {
		const transform = this.datepipe.transform(Date.now(), "ddMMyyyy");
		window.open(
			this.rest.buildFullUrl(
				`gympass/${this.session.chave}/horarios/${this.session.empresaId}/${transform}?idClasse=${this.aulaEdit.codigo}`,
				true
			),
			"_blank"
		);
	}

	markAsTouched() {
		this.formGroup.get("descricao").markAsTouched();
		this.formGroup.get("modalidadeId").markAsTouched();
		this.formGroup.get("toleranciaMin").markAsTouched();
		this.formGroup.get("tipoTolerancia").markAsTouched();
		this.formGroup.get("dataInicio").markAsTouched();
		this.formGroup.get("dataFinal").markAsTouched();
	}

	private editHandler() {
		if (this.formGroup.valid) {
			this.aulaEdit.descricao = this.formGroup.get("descricao").value;
			this.aulaEdit.identificador = this.formGroup.get("identificador").value;
			this.aulaEdit.cor = this.formGroup.get("cor").value;
			this.aulaEdit.meta = this.formGroup.get("meta").value;
			this.aulaEdit.pontuacaoBonus = this.formGroup.get("pontuacaoBonus").value;
			this.aulaEdit.bonificacao = this.formGroup.get("bonificacao").value;
			this.aulaEdit.mensagem = this.formGroup.get("mensagem").value;
			this.aulaEdit.ocupacao = this.formGroup.get("ocupacao").value;
			this.aulaEdit.modalidadeId = this.formGroup.get("modalidadeId").value;
			this.aulaEdit.toleranciaMin = this.formGroup.get("toleranciaMin").value;
			this.aulaEdit.tipoTolerancia = this.formGroup.get("tipoTolerancia").value;
			this.aulaEdit.validarRestricoesMarcacao = this.formGroup.get(
				"validarRestricoesMarcacao"
			).value;
			this.aulaEdit.naoValidarModalidadeContrato = this.formGroup.get(
				"naoValidarModalidadeContrato"
			).value;
			this.aulaEdit.produtoGymPass = this.formGroup.get("produtoGymPass").value;
			this.aulaEdit.idClasseGymPass = this.idClasseGymPass;
			this.aulaEdit.urlTurmaVirtual =
				this.formGroup.get("urlTurmaVirtual").value;
			this.aulaEdit.permiteFixar = this.formGroup.get("permiteFixar").value;
			this.aulaEdit.aulaIntegracaoSelfloops = this.formGroup.get(
				"aulaIntegracaoSelfloops"
			).value;
			this.aulaEdit.visualizarProdutosGympass = this.formGroup.get(
				"visualizarProdutosGympass"
			).value;
			this.aulaEdit.visualizarProdutosTotalpass = this.formGroup.get(
				"visualizarProdutosTotalpass"
			).value;

			const listNiveis: Array<Nivel> = [];
			if (this.formGroup.get("niveis").value) {
				this.formGroup.get("niveis").value.forEach((nivel) => {
					this.listaNiveis.find((item) => {
						if (nivel === item.id) {
							listNiveis.push(item);
						}
					});
				});
			}
			this.aulaEdit.niveis = listNiveis;

			this.aulaEdit.idadeMaximaAnos =
				this.formGroup.get("idadeMaximaAnos").value;
			this.aulaEdit.idadeMaximaMeses =
				this.formGroup.get("idadeMaximaMeses").value;
			this.aulaEdit.idadeMinimaAnos =
				this.formGroup.get("idadeMinimaAnos").value;
			this.aulaEdit.idadeMinimaMeses =
				this.formGroup.get("idadeMinimaMeses").value;
			this.aulaEdit.tipoReservaEquipamento = this.formGroup.get(
				"tipoReservaEquipamento"
			).value;
			this.aulaEdit.dataInicio = new Date(
				this.formGroup.get("dataInicio").value
			)
				.getTime()
				.toString();
			this.aulaEdit.dataFinal = new Date(this.formGroup.get("dataFinal").value)
				.getTime()
				.toString();

			if (this.formGroupVideosUri.get("linkVideo").value !== "") {
				this.linkVideos.push({
					linkVideo: this.formGroupVideosUri.get("linkVideo").value,
					professor: this.formGroupVideosUri.get("professor").value,
				});
			}
			this.aulaEdit.linkVideos = this.linkVideos;

			if (this.selectedMaps && this.selectedMaps.startsWith(";")) {
				this.selectedMaps = this.selectedMaps.slice(1);
			}

			if (this.formGroup.get("tipoReservaEquipamento").value === "LIVRE") {
				this.aulaEdit.mapaEquipamentos = "";
				this.selectedMaps = "";
				this.listaTurmaMapaEquipamentoAparelho = [];
			}

			this.aulaEdit.turmaMapaEquipamentoAparelho =
				this.listaTurmaMapaEquipamentoAparelho;
			this.aulaEdit.listaMapaEquipamentoAparelho = [];
			this.aulaEdit.imageDataUpload =
				this.imgAulaDataUrl !== ""
					? this.imgAulaDataUrl
					: this.formGroup.get("arquivo").value.dados;

			if (this.imagemFoiRemovida && !this.aulaEdit.imageDataUpload) {
				this.aulaEdit.imageUrl = "";
			}

			if (this.validarCamposPreenchidosCorretamente()) {
				this.aulaService
					.atualizarAulaV2(this.aulaEdit.codigo, this.aulaEdit)
					.subscribe({
						error: (error) => {
							this.snotifyService.error(error.error.meta.message);
						},
						next: (dados) => {
							this.notificationService.success(
								this.notificacoesTranslate.getLabel("editSuccess")
							);
							this.cancelHandler();
							this.agendaStateService.forceLoad$.next(true);
						},
					});
			}
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	private validarCamposPreenchidosCorretamente(): boolean {
		let valid = true;
		if (this.aulaEdit.dataInicio > this.aulaEdit.dataFinal) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("validacaoData")
			);
			valid = false;
		}

		const listEquipamentos =
			this.selectedMaps === "" ? [] : this.selectedMaps.split(";");
		if (
			this.formGroup.get("tipoReservaEquipamento").value !== "LIVRE" &&
			listEquipamentos.length < this.getLimiteReservaEquipamento
		) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("validacaoQtEquipamentosReserva")
			);
			valid = false;
		}

		return valid;
	}

	private createListaDiasSemana() {
		const diasSemana = this.formGroup.get("diasSemana").value;
		const diasEscolhidos: Array<string> = [];
		for (const dia in diasSemana) {
			if (diasSemana[dia]) {
				diasEscolhidos.push(dia);
			}
		}
		return diasEscolhidos;
	}

	cancelHandler() {
		this.router.navigate(["agenda", "aula"]);
	}

	private exibirBotaoRemover() {
		return !isNullOrUndefinedOrEmpty(this.aulaEdit.produtoGymPass);
	}

	removerProdutoGymPass() {
		const id = this.aulaEdit.codigo === 0 ? "" : this.aulaEdit.codigo;
		const gympass = this.aulaEdit.produtoGymPass;
		const dialogRef = this.ngbModal.open(ConfirmDialogDeleteComponent, {
			windowClass: "modal-confirmação",
		});
		dialogRef.componentInstance.texto = `Tem certeza que deseja remover esta aula da Gympass ?`;
		dialogRef.componentInstance.textoAlerta = `Caso execute esta ação os alunos já marcados terão suas aulas canceladas e o mesmo sumirá do aplicativo da Gympass`;
		dialogRef.componentInstance.titulo = `Remover aula na Gympass`;
		dialogRef.componentInstance.actionLabel = `Remover`;
		if (dialogRef.result) {
			dialogRef.result
				.then((excluir) => {
					if (excluir) {
						this.aulaService.removerProduto(id).subscribe((response) => {
							this.formGroup.get("produtoGymPass").setValue(undefined);
							this.aulaEdit.produtoGymPass = undefined;
							this.idClasseGymPass = undefined;
							this.notificationService.success("Código Gympass excluido");
						});
					}
				})
				.catch((error) => {});
		}
		this.cd.detectChanges();
	}

	get urlLog() {
		return this.rest.buildFullUrl(`log/aula/${this.aulaEdit.codigo}`);
	}

	get _rest() {
		return this.rest;
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		return response.content;
	};

	removeImageHandle(index) {
		this.images.splice(index, 1);
		this.manterFoto = false;
	}

	get acaoHabilitada() {
		return this.formGroupVideosUri.get("linkVideo").value;
	}

	adicionarHandler() {
		this.linkVideos.push({
			linkVideo: this.formGroupVideosUri.get("linkVideo").value,
			professor: this.formGroupVideosUri.get("professor").value,
		});
		this.formGroupVideosUri.get("linkVideo").setValue("");
		this.formGroupVideosUri.get("professor").setValue(false);
	}

	deletButon(index) {
		this.linkVideos.splice(index, 1);
	}

	checkCheckBoxProfessor(objeto) {
		if (objeto.professor) {
			objeto.professor = false;
		} else {
			objeto.professor = true;
		}
	}

	handleSelectedMaps(selectedMaps) {
		this.mapaReservaEquipaementoSelecionados = selectedMaps;
		const listaPosicoes = this.mapaReservaEquipaementoSelecionados
			.map((item) => item.posicaoMapa)
			.filter(Boolean)
			.join(";");

		this.selectedMaps = listaPosicoes;
		this.aulaEdit.mapaEquipamentos = listaPosicoes;
		this.aulaEdit.listaMapaEquipamentoAparelho =
			this.mapaReservaEquipaementoSelecionados;

		this.listaTurmaMapaEquipamentoAparelho =
			this.transformarModelMapParaTurmaMapaEquipamentoAparelho(
				this.mapaReservaEquipaementoSelecionados
			);
	}

	transformarModelMapParaTurmaMapaEquipamentoAparelho(
		modelMaps: ModelMap[]
	): TurmaMapaEquipamentoAparelho[] {
		const agrupadosPorAparelhoId = new Map<number, string[]>();
		for (const model of modelMaps) {
			if (
				model.aparelhoId !== undefined &&
				model.posicaoMapa &&
				model.aparelhoId > 0
			) {
				if (!agrupadosPorAparelhoId.has(model.aparelhoId)) {
					agrupadosPorAparelhoId.set(model.aparelhoId, []);
				}
				agrupadosPorAparelhoId.get(model.aparelhoId).push(model.posicaoMapa);
			}
		}
		const resultado: TurmaMapaEquipamentoAparelho[] = [];
		agrupadosPorAparelhoId.forEach((posicoes, aparelhoId) => {
			resultado.push({
				codigo_aparelhotreino: aparelhoId,
				mapaequipamento: posicoes.join(";"),
			});
		});

		return resultado;
	}

	changeSelectReservaEquipamento() {
		this.showMapaEquipamentos =
			this.formGroup.get("tipoReservaEquipamento").value !== "LIVRE";
		this.cd.detectChanges();
	}

	changedCapacidade() {
		this.equipamentos = [
			{ value: "LIVRE", name: "Livre" },
			{ value: "CAPACIDADE_AULA", name: "Mapa de equipamentos" },
		];

		this.cd.detectChanges();
	}

	get getLimiteReservaEquipamento() {
		let limite = 0;
		if (
			this.formGroup.get("tipoReservaEquipamento").value === "CAPACIDADE_AULA"
		) {
			limite = 1;
		}
		return limite;
	}

	avancar() {
		if (!this.formGroup.valid) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	alternarBonificacao(usarBonificacao: string): void {
		if (usarBonificacao === "sim") {
			this.habilitarCamposBonificacao();
		} else if (usarBonificacao === "nao") {
			this.desabilitarCamposBonificacao();
		}
	}

	habilitarCamposBonificacao(): void {
		this.formGroup.get("bonificacao").enable();
		this.formGroup.get("meta").enable();
	}

	desabilitarCamposBonificacao(): void {
		this.formGroup.get("bonificacao").disable();
		this.formGroup.get("meta").disable();
	}

	btnClickHandler() {
		this.getModalAdicionarHorario(
			"Cadastro de dias e horários",
			ModalEditHorarioAulaComponent
		);
	}

	getModalAdicionarHorario(title, modalComponent: any) {
		const modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-horario-aula"
		);
		modal.componentInstance.edicao = false;
		modal.componentInstance.podeSelecionarMaisDeUmDia = true;
		modal.componentInstance.ambientes = this.ambientes;
		modal.componentInstance.professores = this.professores;
		modal.result.then(
			(dto) => {
				if (dto) {
					const horarios = [];
					dto.forEach((d) => {
						horarios.push({
							codigo: null,
							responsavel: d.responsavel,
							ambiente: d.ambiente,
							horaInicio: d.horaInicio,
							horaFim: d.horaFim,
							diaSemana: d.diaSemana,
							ativo: true,
							capacidade: d.capacidade,
							dataSaiuTurma: null,
							limiteVagasAgregados: d.limiteVagasAgregados,
							qtdeMaximaAlunoExperimental: d.qtdeMaximaAlunoExperimental,
							liberadoMarcacaoApp: true,
							horarioDisponivelVenda: true,
						});
					});
					if (
						this.aulaEdit &&
						this.aulaEdit.codigo &&
						this.aulaEdit.codigo > 0
					) {
						this.horariosNovosAdd = [];
						horarios.forEach((h) => {
							this.horariosNovosAdd.push(this.horarioTurmaDTO(h));
						});
						this.aulaService
							.saveOrUpdateHorarios(this.horariosNovosAdd)
							.subscribe({
								error: (error) => {
									this.snotifyService.error(error.error.meta.message);
								},
								next: (dados) => {
									this.horariosNovosAdd = [];
									this.tableData.reloadData();
									this.snotifyService.success(
										"Novo horário salvo com sucesso!"
									);
								},
							});
					} else {
						// CRIAR
						horarios.forEach((h) => {
							this.horariosNovosAdd.push(this.horarioTurmaDTO(h));
						});

						if (!this.aulaEdit) {
							this.aulaEdit = { horarios: [] } as AulaColetiva;
							this.aulaEdit.horarios = this.horariosNovosAdd;
						}

						this.formGroup.get("horarios").setValue(this.horariosNovosAdd);
						this.createHorariosPageObject(
							1,
							5,
							true,
							this.horariosNovosAdd,
							true
						);
					}
				}
			},
			() => {}
		);
	}

	createHorariosPageObject(
		page = 1,
		size = 5,
		reloadData = true,
		array = null,
		emitFilters: boolean = true
	) {
		const arrays = array
			? array
			: this.aulaEdit && this.aulaEdit.horarios
			? this.aulaEdit.horarios
			: this.formGroup.get("horarios") && this.formGroup.get("horarios").value
			? this.formGroup.get("horarios").value
			: [];

		this.horariosData.totalElements = arrays.length;
		this.horariosData.size = size;
		this.horariosData.totalPages = Math.ceil(
			+(this.horariosData.totalElements / this.horariosData.size)
		);
		this.horariosData.first = page === 0 || page === 1;
		this.horariosData.last = page === this.horariosData.totalPages;
		this.horariosData.content = arrays.slice(size * page - size, size * page);

		if (this.tableData) {
			if (reloadData) {
				this.tableData.reloadData(emitFilters);
			}
		}
	}

	horarioTurmaDTO(horario): HorarioTurma {
		// AULA E TURMA COMPARTILHAM DA MESMA ESTRUTURA
		let codigoAula = null;
		if (this.aulaEdit && this.aulaEdit.codigo && this.aulaEdit.codigo > 0) {
			codigoAula = this.aulaEdit.codigo;
		}
		return {
			codigo: horario.codigo,
			professor: horario.responsavel.nome,
			professorId: horario.responsavel.codigo,
			ambiente: horario.ambiente.nome,
			ambienteId: horario.ambiente.codigo,
			nivelTurmaId: horario.nivelTurmaId,
			horaInicial: horario.horaInicio,
			horaFinal: horario.horaFim,
			maxAlunos: horario.capacidade,
			limiteVagasAgregados: horario.limiteVagasAgregados,
			qtdeMaximaAlunoExperimental: horario.qtdeMaximaAlunoExperimental,
			toleranciaMin: this.formGroup.get("toleranciaMin").value,
			toleranciaAposMin: 0,
			dataSaiuTurma: horario.dataSaiuTurma,
			situacao:
				horario.dataSaiuTurma && horario.dataSaiuTurma !== "" ? "IN" : "AT",
			turma: codigoAula,
			liberadoMarcacaoApp: horario.liberadoMarcacaoApp,
			horarioDisponivelVenda: horario.horarioDisponivelVenda,
			dia: horario.diaSemana,
			identificadorTurma: this.aulaEdit.descricao,
			horarioCapacidadeCategoria: [],
		};
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.btnEditHandler($event.row);
		}
		if ($event.iconName === "remove") {
			this.btnRemoveHandler($event.row);
		}
	}

	btnEditHandler(item) {
		if (item.codigo !== null && item.codigo > 0) {
			this.validarSePodeEditarHorario(
				"Edição de dias e horário",
				ModalEditHorarioAulaComponent,
				item
			);
		} else {
			this.snotifyService.warning(
				"Este horário ainda não foi registrado. Apenas horários já registrados e com um código podem ser editados!"
			);
		}
	}

	validarSePodeEditarHorario(title, modalComponent: any, item: any) {
		this.aulaService
			.validarExisteAlunoFuturoHorarioAula(item.codigo)
			.subscribe({
				error: (error) => {
					this.snotifyService.error(
						"Não foi possível validar se o horário possui alunos futuros registrados!"
					);
					console.error(error.error.meta.message);
					this.openModalEditarHorario(
						"Edição de dias e horário",
						ModalEditHorarioAulaComponent,
						item
					);
				},
				next: (dados) => {
					if (dados === true || dados === "true") {
						this.snotifyService.error(
							"Há alunos com check-in registrado para este horário em uma data futura. Antes de editar, remova esses alunos para evitar divergências!"
						);
					} else {
						this.openModalEditarHorario(
							"Edição de dias e horário",
							ModalEditHorarioAulaComponent,
							item
						);
					}
				},
			});
	}

	openModalEditarHorario(title, modalComponent: any, item: any) {
		const modal = this.modalService.open(
			title,
			modalComponent,
			PactoModalSize.LARGE,
			"modal-horario-aula"
		);
		modal.componentInstance.edicao = true;
		modal.componentInstance.professores = this.professores;
		modal.componentInstance.ambientes = this.ambientes;
		modal.componentInstance.formGroup.get("codigo").setValue(item.codigo);
		modal.componentInstance.formGroup
			.get("responsavel")
			.setValue(item.professorId);
		modal.componentInstance.formGroup.get("ambiente").setValue(item.ambienteId);
		modal.componentInstance.formGroup
			.get("capacidade")
			.setValue(item.maxAlunos);
		modal.componentInstance.formGroup
			.get("limiteVagasAgregados")
			.setValue(item.limiteVagasAgregados);
		modal.componentInstance.formGroup
			.get("qtdeMaximaAlunoExperimental")
			.setValue(item.qtdeMaximaAlunoExperimental);
		modal.componentInstance.formGroup
			.get("horaInicio")
			.setValue(item.horaInicial);
		modal.componentInstance.formGroup.get("horaFim").setValue(item.horaFinal);
		modal.componentInstance.formGroup.get("diaSemana").setValue(item.dia);

		if (item.dia === "DM") {
			modal.componentInstance.formGroup.get("dom").setValue(true);
		}
		if (item.dia === "SG") {
			modal.componentInstance.formGroup.get("seg").setValue(true);
		}
		if (item.dia === "TR") {
			modal.componentInstance.formGroup.get("ter").setValue(true);
		}
		if (item.dia === "QA") {
			modal.componentInstance.formGroup.get("qua").setValue(true);
		}
		if (item.dia === "QI") {
			modal.componentInstance.formGroup.get("qui").setValue(true);
		}
		if (item.dia === "SX") {
			modal.componentInstance.formGroup.get("sex").setValue(true);
		}
		if (item.dia === "SB") {
			modal.componentInstance.formGroup.get("sab").setValue(true);
		}
		let dataSaiuTurma = "";
		if (item.dataSaiuTurma) {
			dataSaiuTurma = item.dataSaiuTurma;
		}

		modal.result.then(
			(dto) => {
				if (dto.codigo) {
					if (dto) {
						const horario = {
							codigo: dto.codigo,
							responsavel: dto.responsavel,
							ambiente: dto.ambiente,
							horaInicio: dto.horaInicio,
							horaFim: dto.horaFim,
							diaSemana: dto.diaSemana,
							ativo: true,
							capacidade: dto.capacidade,
							dataSaiuTurma: "",
							limiteVagasAgregados: dto.limiteVagasAgregados,
							qtdeMaximaAlunoExperimental: dto.qtdeMaximaAlunoExperimental,
							liberadoMarcacaoApp: item.liberadoMarcacaoApp,
							horarioDisponivelVenda: item.horarioDisponivelVenda,
						};
						horario.dataSaiuTurma = dataSaiuTurma;
						const horarioTurmaEdit: Array<HorarioTurma> = [];
						horarioTurmaEdit.push(this.horarioTurmaDTO(horario));

						this.aulaService.saveOrUpdateHorarios(horarioTurmaEdit).subscribe({
							error: (error) => {
								this.snotifyService.error(error.error.meta.message);
							},
							next: (dados) => {
								this.tableData.reloadData();
								this.snotifyService.success("Horário alterado com sucesso!");
							},
						});
					}
				} else {
				}
			},
			() => {}
		);
	}

	btnRemoveHandler(item) {
		if (this.aulaEdit && item.codigo !== null && item.codigo > 0) {
			this.aulaService.removerHorario(item.codigo).subscribe({
				error: (error) => {
					this.snotifyService.error(error.error.meta.message);
				},
				next: () => {
					this.snotifyService.success("Horário inativado com sucesso!");
					this.tableData.reloadData();
				},
			});
		} else {
			const index = this.formGroup.get("horarios").value.indexOf(item, 0);
			if (index > -1) {
				const horarioIndex = this.horariosData.content.findIndex(
					(h) => h === item
				);
				const horarioFGIndex = this.formGroup
					.get("horarios")
					.value.findIndex((h) => h === item);
				if (horarioIndex > -1 && horarioFGIndex > -1) {
					this.horariosData.content.splice(horarioIndex, 1);
					this.formGroup.get("horarios").value.splice(horarioFGIndex, 1);
					this.cd.detectChanges();
				}
				this.createHorariosPageObject(this.page, this.size, true);
			}
		}
	}

	private initTableHorarios() {
		let codigoAula = 0;
		if (this.aulaEdit && this.aulaEdit.codigo && this.aulaEdit.codigo > 0) {
			codigoAula = this.aulaEdit.codigo;
		}
		setTimeout(() => {
			this.table = new PactoDataGridConfig({
				endpointUrl: this.rest.buildFullUrl(
					`aulas/obter-horarios/${codigoAula}`
				),
				logUrl: this.rest.buildFullUrl(
					`log/horarioTurma/${
						this.aulaEdit && this.aulaEdit.codigo > 0
							? this.aulaEdit.codigo
							: ""
					}`
				),
				quickSearch: false,
				ghostLoad: true,
				ghostAmount: 5,
				dataAdapterFn: (serverData) => {
					if (codigoAula > 0) {
						return serverData;
					} else {
						return this.horariosData;
					}
				},
				showFilters: true,
				columns: [
					{
						nome: "codigo",
						titulo: this.codigoColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: true,
						defaultVisible: false,
						campo: "codigo",
						celula: this.codigoCelula,
					},
					{
						nome: "professor",
						titulo: this.professorColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "professor",
						celula: this.professorCelula,
					},
					{
						nome: "ambiente",
						titulo: this.ambienteColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "ambiente",
						celula: this.ambienteCelula,
					},
					{
						nome: "horario",
						titulo: this.horarioColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: false,
						defaultVisible: true,
						campo: "horario",
						celula: this.horarioCelula,
					},
					{
						nome: "duracao",
						titulo: this.duracaoColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: false,
						defaultVisible: true,
						campo: "duracao",
						celula: this.duracaoCelula,
					},
					{
						nome: "dia",
						titulo: this.diaColumnName,
						buscaRapida: false,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "dia",
						celula: this.diaSemanaCelula,
					},
					{
						nome: "maxAlunos",
						titulo: this.maxAlunosColumnName,
						buscaRapida: true,
						visible: true,
						ordenavel: true,
						defaultVisible: true,
						campo: "maxAlunos",
						celula: this.maxAlunosCelula,
					},
					{
						nome: "dataSaiuTurma",
						titulo: "Data Saiu",
						buscaRapida: false,
						visible: true,
						ordenavel: false,
						defaultVisible: false,
						campo: "dataSaiuTurma",
					},
				],
				actions: [
					{
						nome: "edit",
						iconClass: "pct pct-edit",
						showIconFn: (row) => this.podeEditarHorario(row),
					},
					{
						nome: "remove",
						iconClass: "pct pct-trash-2",
						tooltipText: "Inativar",
						showIconFn: (row) => this.podeInativarHorario(row),
					},
				],
			});
			this.cd.detectChanges();
		});
		if (this.tableData) {
			this.tableData.pageSizeControl.setValue(5);
		}
	}

	private podeEditarHorario(row: any) {
		return row.codigo !== null && row.codigo > 0;
	}

	private podeInativarHorario(row: any) {
		// se código = null indica que é um horário novo que ainda não foi registrado em banco e pode ser removido
		// se código != null e possui dataSaiuTurma, indica que é um horário que já foi inativado
		return row.codigo === null || (row.codigo !== null && !row.dataSaiuTurma);
	}

	private configFilters() {
		this.filterConfig = {
			filters: [
				{
					name: "situacao",
					label: "Situação",
					type: GridFilterType.DS3_SELECT_MANY,
					options: [
						{ value: "ATIVO", label: "Ativo" },
						{ value: "INATIVO", label: "Inativo" },
					],
					initialValue: ["ATIVO"],
				},
				{
					name: "professor",
					label: "Professor",
					type: GridFilterType.DS3_SELECT_MANY,
					options: this.professoresFiltro.content.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
				},
				{
					name: "ambiente",
					label: "Ambiente",
					type: GridFilterType.DS3_SELECT_MANY,
					options: this.ambientesFiltro.content.map((item) => ({
						value: item.id,
						label: item.nome,
					})),
				},
				{
					name: "dias",
					label: "Dia",
					type: GridFilterType.DS3_SELECT_MANY,
					options: [
						{ value: "SG", label: "SG" },
						{ value: "TR", label: "TR" },
						{ value: "QA", label: "QA" },
						{ value: "QI", label: "QI" },
						{ value: "SX", label: "SX" },
						{ value: "SB", label: "SB" },
						{ value: "DM", label: "DM" },
					],
				},
			],
		};
		this.cd.detectChanges();
	}

	private getSelectItemsPactoCatSelect(
		items: any
	): Array<SelectItemPactoCatSelect> {
		const selectItems = new Array<SelectItemPactoCatSelect>();
		items.forEach((d) => {
			const selectItem = new SelectItemPactoCatSelect();
			selectItem.value = d;
			selectItem.label = d;
			selectItems.push(selectItem);
		});
		return selectItems;
	}

	salvarAula() {
		if (!this.formGroup.valid) {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		} else {
			if (this.operation === "edit") {
				this.editHandler();
			} else {
				this.createHandler();
				this.session.notificarRecursoEmpresa(
					RecursoSistema.CRIOU_AULACOLETIVA_NTO
				);
			}
		}
	}

	private createHandler() {
		if (this.formGroup.valid) {
			this.aulaEdit.codigo = null;
			this.aulaEdit.descricao = this.formGroup.get("descricao").value;
			this.aulaEdit.identificador = this.formGroup.get("identificador").value;
			this.aulaEdit.cor = this.formGroup.get("cor").value;
			this.aulaEdit.meta = this.formGroup.get("meta").value;
			this.aulaEdit.pontuacaoBonus = this.formGroup.get("pontuacaoBonus").value;
			this.aulaEdit.bonificacao = this.formGroup.get("bonificacao").value;
			this.aulaEdit.mensagem = this.formGroup.get("mensagem").value;
			this.aulaEdit.ocupacao = this.formGroup.get("ocupacao").value;
			this.aulaEdit.modalidadeId = this.formGroup.get("modalidadeId").value;
			this.aulaEdit.toleranciaMin = this.formGroup.get("toleranciaMin").value;
			this.aulaEdit.tipoTolerancia = this.formGroup.get("tipoTolerancia").value;
			this.aulaEdit.validarRestricoesMarcacao = this.formGroup.get(
				"validarRestricoesMarcacao"
			).value;
			this.aulaEdit.naoValidarModalidadeContrato = this.formGroup.get(
				"naoValidarModalidadeContrato"
			).value;
			this.aulaEdit.produtoGymPass = this.formGroup.get("produtoGymPass").value;
			this.aulaEdit.idClasseGymPass = this.idClasseGymPass;
			this.aulaEdit.urlTurmaVirtual =
				this.formGroup.get("urlTurmaVirtual").value;
			this.aulaEdit.permiteFixar = this.formGroup.get("permiteFixar").value;
			this.aulaEdit.aulaIntegracaoSelfloops = this.formGroup.get(
				"aulaIntegracaoSelfloops"
			).value;
			this.aulaEdit.visualizarProdutosGympass = this.formGroup.get(
				"visualizarProdutosGympass"
			).value;
			this.aulaEdit.visualizarProdutosTotalpass = this.formGroup.get(
				"visualizarProdutosTotalpass"
			).value;
			const listNiveis: Array<Nivel> = [];
			if (this.formGroup.get("niveis").value) {
				this.formGroup.get("niveis").value.forEach((nivel) => {
					this.listaNiveis.find((item) => {
						if (nivel === item.id) {
							listNiveis.push(item);
						}
					});
				});
			}
			this.aulaEdit.niveis = listNiveis;
			this.aulaEdit.idadeMaximaAnos =
				this.formGroup.get("idadeMaximaAnos").value;
			this.aulaEdit.idadeMaximaMeses =
				this.formGroup.get("idadeMaximaMeses").value;
			this.aulaEdit.idadeMinimaAnos =
				this.formGroup.get("idadeMinimaAnos").value;
			this.aulaEdit.idadeMinimaMeses =
				this.formGroup.get("idadeMinimaMeses").value;
			this.aulaEdit.tipoReservaEquipamento = this.formGroup.get(
				"tipoReservaEquipamento"
			).value;
			this.aulaEdit.dataInicio = new Date(
				this.formGroup.get("dataInicio").value
			)
				.getTime()
				.toString();
			this.aulaEdit.dataFinal = new Date(this.formGroup.get("dataFinal").value)
				.getTime()
				.toString();

			if (this.formGroupVideosUri.get("linkVideo").value !== "") {
				this.linkVideos.push({
					linkVideo: this.formGroupVideosUri.get("linkVideo").value,
					professor: this.formGroupVideosUri.get("professor").value,
				});
			}
			this.aulaEdit.linkVideos = this.linkVideos;

			if (this.selectedMaps && this.selectedMaps.startsWith(";")) {
				this.selectedMaps = this.selectedMaps.slice(1);
			}

			if (this.formGroup.get("tipoReservaEquipamento").value === "LIVRE") {
				this.aulaEdit.mapaEquipamentos = "";
				this.selectedMaps = "";
				this.listaTurmaMapaEquipamentoAparelho = [];
			}

			this.aulaEdit.turmaMapaEquipamentoAparelho =
				this.listaTurmaMapaEquipamentoAparelho;
			this.aulaEdit.listaMapaEquipamentoAparelho = [];
			this.aulaEdit.horarios = this.horariosNovosAdd;
			this.aulaEdit.imageDataUpload = this.formGroup.get("arquivo").value.dados;

			if (this.validarCamposPreenchidosCorretamente()) {
				this.salvando = true;
				this.aulaService.criarAulaV2(this.aulaEdit).subscribe({
					error: (error) => {
						this.snotifyService.error(error.error.meta.message);
						this.salvando = false;
					},
					next: (dados) => {
						this.salvando = false;
						this.notificationService.success(
							this.notificacoesTranslate.getLabel("createSuccess")
						);
						this.cancelHandler();
						this.agendaStateService.forceLoad$.next(true);
					},
				});
			}
		} else {
			this.notificationService.error(
				this.notificacoesTranslate.getLabel("campoObrigatorio")
			);
		}
	}

	obterDuracao(item: any) {
		const [horaInicial, minutoInicial] = item.horaInicial
			.split(":")
			.map(Number);
		const [horaFinal, minutoFinal] = item.horaFinal.split(":").map(Number);

		const inicioEmMinutos = horaInicial * 60 + minutoInicial;
		const fimEmMinutos = horaFinal * 60 + minutoFinal;

		const duracaoEmMinutos = fimEmMinutos - inicioEmMinutos;
		const horas = Math.floor(duracaoEmMinutos / 60);
		const minutos = duracaoEmMinutos % 60;

		return `${horas}h ${minutos}m`;
	}

	obterDiaSemana(item: any) {
		const dias = {
			DM: "Dom",
			SG: "Seg",
			TR: "Ter",
			QA: "Qua",
			QI: "Qui",
			SX: "Sex",
			SB: "Sab",
		};
		return dias[item.dia] || "";
	}

	pageChangeEvent(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createHorariosPageObject(this.page, this.size, true);
	}

	pageSizeChange(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.page = 1;
		}
		this.createHorariosPageObject(this.page, this.size, true);
	}

	ordenarHorarios(eventSort) {
		if (this.aulaEdit) {
			this.aulaEdit.horarios = this.sortList(
				this.aulaEdit.horarios,
				eventSort.columnName,
				eventSort.direction
			);
			this.createHorariosPageObject(this.page, this.size, true);
		}
	}

	sortList(
		list: Array<any>,
		columnName: string,
		direction: string
	): Array<any> {
		list = list.sort((a, b) => {
			if (direction === "ASC") {
				if (
					a[columnName] > b[columnName] ||
					a[columnName].nome > b[columnName].nome
				) {
					return 1;
				} else if (
					a[columnName] < b[columnName] ||
					a[columnName].nome < b[columnName].nome
				) {
					return -1;
				} else {
					return 0;
				}
			} else {
				if (
					a[columnName] < b[columnName] ||
					a[columnName].nome < b[columnName].nome
				) {
					return 1;
				} else if (
					a[columnName] > b[columnName] ||
					a[columnName].nome > b[columnName].nome
				) {
					return -1;
				} else {
					return 0;
				}
			}
		});
		return list;
	}

	comprimirImagem(tamanhoMaxImg) {
		console.log(
			"Tamanho da imagem antes de ser comprimida:  ",
			this.imgAulaDataUrl.length
		);
		if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
			this.imageCompress
				.compressFile(this.imgAulaDataUrl, 1, 50, 50)
				.then((result: DataUrl) => {
					this.imgAulaDataUrl = result;
					this.formGroup.get("arquivo").value.dados = result;
					if (this.imgAulaDataUrl.length > tamanhoMaxImg) {
						this.comprimirImagem(tamanhoMaxImg);
					}
				});
		}
	}

	obterDataUrlImagem(image): Promise<void> {
		return new Promise<void>((resolve, reject) => {
			const base64String = image; // (sua string completa aqui)
			const file = this.base64ToFile(base64String, "obterDataUrlImagem.jpg");
			const reader = new FileReader();
			reader.onload = () => {
				this.imgAulaDataUrl = reader.result as string;
				resolve();
			};
			reader.onerror = (error) => reject(error);
			if (file) {
				reader.readAsDataURL(file);
			}
		});
	}

	private isTamanhoImagemPermitido(image, tamanhoMax: number) {
		const base64String = image; // (sua string completa aqui)
		const file = this.base64ToFile(
			base64String,
			"isTamanhoImagemPermitido.jpg"
		);
		if (file) {
			if (file.size > tamanhoMax) {
				return false;
			}
		}
		return true;
	}

	base64ToFile(dataUrl: string, filename: string): File {
		const arr = dataUrl.split(",");
		const mimeMatch = arr[0].match(/:(.*?);/);
		if (!mimeMatch) {
			throw new Error("Tipo MIME não encontrado.");
		}
		const mime = mimeMatch[1];
		const bstr = atob(arr[1]);
		let n = bstr.length;
		const u8arr = new Uint8Array(n);
		while (n--) {
			u8arr[n] = bstr.charCodeAt(n);
		}
		return new File([u8arr], filename, { type: mime });
	}
}
