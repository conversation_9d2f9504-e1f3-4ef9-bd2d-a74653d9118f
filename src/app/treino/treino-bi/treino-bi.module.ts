import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule, Routes } from "@angular/router";

import { BaseSharedModule } from "@base-shared/base-shared.module";
import { IndicadoresGridComponent } from "./components/indicadores-grid/indicadores-grid.component";
import { TreinoBiHomeV2Component } from "./components/treino-bi-home-v2/treino-bi-home-v2.component";
import { TreinoBiProfessorComponent } from "./components/treino-bi-professor/treino-bi-professor.component";
import { TreinoBiMovimentacaoAlunosComponent } from "./components/treino-bi-movimentacao-alunos/treino-bi-movimentacao-alunos.component";
import { TreinoBiAgendaDisponibilidadeComponent } from "./components/treino-bi-agenda-disponibilidade/treino-bi-agenda-disponibilidade.component";
import { TreinoBiProfessorAgendamentoComponent } from "./components/treino-bi-professor-agendamento/treino-bi-professor-agendamento.component";
import { TreinoBiStateService } from "./components/treino-bi-home-v2/treino-bi-state.service";
import { RelatorioComponent } from "ui-kit";
import { CustomBiPanelComponent } from "./components/custom-bi-panel/custom-bi-panel.component";
import { CustomViewModalComponent } from "./components/custom-view-modal/custom-view-modal.component";
import { BiAppComponent } from "../treino-gestao/alunos-app/bi-app/bi-app.component";
import { AgendaBiStateService } from "../../agenda/agenda/agenda-bi/agenda-bi-state";
import { GestaoGeralGuard } from "@base-core/guards/gestao-geral.guard";
import { TreinoBiAvaliacaoMediaTreinoComponent } from "./components/treino-bi-avaliacao-media-treino/treino-bi-avaliacao-media-treino.component";
import { StarComponent } from "./components/star/star.component";
import { ModalAcompanharTreinoComponent } from "./components/modal-acompanhar-treino/modal-acompanhar-treino.component";
import { HintModule } from "../../hint/hint.module";
import { DetalhesModalComponent } from "./components/treino-bi-movimentacao-alunos/detalhes-modal/detalhes-modal.component";
import { TreinoBiAvaliacaoAcompanhamentoComponent } from "./components/treino-bi-avaliacao-acompanhamento/treino-bi-avaliacao-acompanhamento.component";
import { TreinoBiAvaliacaoProfessorComponent } from "./components/treino-bi-avaliacao-professor/treino-bi-avaliacao-professor.component";

const routes: Routes = [
	{
		path: "",
		component: TreinoBiHomeV2Component,
	},
	{
		path: "dashboard",
		component: TreinoBiHomeV2Component,
	},
	{
		path: "personalizado",
		component: TreinoBiHomeV2Component,
	},
	{
		path: "alunos-app",
		component: BiAppComponent,
		canActivate: [GestaoGeralGuard],
	},
];

@NgModule({
	imports: [
		RouterModule.forChild(routes),
		CommonModule,
		BaseSharedModule,
		HintModule,
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
	entryComponents: [
		DetalhesModalComponent,
		RelatorioComponent,
		CustomViewModalComponent,
		ModalAcompanharTreinoComponent,
	],
	declarations: [
		DetalhesModalComponent,
		BiAppComponent,
		IndicadoresGridComponent,
		TreinoBiHomeV2Component,
		TreinoBiProfessorComponent,
		TreinoBiMovimentacaoAlunosComponent,
		TreinoBiAgendaDisponibilidadeComponent,
		TreinoBiProfessorAgendamentoComponent,
		CustomBiPanelComponent,
		CustomViewModalComponent,
		TreinoBiAvaliacaoMediaTreinoComponent,
		StarComponent,
		ModalAcompanharTreinoComponent,
		TreinoBiAvaliacaoAcompanhamentoComponent,
		TreinoBiAvaliacaoProfessorComponent,
	],
	exports: [
		TreinoBiAgendaDisponibilidadeComponent,
		TreinoBiProfessorAgendamentoComponent,
		TreinoBiProfessorAgendamentoComponent,
		ModalAcompanharTreinoComponent,
	],
	providers: [TreinoBiStateService, AgendaBiStateService],
})
export class TreinoBiModule {}
