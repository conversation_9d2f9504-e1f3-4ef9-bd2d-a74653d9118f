import { Injectable } from "@angular/core";
import {
	TreinoBiTreinamento,
	TreinoBiCarteira,
	TreinoBiAvaliacaoTreino,
	TreinoBiAvaliacaoAcompanhamento,
	TreinoBiAvaliacaoProfessor,
} from "@treino-core/treino-bi/treino-bi2.model";
import { BehaviorSubject } from "rxjs";
import { TreinoBiAgenda, UsuarioBase } from "treino-api";

@Injectable()
export class TreinoBiStateService {
	constructor() {}

	agenda: TreinoBiAgenda;
	treinamento: TreinoBiTreinamento;
	carteira: TreinoBiCarteira;
	colaboradores: UsuarioBase[] = [];
	avaliacaoTreino: TreinoBiAvaliacaoTreino;
	avaliacaoAcompanhamento: TreinoBiAvaliacaoAcompanhamento;
	avaliacaoProfessor: TreinoBiAvaliacaoProfessor;

	update$: BehaviorSubject<any> = new BehaviorSubject(null);
}
