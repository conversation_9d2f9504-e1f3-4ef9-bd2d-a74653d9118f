<pacto-cat-layout-v2 id="treino-bi">
	<div [ngClass]="{ 'loading-blur': loading }" class="bi-wrapper">
		<div class="top-wrapper">
			<div class="type-h2 indicadores-titulo">
				<ng-container i18n="@@treino-bi:indicadores">
					Business Intelligence
				</ng-container>

				<span *ngIf="professorSelected && dash">
					- {{ professorSelected.nome | lowercase }}
				</span>
				<div class="update-indicadores">
					<span *ngIf="dash">
						<ng-container i18n="treino-bi:ultima-atualizacao:data">
							Última atualização:
						</ng-container>
						{{ lastUpdate }}
					</span>
				</div>
			</div>

			<div class="action-filters">
				<!-- DASHBOARD -->
				<ng-container *ngIf="dash">
					<pacto-cat-button
						(click)="atualizarBi()"
						[icon]="'pct pct-refresh-cw'"
						[id]="'btn-atualizar-bi'"
						i18n-label="@@treino-bi:btn-atualizar-new"
						label="atualizar"></pacto-cat-button>
					<pacto-cat-select
						[control]="professorFiltroFC"
						[id]="'bi-filtro'"
						[items]="colaboradores"
						[labelKey]="'nome'"
						[size]="'SMALL'"></pacto-cat-select>
				</ng-container>
				<!-- CUSTOM BI -->
				<ng-container *ngIf="customBi">
					<pacto-cat-button
						(click)="criarViewHandler()"
						[id]="'btn-criar-view'"
						i18n-label="@@treino-bi:btn-criar-grafico"
						label="Criar Gráfico"></pacto-cat-button>
				</ng-container>
			</div>
		</div>

		<pacto-cat-tabs-transparent #biTabs class="bi-tabs">
			<ng-template
				i18n-label="@@treino-bi:dashboard"
				label="Dashboard"
				pactoTabTransparent="dashboard"></ng-template>
			<ng-template
				i18n="@@treino-bi:personalizado"
				label="Personalizado"
				pactoTabTransparent="personalizado"></ng-template>
		</pacto-cat-tabs-transparent>

		<!-- DASH CONTENT -->
		<ng-container *ngIf="dash">
			<pacto-indicadores-grid
				[professorCodigoPessoaFiltro]="professorCodigoPessoa"
				[professorData]="professorData"
				[professorFiltro]="professorFiltroFC.value"></pacto-indicadores-grid>
			<pacto-treino-bi-movimentacao-alunos></pacto-treino-bi-movimentacao-alunos>

			<div>
				<div class="row">
					<div class="col-md-6">
						<pacto-treino-bi-avaliacao-media-treino
							[professorCodigoPessoaFiltro]="professorCodigoPessoa"
							[professorFiltro]="
								professorFiltroFC.value
							"></pacto-treino-bi-avaliacao-media-treino>
					</div>
					<div class="col-md-6">
						<pacto-treino-bi-avaliacao-acompanhamento
							[professorCodigoPessoaFiltro]="professorCodigoPessoa"
							[professorFiltro]="
								professorFiltroFC.value
							"></pacto-treino-bi-avaliacao-acompanhamento>
					</div>
				</div>
				<div class="mb-3"></div>
				<div class="row">
					<div class="col-md-6">
						<pacto-treino-bi-avaliacao-professor
							[professorCodigoPessoaFiltro]="professorCodigoPessoa"
							[professorFiltro]="
								professorFiltroFC.value
							"></pacto-treino-bi-avaliacao-professor>
					</div>
				</div>
			</div>
		</ng-container>

		<!-- CUSTOM BI CONTENT -->
		<ng-container *ngIf="customBi">
			<pacto-custom-bi-panel #biPanel></pacto-custom-bi-panel>
		</ng-container>
	</div>
</pacto-cat-layout-v2>

<pacto-traducoes-xingling #xingling>
	<span [xingling]="'todos'" i18n="@@treino-bi:todos-professores">
		Todos os professores
	</span>
</pacto-traducoes-xingling>
