<pacto-cat-card-plain>
	<div>
		<div
			class="type-h5 div-padding-bottom-30"
			i18n="@@treino-bi:avaliacao-media-treino-alunos">
			Avaliação do professor
		</div>
	</div>

	<div class="star-wrapper row div-padding-bottom-30">
		<div
			(click)="
				ClickHandlerAvaliacao(
					'avaliacaoProfessor1',
					avaliacaoProfessor?.nr1estrela
				)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:professor-avaliacao-1-estrela"
			title="{{ avaliacaoProfessor?.nr1estrela }} alunos deram 1 estrela">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoProfessor?.nr1estrela"
					[tamanho]="45"></pacto-star>
			</div>
		</div>

		<div
			(click)="
				ClickHandlerAvaliacao(
					'avaliacaoProfessor2',
					avaliacaoProfessor?.nr2estrelas
				)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:professor-avaliacao-2-estrelas"
			title="{{ avaliacaoProfessor?.nr2estrelas }} alunos deram 2 estrelas">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoProfessor?.nr2estrelas"
					[tamanho]="45"></pacto-star>
			</div>
		</div>

		<div
			(click)="
				ClickHandlerAvaliacao(
					'avaliacaoProfessor3',
					avaliacaoProfessor?.nr3estrelas
				)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:professor-avaliacao-3-estrelas"
			title="{{ avaliacaoProfessor?.nr3estrelas }} alunos deram 3 estrelas">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoProfessor?.nr3estrelas"
					[tamanho]="45"></pacto-star>
			</div>
		</div>

		<div
			(click)="
				ClickHandlerAvaliacao(
					'avaliacaoProfessor4',
					avaliacaoProfessor?.nr4estrelas
				)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:professor-avaliacao-4-estrelas"
			title="{{ avaliacaoProfessor?.nr4estrelas }} alunos deram 4 estrelas">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoProfessor?.nr4estrelas"
					[tamanho]="45"></pacto-star>
			</div>
		</div>

		<div
			(click)="
				ClickHandlerAvaliacao(
					'avaliacaoProfessor5',
					avaliacaoProfessor?.nr5estrelas
				)
			"
			class="margin-element div-star cursor-pointer"
			i18n-title="@@treino-bi:professor-avaliacao-5-estrelas"
			title="{{ avaliacaoProfessor?.nr5estrelas }} alunos deram 5 estrelas">
			<div class="center">
				<pacto-star
					[nrEstrela]="avaliacaoProfessor?.nr5estrelas"
					[tamanho]="45"></pacto-star>
			</div>
		</div>
	</div>

	<div class="center">total</div>
	<div class="center font-25-bold">
		<span
			(click)="
				ClickHandlerAvaliacao(
					'avaliacaoProfessorTotal',
					avaliacaoProfessor?.nrAvaliacoesTreino
				)
			"
			class="cursor-pointer"
			i18n="@@treino-bi:professor-avaliacao-0-estrela">
			{{ avaliacaoProfessor?.nrAvaliacoesTreino }} avaliações
		</span>
	</div>
</pacto-cat-card-plain>

<ng-template #notaCelula let-item="item">
	<pacto-star
		[preenchidas]="item.nota - 1"
		[repeticoes]="5"
		[tamanho]="35"></pacto-star>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span xingling="avaliacaoProfessor1Title">{{ professortitle1estrela }}</span>
	<span xingling="avaliacaoProfessor2Title">{{ professortitle2estrelas }}</span>
	<span xingling="avaliacaoProfessor3Title">{{ professortitle3estrelas }}</span>
	<span xingling="avaliacaoProfessor4Title">{{ professortitle4estrelas }}</span>
	<span xingling="avaliacaoProfessor5Title">{{ professortitle5estrelas }}</span>
	<span xingling="avaliacaoProfessorTotalTitle">
		{{ professortitleAvaliacaoTotal }}
	</span>

	<ng-template xingling="matricula">Matrícula</ng-template>
	<ng-template xingling="nomeAluno">Nome</ng-template>
	<ng-template xingling="dataHoraInicioApresentar">Hora</ng-template>
	<ng-template xingling="comentario">Comentário</ng-template>
	<ng-template xingling="professorCarteiraApresentar">Professor</ng-template>
	<ng-template xingling="nota">Nota</ng-template>
</pacto-traducoes-xingling>

<pacto-traducoes-xingling #notificacoesTranslate>
	<span xingling="sem-permissao">
		A visualização detalhada está liberada apenas para os usuários que possuem a
		permissão habilitada no seu perfil de acesso.
	</span>
</pacto-traducoes-xingling>
