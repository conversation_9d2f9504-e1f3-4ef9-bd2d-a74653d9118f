<div class="bi-grid">
	<div [ngClass]="{ integradoZW: integradoZW }" class="indicadores-icone">
		<pacto-cat-xs-info-card-icon
			(click)="cardClickHandler('totalAlunos', carteira?.totalAlunos)"
			[action]="true"
			[icon]="PactoIcon.PCT_USERS"
			[id]="'total_alunos_treino_bi'"
			[value]="carteira?.totalAlunos"
			i18n-label="@@treino-bi:total-alunos"
			label="Total de Alunos"
			title=" {{
				integradoZW
					? 'São contabilizados os alunos vinculados a professor e nas seguintes situações (AT NO, AT AV, IN VE, IN DE, CA TR, AE CR. Não são considerados as seguintes situações (VI, TV)'
					: 'São contabilizados os alunos vinculados a professor e nas seguintes situações (AT, IN e VI) '
			}}"></pacto-cat-xs-info-card-icon>
		<pacto-cat-xs-info-card-icon
			(click)="cardClickHandler('alunosAtivos', carteira?.ativos)"
			[action]="true"
			[icon]="PactoIcon.PCT_USER_CHECK"
			[id]="'total_alunos_ativos_treino_bi'"
			[value]="carteira?.ativos"
			i18n-label="@@treino-bi:alunos-ativos"
			label="Alunos Ativos"
			title="São contabilizados os alunos vinculados a professor e nas seguintes situações {{
				integradoZW
					? '(AT NO e AT AV). Não são considerados as seguintes situações (AE, CR)'
					: '(AT)'
			}}"></pacto-cat-xs-info-card-icon>
		<pacto-cat-xs-info-card-icon
			(click)="cardClickHandler('alunosInativos', carteira?.inativos)"
			[action]="true"
			[icon]="PactoIcon.PCT_USER_X"
			[id]="'total_alunos_inativos_treino_bi'"
			[value]="carteira?.inativos"
			i18n-label="@@treino-bi:alunos-inativos"
			label="Alunos Inativos"
			title="{{ hintAlunosInativos }}"></pacto-cat-xs-info-card-icon>
		<pacto-cat-xs-info-card-icon
			(click)="cardClickHandler('alunosVisitantes', carteira?.visitantes)"
			*ngIf="!integradoZW"
			[action]="true"
			[icon]="PactoIcon.PCT_USERS"
			[id]="'total_alunos_visitantes_treino_bi'"
			[label]="'Visitantes'"
			[value]="carteira?.visitantes"></pacto-cat-xs-info-card-icon>
		<pacto-cat-xs-info-card-icon
			(click)="
				cardClickHandler(
					'alunosSemAcompanhamento',
					carteira?.totalAlunosSemAcompanhamento
				)
			"
			[action]="true"
			[icon]="PactoIcon.PCT_USER_ATENTION"
			[id]="'total_alunos_sem_acompanhamento_treino_bi'"
			[value]="carteira?.totalAlunosSemAcompanhamento"
			i18n-label="@@treino-bi:alunos-sem-acompanhamento"
			label="Alunos não acompanhados"
			title="Alunos ativos sem acompanhamento de professor"></pacto-cat-xs-info-card-icon>

		<pacto-cat-xs-info-card-icon
			(click)="
				cardClickHandler(
					'alunosEmAcompanhamento',
					carteira?.totalAlunosSemAcompanhamento
				)
			"
			[action]="true"
			[icon]="PactoIcon.PCT_USER_DUMBELL"
			[id]="'total_alunos_em_acompanhamento_treino_bi'"
			[value]="carteira?.totalAlunosEmAcompanhamento"
			i18n-label="@@treino-bi:alunos-sem-acompanhamento"
			label="Alunos em acompanhamento"
			title="Alunos ativos em acompanhamento de professor"></pacto-cat-xs-info-card-icon>
	</div>

	<div [ngClass]="{ integradoZW: integradoZW }" class="indicadores-porcentagem">
		<pacto-cat-sm-info-card-percent
			(click)="
				cardClickHandler('treinosVencidos', treinamento?.alunosProgramaVencidos)
			"
			[accentColor]="PactoColor.HELLBOY_PRI"
			[description]="
				treinoVencidosPerc ? treinoVencidosPerc + '% dos alunos' : '-'
			"
			[id]="'porcentagem_treino_vencidos_treino_bi'"
			[percent]="treinoVencidosPerc"
			[value]="treinamento?.alunosProgramaVencidos"
			i18n-label="@@treino-bi:treinos-vencidos"
			label="Treinos Vencidos"
			title=" {{
				integradoZW
					? 'São contabilizados os alunos vinculados a professor e nas seguintes situações (AT NO e AT AV)'
					: 'São contabilizados somente os alunos vinculados a professor e ativos'
			}}"></pacto-cat-sm-info-card-percent>
		<pacto-cat-sm-info-card-percent
			(click)="
				cardClickHandler('alunoSemTreino', treinamento?.alunosAtivosSemTreino)
			"
			[accentColor]="PactoColor.HELLBOY_PRI"
			[description]="semTreinoPerc ? semTreinoPerc + '% dos alunos' : '-'"
			[id]="'porcentagem_alunos_semtreino_treino_bi'"
			[percent]="semTreinoPerc"
			[tooltip]="'Alunos ativos e sem treino'"
			[value]="treinamento?.alunosAtivosSemTreino"
			i18n-label="@@treino-bi:aluno-sem-treino"
			label="Alunos sem treino"
			title=" {{
				integradoZW
					? 'São contabilizados os alunos vinculados a professor e nas seguintes situações (AT NO e AT AV)'
					: 'São contabilizados somente os alunos vinculados a professor e ativos'
			}}"></pacto-cat-sm-info-card-percent>
		<pacto-cat-sm-info-card-percent
			(click)="
				cardClickHandler('treinoAVencer', treinamento?.alunosProgramaRenovar)
			"
			[accentColor]="PactoColor.PEQUIZAO_PRI"
			[description]="aVencerPerc ? aVencerPerc + ' % dos alunos' : '-'"
			[id]="'porcentagem_treino_avencer_treino_bi'"
			[percent]="aVencerPerc"
			[value]="treinamento?.alunosProgramaRenovar"
			i18n-label="@@treino-bi:treino-a-vencer"
			label="Treinos a Vencer"
			title=" {{
				integradoZW
					? 'São contabilizados os alunos vinculados a professor e nas seguintes situações (AT NO e AT AV)'
					: 'São contabilizados somente os alunos vinculados a professor e ativos'
			}}"></pacto-cat-sm-info-card-percent>
		<pacto-cat-sm-info-card-percent
			(click)="cardClickHandler('contratoAVencer', carteira?.aVencerZW)"
			*ngIf="integradoZW"
			[accentColor]="PactoColor.PEQUIZAO_PRI"
			[description]="
				contratoAVencerPerc ? contratoAVencerPerc + '% dos alunos' : '-'
			"
			[id]="'porcentagem_contrato_avencer_treino_bi'"
			[percent]="contratoAVencerPerc"
			[tooltip]="'Contratos a vencer dentro de 30 dias'"
			[value]="carteira?.aVencerZW"
			i18n-label="@@treino-bi:contrato-a-vencer"
			label="Contratos a vencer"
			title=" {{
				integradoZW
					? 'São contabilizados os alunos vinculados a professor e nas seguintes situações (AT NO e AT AV)'
					: 'São contabilizados somente os alunos vinculados a professor e ativos'
			}}"></pacto-cat-sm-info-card-percent>

		<pacto-cat-sm-info-card-percent
			(click)="
				cardClickHandler('treinoEmDia', treinamento?.alunosAtivosProgramaEmDia)
			"
			[description]="
				programaEmDiaPerc ? programaEmDiaPerc + '% dos programas' : '-'
			"
			[id]="'porcentagem_treino_em_dia_treino_bi'"
			[percent]="programaEmDiaPerc"
			[value]="treinamento?.alunosAtivosProgramaEmDia"
			i18n-label="@@treino-bi:treinos-em-dia"
			label="Treinos em dia"
			title=" {{
				integradoZW
					? 'São contabilizados os alunos vinculados a professor e nas seguintes situações (AT NO e AT AV)'
					: 'São contabilizados somente os alunos vinculados a professor e ativos'
			}}"></pacto-cat-sm-info-card-percent>
		<pacto-cat-sm-info-card-percent
			(click)="
				cardClickHandler('alunosComTreino', treinamento?.alunosAtivosComTreino)
			"
			[description]="
				ativoComTreinoPerc ? ativoComTreinoPerc + '% dos alunos' : '-'
			"
			[id]="'porcentagem_alunos_comTreino_treino_bi'"
			[percent]="ativoComTreinoPerc"
			[value]="treinamento?.alunosAtivosComTreino"
			i18n-label="@@treino-bi:aluno-com-treino"
			label="Alunos com treino"
			title=" {{
				integradoZW
					? 'São contabilizados os alunos vinculados a professor e nas seguintes situações (AT NO e AT AV)'
					: 'São contabilizados somente os alunos vinculados a professor e ativos'
			}}"></pacto-cat-sm-info-card-percent>
		<pacto-cat-sm-info-card-percent
			[action]="false"
			[id]="'duracao_media_programa_treino_bi'"
			[tooltip]="'Duração médio do programa'"
			[value]="tempoMedio ? tempoMedio + ' dias' : null"
			i18n-label="@@treino-bi:duracao-media"
			label="Duração média"></pacto-cat-sm-info-card-percent>

		<pacto-cat-sm-info-card-percent
			(click)="cardClickHandler('contratoRenovado', totalRenovacoesCarteira)"
			*ngIf="integradoZW"
			[accentColor]="PactoColor.PEQUIZAO_PRI"
			[description]="
				carteira?.taxaRenovacaoZW
					? carteira?.taxaRenovacaoZW + '% dos alunos'
					: null
			"
			[id]="'contratos_renovados_treino_bi'"
			[percent]="carteira?.taxaRenovacaoZW ? carteira?.taxaRenovacaoZW : null"
			[tooltip]="'Renovados nos últimos 30 dias'"
			[value]="totalRenovacoesCarteira"
			i18n-label="@@treino-bi:renovado-recentemente"
			label="Renovados recentemente"></pacto-cat-sm-info-card-percent>
	</div>
</div>

<pacto-traducoes-xingling #traducoes>
	<span xingling="totalAlunosTitle">Total de Alunos</span>
	<span xingling="alunosAtivosTitle">Alunos Ativos</span>
	<span xingling="alunosInativosTitle">Alunos Inativos</span>
	<span xingling="alunosSemAcompanhamentoTitle">Alunos sem Acompanhamento</span>
	<span xingling="alunosEmAcompanhamentoTitle">Alunos em Acompanhamento</span>
	<span xingling="alunosVisitantesTitle">Visitantes</span>
	<span xingling="treinoVencidoTitle">Treinos Vencidos</span>
	<span xingling="alunoSemTreinoTitle">Alunos sem Treino</span>
	<span xingling="treinoAVencerTitle">Treinos a Vencer</span>
	<span xingling="contratoAVencerTitle">Contratos a Vencer</span>
	<span xingling="treinoEmDiaTitle">Treinos em Dia</span>
	<span xingling="treinoComTreinoTitle">Alunos com Treino</span>
	<span xingling="contratoRenovadoTitle">Contrato Renovado Recentemente</span>

	<ng-template xingling="matricula">Matrícula</ng-template>
	<ng-template xingling="nomeProfessor">Nome do Professor</ng-template>
	<ng-template xingling="nomeAbreviado">Nome do aluno</ng-template>
	<ng-template xingling="nome">Nome do aluno</ng-template>
	<ng-template xingling="situacao">Situação</ng-template>
	<ng-template xingling="dataPrograma">Venc. Programa</ng-template>
	<ng-template xingling="dataUltimoacesso">Último acesso</ng-template>
	<ng-template xingling="dataVigenciaAteAjustadaApresentar">
		Data Vencimento
	</ng-template>
</pacto-traducoes-xingling>

<ng-template #imageCelula let-item="item">
	<div
		[ngStyle]="{ 'background-image': getImagemAluno(item.urlFoto) }"
		class="preview-object"></div>
</ng-template>

<span
	#tooltipIniciarAcompanhamento
	[hidden]="true"
	i18n="@@treino-bi:iniciar:tooltip-icon">
	Iniciar Acompanhamento
</span>
<span
	#tooltipAcompanharTreino
	[hidden]="true"
	i18n="@@treino-bi:iniciar:tooltip-icon">
	Acompanhar o Treino
</span>
<span
	#iniciarModalMsg
	[hidden]="true"
	i18n="@@treino-bi:iniciado-sucesso:tooltip-icon">
	Acompanhamento iniciado com sucesso.
</span>
<span
	#inseridoModalMsg
	[hidden]="true"
	i18n="@@treino-bi:iniciado:tooltip-icon">
	O acompanhamento para este aluno já foi iniciado.
</span>
<span
	#semProgramaVigenteModalMsg
	[hidden]="true"
	i18n="@@treino-bi:iniciado:tooltip-icon">
	O aluno não possui programa vigente para acompanhamento.
</span>
