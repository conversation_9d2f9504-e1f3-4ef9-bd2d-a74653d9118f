import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { EditorDetalhesFichaComponent } from "./components/editor-detalhes-ficha/editor-detalhes-ficha.component";
import { ConfigurarFichaService } from "../configurar-ficha.service";
import { TraducoesXinglingComponent } from "ui-kit";
import { SnotifyService } from "ng-snotify";
import { ModalCriarFichaComponent } from "../modal-criar-ficha/modal-criar-ficha.component";
import {
	Aluno,
	FichaPrograma,
	PerfilAcessoRecursoNome,
	Programa,
	TreinoApiProgramaService,
} from "treino-api";
import { SessionService } from "@base-core/client/session.service";
import { PerfilAcessoFuncionalidadeNome } from "treino-api";
import { ModalAdicionarAtividadesComponent } from "../modal-adicionar-atividades/modal-adicionar-atividades.component";
import { RestService } from "@base-core/rest/rest.service";
import { HttpClient, HttpParams } from "@angular/common/http";
import { ActivatedRoute } from "@angular/router";
import { catchError, finalize, switchMap } from "rxjs/operators";
import {
	PermissaoService,
	PlataformModuleConfig,
	PlatformMenuItem,
} from "pacto-layout";
import { PerfilRecursoPermissoTipo } from "sdk";
import { Observable, from, of } from "rxjs";
import { TreinoConfigCacheService } from "../../../base/configuracoes/configuration.service";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";

@Component({
	selector: "pacto-configurar-ficha",
	templateUrl: "./configurar-ficha.component.html",
	styleUrls: ["./configurar-ficha.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfigurarFichaComponent implements OnInit {
	@ViewChild("notificacoesTranslate", { static: true })
	notificacoesTranslate: TraducoesXinglingComponent;
	@ViewChild("modalTranslate", { static: true })
	modalTranslate: TraducoesXinglingComponent;
	@ViewChild("modalAdicionarAtividadesComponent", { static: false })
	modalAdicionarAtividadesComponent: ModalAdicionarAtividadesComponent;
	@Input() programa: Programa;
	aluno: Aluno;
	ficha: FichaPrograma;
	fichaSelecionadaIndex = 0;
	atividadeFichaSelecionadaIndex = 0;
	nomeFicha = "";
	fichaSendoModificada = false;
	adicionandoAtividade = false;
	enviandoOutrosAlunos = false;
	ready = false;

	fichas: Array<FichaPrograma> = [];
	predefinido;
	permissaotornarpredefinido;
	existeFicha = true;
	permissaoProgramaTreino;
	permissaoEnviarTreinoEmMassa;
	id: number;
	pactobr = false;
	accordionExpanded: boolean = false;
	botaoAprovarDesabilitado = false;
	constructor(
		private modalService: ModalService,
		private configurarFichaService: ConfigurarFichaService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private rest: RestService,
		private http: HttpClient,
		private route: ActivatedRoute,
		private configurationService: TreinoConfigCacheService,
		private programaService: TreinoApiProgramaService
	) {
		this.route.params.subscribe((params) => {
			this.id = params.id;
		});
	}

	ngOnInit() {
		this.carregarPermissoes();
		this.loadData();
		this.hookUpService();
		this.processParams(this.programa);
		this.verificarFichaExiste();
		this.botaoAprovarDesabilitado = this.deveMostrarBotaoAprovarTreino();
		this.cd.detectChanges();
	}

	getPredefinedPrograms(page: number, size: number) {
		const filters = {
			quicksearchFields: ["nome", "genero", "professorMontou.nome", "situacao"],
		};

		const params = new HttpParams()
			.set("filters", JSON.stringify(filters))
			.set("configs", "{}")
			.set("page", page.toString())
			.set("size", size.toString());

		const fullUrl = this.rest.buildFullUrl("programas/pre-definido");
		return this.http.get(fullUrl, { params });
	}

	private loadData() {
		this.configurarFichaService.programa$.subscribe((value: Programa) => {
			this.fichas = value.fichas;
			this.pactobr =
				this.sessionService.loggedUser.username.toLowerCase() === "pactobr";
			this.cd.detectChanges();
		});

		this.predefinido = this.programa.predefinido;
	}

	private hookUpService() {
		this.configurarFichaService.fichaSelecionadaIndex$.subscribe((index) => {
			this.fichaSelecionadaIndex = index;
		});
		this.configurarFichaService.atividadeFichaSelecionadaIndex$.subscribe(
			(index) => {
				this.atividadeFichaSelecionadaIndex = index;
			}
		);
		this.configurarFichaService.programa$.subscribe((programa) => {
			if (programa) {
				this.inicializarSelecaoFicha();
			}
		});
	}

	private inicializarSelecaoFicha() {
		if (this.programa.fichas.length) {
			this.ficha = this.configurarFichaService.obterFichaAtual();
			this.cd.detectChanges();
		}
	}

	private processParams(programa: Programa) {
		if (programa && programa.fichas && programa.fichas.length > 1) {
			programa.fichas.sort((a, b) => {
				const ordemA = a.ordem != null ? a.ordem : Infinity;
				const ordemB = b.ordem != null ? b.ordem : Infinity;

				if (ordemA === ordemB) {
					return a.id < b.id ? -1 : 1;
				}
				return ordemA - ordemB;
			});
		}

		this.configurarFichaService.programa$.next(programa);
		this.ready = true;
	}

	get logUrl() {
		return this.rest.buildFullUrl(`log/programa/${this.programa.id}`);
	}

	editarFichaHandler() {
		if (this.fichas && this.fichas.length > 0) {
			this.abrirModalEditorFicha();
			this.cd.detectChanges();
		}
	}

	abrirModalEditorFicha() {
		const modal = this.modalService.open(
			"Configurar ficha",
			EditorDetalhesFichaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.ready(
			this.configurarFichaService.fichaFormGroup.getRawValue()
		);

		from(modal.result)
			.pipe(
				switchMap((result) =>
					this.configurarFichaService.atualizarDetalhesFicha(result)
				),
				switchMap(() => this.configurarFichaService.salvarFicha()),
				catchError((error) => {
					this.snotifyService.error(
						this.notificacoesTranslate.getLabel("ficha-edit-error")
					);
					return of(null);
					0;
				})
			)
			.subscribe((retorno) => {
				if (retorno) {
					this.snotifyService.success(
						this.notificacoesTranslate.getLabel("ficha-edit-success")
					);
				} else {
					this.snotifyService.error(
						this.notificacoesTranslate.getLabel("ficha-edit-error")
					);
				}
				this.loadData();
				this.cd.detectChanges();
			});
	}

	abrirModalAdicionarFicha() {
		const modal = this.modalService.open(
			"Criar ficha",
			ModalCriarFichaComponent
		);
		modal.result.then((result) => {
			this.configurarFichaService.adicionarFicha(result).subscribe(() => {
				this.cd.detectChanges();
				this.snotifyService.success(
					this.notificacoesTranslate.getLabel("ficha-create-success")
				);
				this.verificarFichaExiste();
				this.loadData();
			});
		});
	}

	enviarProgramaOutrosAlunos() {
		const permissoesAluno = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.ALUNOS
		);
		const permissaoCliente2_04 =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.04"
			);
		if (
			(permissoesAluno &&
				permissoesAluno.permissao.find(
					(tp) =>
						tp === PerfilRecursoPermissoTipo.CONSULTAR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR ||
						tp === PerfilRecursoPermissoTipo.EDITAR ||
						tp === PerfilRecursoPermissoTipo.EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.TOTAL ||
						tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
				)) ||
			(permissaoCliente2_04 &&
				permissaoCliente2_04.tipoPermissoes.find(
					(tp) =>
						tp === PerfilRecursoPermissoTipo.CONSULTAR ||
						tp === PerfilRecursoPermissoTipo.INCLUIR ||
						tp === PerfilRecursoPermissoTipo.EDITAR ||
						tp === PerfilRecursoPermissoTipo.EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
						tp === PerfilRecursoPermissoTipo.TOTAL ||
						tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
				))
		) {
			this.enviandoOutrosAlunos = true;
			this.cd.detectChanges();
		} else {
			this.snotifyService.warning(
				'Você não possui as permissões necessárias para acessar esta tela ("Aluno>Permissões>Alunos" ou "2.04 - Clientes").'
			);
		}
	}

	tornarFichaPredefinidaHandler() {
		const fichaAtual = this.configurarFichaService.obterFichaAtual();
		if (fichaAtual) {
			this.nomeFicha = fichaAtual.nome;
			this.configurarFichaService
				.tornarFichaPreDefinida()
				.subscribe((result) => {
					if (this.permissaotornarpredefinido) {
						if (result === "ficha_duplicada") {
							this.snotifyService.error(
								this.notificacoesTranslate.getLabel("ficha-duplic-error")
							);
						} else {
							this.snotifyService.success(
								this.notificacoesTranslate.getLabel("ficha-predefinida-success")
							);
						}
					} else {
						this.snotifyService.error(
							this.notificacoesTranslate.getLabel("usuariosempermissao")
						);
					}
				});
		}
	}

	excluirFicha() {
		if (this.permissaoProgramaTreino.editar) {
			if (this.fichas && this.fichas.length > 0) {
				this.excluirFichaHandler();
				this.cd.detectChanges();
			}
		}
	}

	excluirFichaHandler() {
		const fichaAtual = this.configurarFichaService.obterFichaAtual();
		if (fichaAtual) {
			this.nomeFicha = fichaAtual.nome;
			setTimeout(() => {
				const modal = this.modalService.confirm(
					this.modalTranslate.getLabel("ficha-remove-title"),
					this.modalTranslate.getLabel("ficha-remove-body"),
					this.modalTranslate.getLabel("ficha-remove-action")
				);
				modal.result.then(() => {
					this.configurarFichaService.removerFicha().subscribe(() => {
						this.cd.detectChanges();
						this.snotifyService.success(
							this.notificacoesTranslate.getLabel("ficha-remove-success")
						);
						this.verificarFichaExiste();
						this.loadData();
					});
				});
			});
		}
	}

	validaFichaHandler(evento) {
		this.fichaSendoModificada = evento;
	}

	addAtividade(evento) {
		this.loadData();
		this.adicionandoAtividade = true;
		this.modalAdicionarAtividadesComponent.init();
		this.cd.detectChanges();
	}

	closeAtividade() {
		this.adicionandoAtividade = false;
	}

	closeEnviarOutros() {
		this.enviandoOutrosAlunos = false;
	}

	aprovarProgramaHandler() {
		this.programaService
			.aprovarPrograma(this.programa.id, this.getDto())
			.pipe(
				switchMap(() =>
					this.programaService.obterProgramaCompleto(this.programa.id)
				)
			)
			.subscribe(
				(programaAtualizado) => {
					this.programa = programaAtualizado;
					this.configurarFichaService.atualizarProgramaService(
						programaAtualizado
					);
					this.botaoAprovarDesabilitado = this.deveMostrarBotaoAprovarTreino();
					this.snotifyService.success("Programa aprovado com sucesso.");
					this.cd.detectChanges();
				},
				(err) => {
					this.snotifyService.error("Erro ao aprovar o programa.");
					this.cd.detectChanges();
				}
			);
	}

	private getDto(): any {
		return {
			id: this.programa.id,
			emRevisaoProfessor: false,
		};
	}

	deveMostrarBotaoAprovarTreino(): boolean {
		const tempoAprovacaoAutomatica =
			this.configurationService.configuracoesIa.tempo_aprovacao_automatica || 0;
		const agora = new Date();
		const dataLancamento = new Date(this.programa.dataLancamento);
		const limiteAprovacao = new Date(
			dataLancamento.getTime() + tempoAprovacaoAutomatica * 60 * 1000
		);

		const habilitarAprovacaoProfessor =
			this.configurationService.configuracoesIa
				.habilitar_obrigatoriedade_aprovacao_professor;

		// if ((agora >= limiteAprovacao || tempoAprovacaoAutomatica === 0) && !habilitarAprovacaoProfessor && this.programa.emRevisaoProfessor) {
		// 	this.aprovarProgramaHandler();
		// }

		return (
			this.programa.geradoPorIA &&
			this.programa.emRevisaoProfessor &&
			(habilitarAprovacaoProfessor ||
				agora <= limiteAprovacao ||
				tempoAprovacaoAutomatica === 0)
		);
	}

	salvarFichaHandler(item) {
		if (this.permissaoProgramaTreino.editar) {
			this.configurarFichaService.salvarFicha().subscribe((result) => {
				if (item != "aprovar") {
					if (result) {
						this.snotifyService.success("Ficha salva com sucesso.");
					} else {
						this.snotifyService.error("Erro ao salvar a ficha.");
					}
				}
				this.cd.detectChanges();
			});
		}
	}

	private carregarPermissoes() {
		this.permissaotornarpredefinido = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.TORNAR_FICHA_PREDEFINIDA
		);
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
		this.permissaoEnviarTreinoEmMassa = this.sessionService.funcionalidades.get(
			PerfilAcessoFuncionalidadeNome.ENVIAR_TREINO_EM_MASSA
		);
	}

	verificarFichaExiste() {
		const verificarFicha = this.configurarFichaService.obterFichaAtual();
		this.existeFicha = verificarFicha === null ? false : true;
		this.cd.detectChanges();
	}

	dropFicha(event: CdkDragDrop<FichaPrograma[]>) {
		const fichasReordenadas = [...this.fichas];
		moveItemInArray(fichasReordenadas, event.previousIndex, event.currentIndex);
		this.snotifyService.info("Salvando nova ordem...");
		this.configurarFichaService.salvarOrdemFichas(fichasReordenadas).subscribe(
			() => {
				this.fichas = fichasReordenadas;
				this.snotifyService.success("Ordem salva com sucesso!");
				this.selecionarFicha(event.currentIndex);
				this.cd.detectChanges();
			},
			(error) => {
				this.snotifyService.error("Falha ao salvar a nova ordem.");
			}
		);
	}

	tabChangeHandler(index: number) {
		if (this.fichaSendoModificada && index !== this.fichaSelecionadaIndex) {
			this.salvarFichaAtualESelecionarProxima(index);
		} else {
			this.selecionarFicha(index);
		}
	}

	toggleAccordion() {
		this.accordionExpanded = !this.accordionExpanded;
	}

	salvarFichaAtualESelecionarProxima(proximoIndex: number) {
		if (this.permissaoProgramaTreino.editar) {
			this.configurarFichaService.salvarFicha().subscribe((result) => {
				if (result) {
					this.fichaSendoModificada = false;
					this.selecionarFicha(proximoIndex);
				} else {
					this.snotifyService.error("Erro ao salvar a ficha.");
				}
				this.cd.detectChanges();
			});
		}
	}

	selecionarFicha(index: number) {
		this.fichaSelecionadaIndex = index;
		this.configurarFichaService.selecionarFicha(index);
		this.cd.detectChanges();
	}
}
