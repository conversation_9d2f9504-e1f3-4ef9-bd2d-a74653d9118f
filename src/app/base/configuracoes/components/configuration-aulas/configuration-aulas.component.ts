import { Component, OnInit, ViewChild } from "@angular/core";
import { FormGroup, FormControl } from "@angular/forms";

import { SnotifyService } from "ng-snotify";
import { switchMap } from "rxjs/operators";

import { SessionService } from "@base-core/client/session.service";
import {
	ConfigItemType,
	TreinoApiConfiguracoesTreinoService,
	PerfilAcessoRecursoNome,
} from "treino-api";
import { TreinoConfigCacheService } from "../../configuration.service";
import { RestService } from "@base-core/rest/rest.service";

@Component({
	selector: "pacto-configuration-aulas",
	templateUrl: "./configuration-aulas.component.html",
	styleUrls: ["./configuration-aulas.component.scss"],
})
export class ConfigurationAulasComponent implements OnInit {
	@ViewChild("translator", { static: true }) translator;
	@ViewChild("configSuccess", { static: true }) configSuccess;

	formGroup: FormGroup = new FormGroup({});
	ready = false;
	saving = false;
	items: any[] = [];
	itemsControle: any[] = [];
	itemsExperimentais: any[] = [];
	integracaoZW = false;
	esconderExperimental: any;

	hourMask = (numberInsert) => {
		const firstNumber = parseInt(numberInsert[0], 10);
		if (firstNumber === 2) {
			return [/[0-2]/, /[0-3]/, ":", /[0-5]/, /[0-9]/];
		} else {
			return [/[0-1]/, /[0-9]|/, ":", /[0-5]/, /[0-9]/];
		}
	};

	constructor(
		private configurationService: TreinoConfigCacheService,
		private notify: SnotifyService,
		private rest: RestService,
		private configTreinoService: TreinoApiConfiguracoesTreinoService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		if (Object.keys(this.configurationService.configuracoesAula).length === 0) {
			this.configurationService.loadTreinoConfigCache().subscribe(() => {
				this.inicializarComponente();
			});
		} else {
			this.inicializarComponente();
		}
	}

	private inicializarComponente() {
		this.construirItens();
		if (this.configurationService.configuracoesAula.controlar_por_freepass) {
			this.esconderExperimental =
				this.configurationService.configuracoesAula.controlar_por_freepass;
		}
		this.setupEvents();
		this.verificarPermissoes();
	}

	get urlLog() {
		return this.rest.buildFullUrl("configuracoes/aulas/log");
	}

	construirItens() {
		this.integracaoZW = this.sessionService.integracaoZW;
		this.itemsControle = [
			{
				name: "controlar_por_freepass",
				titleKey: "controlar_por_freepass-title",
				descriptionKey: "controlar_por_freepass-desc",
				type: ConfigItemType.CHECKBOX,
				translator: this.translator,
			},
		];
		this.itemsExperimentais = [
			{
				name: "nr_aula_experimental_aluno",
				titleKey: "nr-aula-experimental-aluno-title",
				descriptionKey: "nr-aula-experimental-aluno-desc",
				type: ConfigItemType.INPUT,
				mask: { mask: [/[0-9]/, /[0-9]/], guide: false },
				translator: this.translator,
			},
		];

		this.items = [
			{
				name: "minutos_agendar_com_antecedencia",
				titleKey: "minutos-agendar-com-antecedencia-title",
				descriptionKey: "minutos-agendar-com-antecedencia-desc",
				type: ConfigItemType.INPUT,
				mask: {
					mask: [/[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, " minuto(s)"],
					placeholderChar: "\u2000",
					guide: true,
				},
				translator: this.translator,
			},
			{
				name: "minutos_desmarcar_com_antecedencia",
				titleKey: "minutos-desmarcar-com-antecedencia-title",
				descriptionKey: "minutos-desmarcar-com-antecedencia-desc",
				type: ConfigItemType.INPUT,
				mask: {
					mask: [/[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, " minuto(s)"],
					placeholderChar: "\u2000",
					guide: true,
				},
				translator: this.translator,
			},
			{
				name: "minutos_alterar_equipamento_com_antecedencia",
				titleKey: "minutos-alterar-equipamento-com-antecedencia-title",
				descriptionKey: "minutos-alterar-equipamento-com-antecedencia-desc",
				type: ConfigItemType.INPUT,
				mask: {
					mask: [/[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, /[0-9]/, " minuto(s)"],
					placeholderChar: "\u2000",
					guide: true,
				},
				translator: this.translator,
			},
		];

		if (this.integracaoZW) {
			this.items.push(
				{
					name: "validar_modalidade",
					titleKey: "modulo-modalidade-title",
					descriptionKey: "modulo-modalidade-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "nr_validar_vezes_modalidade",
					titleKey: "nr-aula-modalidade-aluno-title",
					descriptionKey: "nr-aula-modalidade-aluno-desc",
					type: ConfigItemType.INPUT,
					translator: this.translator,
				},
				{
					name: "validar_horario_contrato",
					titleKey: "modulo-horario-title",
					descriptionKey: "modulo-horario-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "permitir_aluno_marcar_aula_por_tipo_modalidade",
					titleKey: "check-outras-unidades-title",
					descriptionKey: "check-outras-unidades-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "bloquear_mesmo_ambiente",
					titleKey: "bloquear-mesmo-ambiente-title",
					descriptionKey: "bloquear-mesmo-ambiente-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "bloquear_aula_coletiva_nao_pertence_modalidade",
					titleKey: "bloquear-aula-coletiva-nao-pertence-modalidade-title",
					descriptionKey: "bloquear-aula-coletiva-nao-pertence-modalidade-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "desmarcar_aulas_futuras_parcela_atrasada",
					titleKey: "desmarcar-aulas-futuras-parcela-atrasada-title",
					descriptionKey: "desmarcar-aulas-futuras-parcela-atrasada-desc",
					type: ConfigItemType.INPUT,
					translator: this.translator,
				},
				{
					name: "manter_reposicao_aula_coletiva",
					titleKey: "manter_reposicao_aula_coletiva-title",
					descriptionKey: "manter_reposicao_aula_coletiva-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "limite_dias_reposicao_aula_coletiva",
					titleKey: "limite_dias_reposicao_aula_coletiva-title",
					descriptionKey: "limite_dias_reposicao_aula_coletiva-desc",
					type: ConfigItemType.INPUT,
					translator: this.translator,
				},
				{
					name: "descontar_credito_ao_marcar_aula_sem_confirmar_presenca",
					titleKey:
						"descontar_credito_ao_marcar_aula_sem_confirmar_presenca-title",
					descriptionKey:
						"descontar_credito_ao_marcar_aula_sem_confirmar_presenca-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "bloquear_gerar_reposicao_aula_ja_reposta",
					titleKey: "bloquear_gerar_reposicao_aula_ja_reposta-title",
					descriptionKey: "bloquear_gerar_reposicao_aula_ja_reposta-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "utilizar_numeracao_sequencial_identificador_equipamento",
					titleKey:
						"utilizar_numeracao_sequencial_identificador_equipamento-title",
					descriptionKey:
						"utilizar_numeracao_sequencial_identificador_equipamento-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				},
				{
					name: "proibir_marcar_aula_antes_pagamento_primeira_parcela",
					titleKey:
						"proibir_marcar_aula_antes_pagamento_primeira_parcela-title",
					descriptionKey:
						"proibir_marcar_aula_antes_pagamento_primeira_parcela-desc",
					type: ConfigItemType.CHECKBOX,
					translator: this.translator,
				}
			);
		}
		this.popularFormGroup();
		const configAula = { ...this.configurationService.configuracoesAula };

		if (configAula.minutos_alterar_equipamento_com_antecedencia === undefined) {
			configAula.minutos_alterar_equipamento_com_antecedencia = "0";
		}
		this.setFormGroupValues();
	}

	private popularFormGroup() {
		this.formGroup = new FormGroup({});
		this.itemsExperimentais.forEach((item) => {
			this.formGroup.addControl(item.name, new FormControl(null));
			item.formControl = this.formGroup.get(item.name);
		});
		this.itemsControle.forEach((item) => {
			this.formGroup.addControl(item.name, new FormControl(null));
			item.formControl = this.formGroup.get(item.name);
		});
		this.items.forEach((item) => {
			this.formGroup.addControl(item.name, new FormControl(null));
			item.formControl = this.formGroup.get(item.name);
		});
		this.ready = true;
	}

	private setFormGroupValues() {
		const configuracoes = this.configurationService.configuracoesAula;

		try {
			if (
				configuracoes.minutos_alterar_equipamento_com_antecedencia === undefined
			) {
				configuracoes.minutos_alterar_equipamento_com_antecedencia = "0";
			}
			this.formGroup.patchValue(configuracoes);
		} catch (error) {
			Object.keys(configuracoes).forEach((key) => {
				if (this.formGroup.contains(key)) {
					this.formGroup.get(key).setValue(configuracoes[key]);
				}
			});
		}
	}

	saveHandler() {
		const dto = this.formGroup.getRawValue();
		const update$ = this.configTreinoService.updateConfiguracoesAula(dto);
		const reload$ = this.configurationService.loadTreinoConfigCache();
		update$.pipe(switchMap(() => reload$)).subscribe(() => {
			const configSuccess = this.configSuccess.nativeElement.innerHTML;
			this.notify.success(configSuccess);
		});
	}

	private setupEvents() {
		const esconderExperimental = this.itemsControle.find((item) => {
			return item.name === "controlar_por_freepass";
		});
		if (esconderExperimental) {
			esconderExperimental.formControl.valueChanges.subscribe((value) => {
				this.esconderExperimental = value;
			});
		}
	}

	verificarPermissoes() {
		const configuracoesEmpresa = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.CONFIGURACOES_EMPRESA
		);
		if (
			configuracoesEmpresa &&
			!configuracoesEmpresa.incluir &&
			!configuracoesEmpresa.editar &&
			!configuracoesEmpresa.excluir
		) {
			this.formGroup.disable();
			this.saving = true;
		}
	}
}
