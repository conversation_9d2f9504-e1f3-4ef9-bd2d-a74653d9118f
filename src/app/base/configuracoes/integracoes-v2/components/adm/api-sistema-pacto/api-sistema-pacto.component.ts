import {
	AfterContentInit,
	Component,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { SessionService } from "@base-core/client/session.service";
import { RestService } from "@base-core/rest/rest.service";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import {
	CatTabsTransparentComponent,
	DialogService,
	PactoDataGridConfig,
	PactoModalSize,
	RelatorioComponent,
} from "ui-kit";
import { GerarTokenComponent } from "./gerar-token/gerar-token.component";
import { ParametrosComponent } from "./parametros/parametros.component";

import { environment } from "src/environments/environment";

@Component({
	selector: "pacto-api-sistema-pacto",
	templateUrl: "./api-sistema-pacto.component.html",
	styleUrls: ["./api-sistema-pacto.component.scss"],
})
export class ApiSistemaPactoComponent implements OnInit, AfterContentInit {
	@ViewChild("tabs", { static: true })
	public tabs: CatTabsTransparentComponent;

	@ViewChild("tableConfiguracoes", { static: false })
	public tableConfiguracoes: RelatorioComponent;

	@ViewChild("tableHistorico", { static: false })
	public tableHistorico: RelatorioComponent;

	public dataGridConfigConfiguracoes: PactoDataGridConfig;

	public dataGridConfigHistorico: PactoDataGridConfig;

	@ViewChild("cellStatus", { static: true })
	public cellStatus: TemplateRef<any>;

	@ViewChild("cellParametros", { static: true })
	public cellParametros: TemplateRef<any>;

	@ViewChild("gerarToken", { static: true })
	public gerarToken: TemplateRef<any>;

	public docApiUrl: string = environment.docApiUrl;

	constructor(
		private readonly rest: RestService,
		private readonly modal: NgbModal,
		private readonly sessionService: SessionService,
		private readonly modalService: DialogService
	) {}

	public ngOnInit(): void {}

	public ngAfterContentInit(): void {
		this.initDataGridConfigConfiguracoes();
		this.initDataGridConfigHistorico();
	}

	public tabClickHandler(event: {
		index: number;
		previous: string;
		next: string;
	}): void {}

	public initDataGridConfigConfiguracoes(): void {
		const key = this.sessionService.chave;
		const companyId = this.sessionService.empresaId;

		this.dataGridConfigConfiguracoes = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlApiPacto(
				`credential/${key}/${companyId}/list`
			),
			// logUrl: ,
			quickSearch: false,
			buttons: {
				conteudo: this.gerarToken,
				nome: "Gerar credencial",
				id: "gerarToken",
			},
			columns: [
				{
					nome: "description",
					titulo: "Descrição",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "expiresAt",
					titulo: "Data de validade",
					valueTransform(v) {
						return v ? new Date(v).toLocaleDateString() : "";
					},
					visible: true,
					ordenavel: true,
				},
				{
					nome: "",
					titulo: "Status",
					visible: true,
					ordenavel: false,
					celula: this.cellStatus,
				},
			],
			actions: [
				{
					nome: "editar",
					iconClass: "pct pct-edit cor-azulim05",
					tooltipText: "Editar",
				},
			],
		});
	}

	public initDataGridConfigHistorico(): void {
		this.dataGridConfigHistorico = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(`logApi/all`),
			quickSearch: false,
			columns: [
				{
					nome: "descricaoToken",
					titulo: "Descrição",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataUso",
					titulo: "Data de vencimento",
					valueTransform(v) {
						return v ? new Date(v).toLocaleDateString() : "";
					},
					visible: true,
					ordenavel: true,
				},
				{
					nome: "method",
					titulo: "Metodo",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "ip",
					titulo: "IP",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "uri",
					titulo: "Endpoint",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "params",
					titulo: "Parâmetros",
					visible: true,
					ordenavel: false,
					celula: this.cellParametros,
				},
			],
		});
	}

	public iconClickHandler(event: { row: any; iconName: string }): void {
		this[event.iconName](event.row);
	}

	public editar(row): void {
		const modal = this.modal.open(GerarTokenComponent, {
			centered: true,
			size: "lg",
			backdrop: "static",
			beforeDismiss: () => {
				this.tableConfiguracoes.reloadData();
				return true;
			},
		});
		modal.componentInstance.tokenIntegracao = row;
		modal.componentInstance.ehEdicao = true;
	}

	public gerarTokenHandler(): void {
		this.modal.open(GerarTokenComponent, {
			centered: true,
			size: "lg",
			backdrop: "static",
			beforeDismiss: () => {
				this.tableConfiguracoes.reloadData();
				return true;
			},
		});
	}

	public verificaSeEstaAtivo(expiresAt: number): boolean {
		const dataDeVencimento = new Date(expiresAt);
		const hoje = new Date();

		dataDeVencimento.setHours(0, 0, 0, 0);
		hoje.setHours(0, 0, 0, 0);

		return dataDeVencimento >= hoje;
	}

	public verParametros(parametros: any): void {
		const modal = this.modalService.open(
			"Parâmetros",
			ParametrosComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.parametros = parametros;
	}
}
