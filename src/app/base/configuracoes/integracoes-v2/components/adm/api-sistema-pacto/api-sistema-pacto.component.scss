.card {
	margin-top: 1.5rem;
	border-radius: 0.5rem;
	padding: 1rem;

	pacto-relatorio::ng-deep {
		.pacto-table-title-block {
			padding: 0;
		}

		.table-content {
			padding: 0;
		}
	}
}

.situacao {
	width: 5rem;
	padding: 0.3125rem 1rem;
	border-radius: 3.125rem;

	text-align: center;
	font-size: 0.75rem;
	font-weight: 400;
	line-height: 133.333%;

	&.ativa {
		background-color: #bcf5d9;
		color: #107040;
	}

	&.inativa {
		background-color: #f5bcca;
		color: #9c1638;
	}
}

.btn-gerar-token {
	padding: 0.8125rem 1.25rem;
	border-radius: 0.25rem;
	background-color: #1e60fa;

	color: #fff;
	text-align: center;
	font-size: 0.875rem;
	font-weight: 600;
	line-height: 100%; /* 0.875rem */
}

.empty-content {
	padding: 1rem;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 1rem;
	align-self: stretch;

	.descricao {
		color: #55585e;
		text-align: center;
		/* Title/4 */
		font-size: 0.875rem;
		font-weight: 600;
		line-height: 125%; /* 1.09375rem */
	}
}

.link-doc {
	font-family: Nunito Sans;
	font-style: normal;
	font-weight: normal;
	font-size: 16px;
	line-height: 22px;
	color: #80858c;
	margin: 1.5rem 0;

	a {
		color: inherit;
		text-decoration: underline;
		font-weight: 600;
	}
}
