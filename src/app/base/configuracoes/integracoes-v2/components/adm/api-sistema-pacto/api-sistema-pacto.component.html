<pacto-cat-layout-v2>
	<pacto-breadcrumbs
		[breadcrumbConfig]="{
			categoryName: 'Configurações / Integrações',
			menu: 'API Sistema Pacto'
		}"></pacto-breadcrumbs>

	<pacto-cat-tabs-transparent #tabs (activateTab)="tabClickHandler($event)">
		<ng-template
			label="Configuração"
			pactoTabTransparent="configuracao"></ng-template>
		<ng-template
			label="Histórico"
			pactoTabTransparent="historico"></ng-template>
	</pacto-cat-tabs-transparent>
	<p
		*ngIf="docApiUrl?.trim().startsWith('http')"
		class="link-doc"
		i18n="@@configuracoes-int-api:info-docs-api">
		Acesso a documentação da PACTO API disponível em
		<a [href]="docApiUrl" target="_blank" rel="noopener">documentação</a>
		.
	</p>
	<div *ngIf="tabs.tabId === 'configuracao'" class="card">
		<pacto-relatorio
			#tableConfiguracoes
			(btnClick)="gerarTokenHandler()"
			(iconClick)="iconClickHandler($event)"
			[actionTitulo]="'Ações'"
			[customEmptyContent]="emptyToken"
			[showShare]="false"
			[table]="dataGridConfigConfiguracoes"></pacto-relatorio>
	</div>

	<div *ngIf="tabs.tabId === 'historico'" class="card">
		<pacto-relatorio
			#tableHistorico
			(iconClick)="iconClickHandler($event)"
			[customEmptyContent]="emptyHistorico"
			[showShare]="false"
			[table]="dataGridConfigHistorico"></pacto-relatorio>
	</div>
</pacto-cat-layout-v2>

<ng-template #gerarToken>Gerar credencial</ng-template>

<ng-template #cellStatus let-item="item">
	<div
		[ngClass]="{
			situacao: true,
			ativa: verificaSeEstaAtivo(item.expiresAt),
			inativa: !verificaSeEstaAtivo(item.expiresAt)
		}">
		{{ verificaSeEstaAtivo(item.expiresAt) ? "Ativo" : "Inativo" }}
	</div>
</ng-template>

<ng-template #cellParametros let-item="item">
	<i
		(click)="verParametros(item.params)"
		[style.color]="'#1E60FA'"
		class="pct pct-search"></i>
</ng-template>

<ng-template #emptyToken>
	<div class="empty-content">
		<img src="assets/images/prancheta.svg" />

		<strong class="descricao">
			Você ainda não possui nenhuma Credencial gerada
		</strong>

		<pacto-cat-button
			(click)="gerarTokenHandler()"
			label="Gerar credencial"
			size="LARGE"
			type="PRIMARY_ADD"></pacto-cat-button>
	</div>
</ng-template>

<ng-template #emptyHistorico>
	<div class="empty-content">
		<img src="assets/images/prancheta.svg" />

		<strong class="descricao">Você ainda não possui nenhum histórico</strong>
	</div>
</ng-template>
