import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import {
	AbstractControl,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { RestService } from "@base-core/rest/rest.service";
import { SnotifyService } from "ng-snotify";

import { SessionService } from "sdk";
import {
	PactoDataGridConfig,
	PactoDataGridState,
	RelatorioComponent,
	SelectFilterParamBuilder,
	TraducoesXinglingComponent,
	DataFiltro,
	GridFilterConfig,
	GridFilterType,
} from "ui-kit";

import { AdmCoreApiIntegracoesService, Integracao } from "adm-core-api";
import { CrmApiGenericoService } from "crm-api";
import { ModalService } from "@base-core/modal/modal.service";

@Component({
	selector: "pacto-integracao-botconversa-modal",
	templateUrl: "./integracao-botconversa-modal.component.html",
	styleUrls: ["./integracao-botconversa-modal.component.scss"],
})
export class IntegracaoBotConversaModalComponent implements OnInit {
	@ViewChild("traducao", { static: true }) traducao: TraducoesXinglingComponent;
	@ViewChild("tableDataComponent", { static: true })
	tableDataComponent: RelatorioComponent;
	@ViewChild("columnAtivo", { static: true }) columnAtivo: TemplateRef<any>;
	@ViewChild("columnCodigo", { static: true }) columnCodigo: TemplateRef<any>;
	@ViewChild("columnDescricao", { static: true })
	columnDescricao: TemplateRef<any>;
	@ViewChild("columnDisponibilidade", { static: true })
	columnDisponibilidade: TemplateRef<any>;
	@ViewChild("columnFase", { static: true }) columnFase: TemplateRef<any>;
	@ViewChild("tooltipInativar", { static: true }) tooltipInativar;
	@ViewChild("tooltipAtivar", { static: true }) tooltipAtivar;
	@ViewChild("inativarModalTitle", { static: true }) inativarModalTitle;
	@ViewChild("inativarModalBody", { static: true }) inativarModalBody;
	@ViewChild("inativarModalMsg", { static: true }) inativarModalMsg;
	@ViewChild("ativarModalTitle", { static: true }) ativarModalTitle;
	@ViewChild("ativarModalBody", { static: true }) ativarModalBody;
	@ViewChild("ativarModalMsg", { static: true }) ativarModalMsg;

	@Input() integracao: Integracao;
	@Input() empresaSelecionada: any;

	nomeFluxo: String = "";

	formGroup: FormGroup = new FormGroup({
		codigo: new FormControl(),
		tipoFluxo: new FormControl(),
		acaoFaseCrm: new FormControl(),
		webhook: new FormControl(),
		descricaoFluxo: new FormControl(),
	});
	tipoFluxolist = [
		{ codigo: 0, descricao: "Contato em grupo" },
		{ codigo: 1, descricao: "Fase do crm" },
		{ codigo: 2, descricao: "Tela do cliente" },
	];
	situacao = [
		{ codigo: true, descricao: "Ativo" },
		{ codigo: false, descricao: "Inativo" },
	];
	acaoFaseCrm = [
		{ codigo: "AG", descricao: "Agendamentos Presenciais" },
		{ codigo: "LA", descricao: "Agendados de Amanhã" },
		{ codigo: "RE", descricao: "Renovação" },
		{ codigo: "VA", descricao: "Visitantes Antigos" },
		{ codigo: "HO", descricao: "Visitantes 24h" },
		{ codigo: "PE", descricao: "Desistentes" },
		{ codigo: "EX", descricao: "Ex-Alunos" },
		{ codigo: "ID", descricao: "Indicações" },
		{ codigo: "CI", descricao: "Conversão de Indicados" },
		{ codigo: "CV", descricao: "Conversão de Agendados" },
		{ codigo: "CE", descricao: "Conversão de Ex-Alunos" },
		{ codigo: "CA", descricao: "Conversão de Visitantes Antigos" },
		{ codigo: "CD", descricao: "Conversão de Desistentes" },
		{ codigo: "CI", descricao: "Conversão de Receptivo" },
		{ codigo: "RI", descricao: "Grupo de Risco" },
		{ codigo: "VE", descricao: "Vencidos" },
		{ codigo: "PV", descricao: "Pós Venda" },
		{ codigo: "FA", descricao: "Faltosos" },
		{ codigo: "AN", descricao: "Aniversariantes" },
		{ codigo: "SE", descricao: "Últimas Sessões" },
		{ codigo: "SA", descricao: "Sessões sem agenda" },
		{ codigo: "AL", descricao: "Agendamentos de Ligações" },
		{ codigo: "IS", descricao: "Indicações sem Contato" },
		{ codigo: "CP", descricao: "Receptivo" },
		{ codigo: "GY", descricao: "Aluno WellHub" },
		{ codigo: "CR", descricao: "Meta Extra" },
		{ codigo: "LH", descricao: "Leads Hoje" },
		{ codigo: "LC", descricao: "Leads Acumuladas" },
		{ codigo: "VR", descricao: "Visitas recorrentes" },
		{ codigo: "CL", descricao: "Conversão de Lead" },
		{ codigo: "UG", descricao: "Últ. Acesso WellHub" },
	];

	constructor(
		private cd: ChangeDetectorRef,
		public restService: RestService,
		public admCoreApiIntegracoesService: AdmCoreApiIntegracoesService,
		public crmApiGenericoService: CrmApiGenericoService,
		private modalService: ModalService,
		public sessionService: SessionService,
		private notificationService: SnotifyService,
		private activeModal: NgbActiveModal
	) {}

	listBotConversas = new Array<any>();
	tableData: PactoDataGridConfig;
	botconversalist = new Array<any>();
	botconversaData: {
		content: Array<any>;
		first: boolean;
		last: boolean;
		number: number;
		size: number;
		totalElements: number;
		totalPages: number;
	} = {
		content: new Array<any>(),
		first: true,
		last: false,
		number: 0,
		size: 0,
		totalElements: 0,
		totalPages: 0,
	};
	page = 1;
	size = 5;
	itensPerPage = [
		{ id: 5, label: "5" },
		{ id: 10, label: "10" },
		{ id: 15, label: "15" },
	];
	state: PactoDataGridState = new PactoDataGridState();

	filterConfig: GridFilterConfig = {
		filters: [
			{
				name: "status",
				label: "Status",
				type: GridFilterType.MANY,
				options: [
					{ value: "Ativo", label: "Ativo" },
					{ value: "Inativo", label: "Inativo" },
				],
			},
		],
	};

	filterOptions:
		| {
				configs: {};
				filters: {
					status?: string[];
				};
		  }
		| {};

	ngOnInit() {
		this.initTableData();
		let count = 1;
		if (this.integracao.configuracao) {
			console.log(this.integracao.configuracao);
			this.integracao.configuracao.forEach((i) => {
				const fase = this.acaoFaseCrm.find((p) => p.codigo === i.fase);
				const itemBotConversa = {
					codigo: i.codigo,
					ativo: this.situacao.find((p) => p.codigo === i.ativo).descricao,
					descricao: i.descricao,
					disponibilidade: this.tipoFluxolist[i.tipoFluxo].descricao,
					fase: fase == null ? "-" : fase.descricao,
					cdigoFase: fase == null ? "" : fase.codigo,
					hook: i.urlWebHooBotConversa,
					tipoFluxo: i.tipoFluxo,
				};
				this.botconversalist.push(itemBotConversa);
				const qtdBotconversa = this.integracao.configuracao;
				this.sortList(this.botconversalist, "codigo", "ASC");
				this.createBotconversaPageObject(
					this.page,
					this.size,
					count === qtdBotconversa
				);
				count++;
			});
		} else {
			this.notificationService.error(
				"Falha ao obter configuração BotConversa!"
			);
		}
	}

	isFaseCrm() {
		if (this.formGroup.get("tipoFluxo").value == 0) {
			this.formGroup.get("acaoFaseCrm").setValue(null);
		}
		return this.formGroup.get("tipoFluxo").value == 1;
	}

	ordenarBotconversa(eventSort) {
		this.botconversalist = this.sortList(
			this.botconversalist,
			eventSort.columnName,
			eventSort.direction
		);
		this.createBotconversaPageObject(this.page, this.size, true);
	}

	removerdaListaBotconversa(event) {
		const index = this.botconversalist.findIndex(
			(p) => p.codigo === event.row.codigo
		);
		this.botconversalist.splice(index, 1);
		this.createBotconversaPageObject(this.page, this.size, true);
	}

	createBotconversaPageObject(page = 1, size = 5, reloadData = true) {
		this.botconversaData.totalElements = this.botconversalist.length;
		this.botconversaData.size = size;
		this.botconversaData.totalPages = Math.ceil(
			+(this.botconversaData.totalElements / this.botconversaData.size)
		);
		this.botconversaData.first = page === 0 || page === 1;
		this.botconversaData.last = page === this.botconversaData.totalPages;
		this.botconversaData.content = this.botconversalist.slice(
			size * page - size,
			size * page
		);
		this.tableDataComponent.showBtnAdd = false;
		if (reloadData) {
			this.tableDataComponent.reloadData();
		}
		this.tableDataComponent.ngbPage = this.page;
	}

	pageChangeEvent(page) {
		if (!isNaN(page)) {
			this.page = page;
		}
		this.createBotconversaPageObject(this.page, this.size, true);
	}

	pageSizeChange(size) {
		if (!isNaN(size)) {
			this.size = size;
			this.page = 1;
		}
		this.createBotconversaPageObject(this.page, this.size, true);
	}

	sortList(
		list: Array<any>,
		columnName: string,
		direction: string
	): Array<any> {
		list = list.sort((a, b) => {
			if (direction === "ASC") {
				if (a[columnName] > b[columnName]) {
					return 1;
				} else if (a[columnName] < b[columnName]) {
					return -1;
				} else {
					return 0;
				}
			} else {
				if (a[columnName] < b[columnName]) {
					return 1;
				} else if (a[columnName] > b[columnName]) {
					return -1;
				} else {
					return 0;
				}
			}
		});
		return list;
	}

	timeMask() {
		return [/[0-2]/, /[0-9]/, ":", /[0-5]/, /[0-9]/];
	}

	selectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			filters: JSON.stringify({
				quicksearchValue: term,
			}),
		};
	};

	getAditionalFiltersUsuariosMeta() {
		return { codigoEmpresa: this.empresaSelecionada.empresazw };
	}

	removerConfig(item: any) {
		this.admCoreApiIntegracoesService
			.removerConfiguracaoIntegracaoBotConversa(item.row.codigo)
			.subscribe(
				(ret) => {
					if (ret && ret.content === "OK") {
						this.removerdaListaBotconversa(item);
						this.notificationService.success("Removido com sucesso.");
					} else {
						this.notificationService.error(ret.content);
					}
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(
							this.traducao.getLabel("error-ao-salvar")
						);
					}
				}
			);
	}

	salvarIntegracao() {
		this.salvar("salva-com-sucesso");
	}

	salvar(labelMensagemSucesso?: string) {
		this.botconversalist.forEach((i) => {
			const itemBotConversa = {
				ativo: i.ativo == "Ativo" ? true : false,
				codigo: i.codigo,
				descricao: i.descricao,
				tipoFluxo: i.tipoFluxo,
				empresa: this.empresaSelecionada.empresazw,
				fase: i.cdigoFase,
				urlWebHooBotConversa: i.hook,
			};
			this.listBotConversas.push(itemBotConversa);
			this.integracao.configuracao = this.listBotConversas;
		});
		this.admCoreApiIntegracoesService
			.salvarConfiguracaoIntegracaoBotConversa(this.integracao.configuracao)
			.subscribe(
				() => {
					this.notificationService.success(
						this.traducao.getLabel(labelMensagemSucesso)
					);
					this.activeModal.close();
				},
				(httpErrorResponse) => {
					const err = httpErrorResponse.error;
					if (err.meta && err.meta.messageValue) {
						this.notificationService.error(
							this.traducao.getLabel("error-ao-salvar")
						);
					}
				}
			);
	}

	beforeDismiss() {
		const textSelected = window.getSelection();
		if (textSelected.toString().length > 0) {
			return false;
		} else {
			this.activeModal.close();
		}
	}

	initTableData() {
		this.state.paginaTamanho = 5;
		this.state.paginaNumero = 0;
		this.tableData = new PactoDataGridConfig({
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 5,
			showFilters: true,
			dataAdapterFn: (serverData) => {
				return this.botconversaData;
			},
			pagination: true,
			state: this.state,
			columns: [
				{
					nome: "ativo",
					titulo: this.columnAtivo,
					visible: true,
					ordenavel: true,
					width: "8%",
					styleClass: "left",
				},
				{
					nome: "codigo",
					titulo: this.columnCodigo,
					visible: true,
					ordenavel: true,
					width: "10%",
				},
				{
					nome: "descricao",
					titulo: this.columnDescricao,
					visible: true,
					ordenavel: true,
					width: "24%",
				},
				{
					nome: "disponibilidade",
					titulo: this.columnDisponibilidade,
					visible: true,
					ordenavel: true,
					width: "24%",
				},
				{
					nome: "fase",
					titulo: this.columnFase,
					visible: true,
					ordenavel: true,
					width: "20%",
				},
			],
			actions: [
				{
					nome: "edit",
					iconClass: "fa fa-pencil margin-right-5",
					tooltipText: "Editar botconversa",
				},
				{
					nome: "remove",
					iconClass: "fa fa-trash-o margin-right-5",
					tooltipText: "Remover botconversa",
				},
				{
					nome: "inative",
					iconClass: "pct pct-eye-off",
					tooltipText: "Inativar/Ativar",
				},
			],
		});
		this.tableDataComponent.pageSizeControl.setValue(5);
	}

	montarEdicao(item: any) {
		this.formGroup.get("codigo").setValue(item.row.codigo);
		this.formGroup.get("descricaoFluxo").setValue(item.row.descricao);
		this.formGroup.get("webhook").setValue(item.row.hook);
		this.formGroup.get("tipoFluxo").setValue(item.row.tipoFluxo);
		if (this.formGroup.get("tipoFluxo").value == 1) {
			this.formGroup.get("acaoFaseCrm").setValue(item.row.cdigoFase);
		}
		this.removerdaListaBotconversa(item);
	}

	toFormControl(absCtrl: AbstractControl | null): FormControl {
		const formControl = absCtrl as FormControl;
		return formControl;
	}

	actionClickHandler($event: { row: any; iconName: string }) {
		if ($event.iconName === "edit") {
			this.montarEdicao($event);
		} else if ($event.iconName === "remove") {
			if ($event.row.codigo) {
				this.removerConfig($event);
			} else {
				this.removerdaListaBotconversa($event);
			}
		} else if ($event.iconName === "inative") {
			this.editarSituacaoHandler($event);
		}
	}

	adicionarBotconversa() {
		if (
			!(
				this.formGroup.get("descricaoFluxo").value === null ||
				this.formGroup.get("descricaoFluxo").value.trim() === ""
			) &&
			!(this.formGroup.get("tipoFluxo").value === null) &&
			!(
				this.formGroup.get("webhook").value === null ||
				this.formGroup.get("webhook").value.trim() === ""
			)
		) {
			if (
				this.formGroup.get("tipoFluxo").value == 1 &&
				this.formGroup.get("acaoFaseCrm").value === null
			) {
				this.notificationService.error(
					'O campo: "Fase do Crm" deve ser informado.'
				);
			} else {
				const tipoFluxo = this.formGroup.get("tipoFluxo").value;
				const fase = this.acaoFaseCrm.find(
					(p) => p.codigo === this.formGroup.get("acaoFaseCrm").value
				);
				this.sortList(this.botconversalist, "codigo", "DESC");
				const fluxo = {
					ativo: "Ativo",
					codigo: this.formGroup.get("codigo").value,
					descricao: this.formGroup.get("descricaoFluxo").value,
					disponibilidade: this.tipoFluxolist[tipoFluxo].descricao,
					fase: fase == null ? "-" : fase.descricao,
					cdigoFase: fase == null ? "" : fase.codigo,
					hook: this.formGroup.get("webhook").value,
					tipoFluxo,
				};

				if (tipoFluxo !== null && tipoFluxo !== undefined) {
					const indexFase = this.botconversalist.findIndex(
						(p) => p.cdigoFase === fluxo.cdigoFase && p.ativo == "Ativo"
					);
					const indexNome = this.botconversalist.findIndex(
						(p) => p.descricao === fluxo.descricao && p.ativo == "Ativo"
					);

					if (tipoFluxo == 2) {
						const fluxoTipo2 = this.botconversalist.find(
							(p) => p.tipoFluxo == 2 && p.ativo == "Ativo"
						);
						if (fluxoTipo2) {
							this.notificationService.warning(
								"Já existe um fluxo do tipo Tela do Cliente configurado."
							);
							return; // Não permite criar outro fluxo tipo 2
						}
					}

					if (
						(tipoFluxo == 1 && indexFase === -1 && indexNome === -1) ||
						(tipoFluxo == 0 && indexNome === -1) ||
						tipoFluxo == 2
					) {
						this.botconversalist.push(fluxo);
						this.formGroup.get("tipoFluxo").setValue(undefined);
						this.sortList(this.botconversalist, "codigo", "DESC");
						this.createBotconversaPageObject(this.page, this.size, true);
					} else {
						this.notificationService.warning(
							"Já existe um fluxo para esta mesma " +
								(indexFase !== -1 && indexNome === -1
									? "fase"
									: indexNome !== -1 && indexFase === -1
									? "descrição"
									: indexNome !== -1 && tipoFluxo == 1 && indexFase !== -1
									? "fase e descrição"
									: "configuração")
						);
					}
					this.formGroup.reset();
				}
			}
		} else {
			this.notificationService.error("Campo obrigatório não preenchido.");
		}
	}

	editarSituacaoHandler(item) {
		this.nomeFluxo = item.row.descricao;
		if (item.row.ativo == "Inativo" && item.row.tipoFluxo != 0) {
			const verificaDuplicidade = this.botconversalist.find(
				(p) =>
					p.tipoFluxo == item.row.tipoFluxo &&
					p.ativo == "Ativo" &&
					(item.row.tipoFluxo != 1 || p.fase === item.row.fase)
			);
			if (verificaDuplicidade) {
				this.notificationService.error(
					"Já exite um Fluxo ativo para está fase!"
				);
				return;
			}
		}

		setTimeout(() => {
			let modalTitle = "";
			let modalBody = "";
			let modalButton = "";
			let modalMsg = "";
			if (item.row.ativo == "Ativo") {
				modalTitle = this.inativarModalTitle.nativeElement.innerHTML;
				modalBody = this.inativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipInativar.nativeElement.innerHTML;
				modalMsg = this.inativarModalMsg.nativeElement.innerHTML;
			} else {
				modalTitle = this.ativarModalTitle.nativeElement.innerHTML;
				modalBody = this.ativarModalBody.nativeElement.innerHTML;
				modalButton = this.tooltipAtivar.nativeElement.innerHTML;
				modalMsg = this.ativarModalMsg.nativeElement.innerHTML;
			}
			const handler = this.modalService.confirm(
				modalTitle,
				modalBody,
				modalButton
			);
			handler.result.then(() => {
				const atividade = { ativa: item.ativa };
				const codigo: number = item.row.codigo;
				this.admCoreApiIntegracoesService
					.inativarOuAtivarConfiguracaoIntegracaoBotConversa(codigo)
					.subscribe(
						(ret) => {
							if (ret && ret.content === "OK") {
								if (this.botconversalist[index].ativo == "Ativo") {
									this.notificationService.success("Ativado com sucesso.");
								} else {
									this.notificationService.success("Inativado com sucesso.");
								}
							} else {
								this.notificationService.error(
									"Fluxo gymbotpro sendo utilizado. Código do(s) contato(s) em grupo: " +
										ret.content
								);
							}
						},
						(httpErrorResponse) => {
							const err = httpErrorResponse.error;
							if (err.meta && err.meta.messageValue) {
								this.notificationService.error(
									this.traducao.getLabel("error-ao-salvar")
								);
							}
						}
					);

				const index = this.botconversalist.findIndex(
					(p) => p.codigo === item.row.codigo
				);
				this.botconversalist[index].ativo =
					this.botconversalist[index].ativo == "Ativo" ? "Inativo" : "Ativo";
				this.createBotconversaPageObject(this.page, this.size, true);
			});
		});
	}

	onSearch(filtro) {
		this.filterOptions = filtro.filters;

		const filtrosSelecionados: { status?: string[] } = this.filterOptions || {};

		const listaFiltrada = this.botconversalist.filter((item) => {
			if (
				Array.isArray(filtrosSelecionados.status) &&
				filtrosSelecionados.status.length > 0 &&
				!filtrosSelecionados.status.includes(item.ativo)
			) {
				return false;
			}
			return true;
		});

		this.botconversaData.totalElements = listaFiltrada.length;
		this.botconversaData.totalPages = Math.ceil(
			listaFiltrada.length / this.size
		);
		this.botconversaData.content = listaFiltrada.slice(
			this.size * this.page - this.size,
			this.size * this.page
		);

		this.tableDataComponent.reloadData();
	}
}
