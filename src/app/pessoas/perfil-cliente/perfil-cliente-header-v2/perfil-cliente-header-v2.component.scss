@import "~src/assets/scss/pacto/plataforma-import.scss";
@import "projects/ui/assets/ds3/colors.var.scss";
@import "projects/ui/assets/ds3/fonts/fonts.scss";
@import "projects/ui/assets/ds3/typography/mixins";

$azulBorda: #1998fc;

:host {
	position: relative;
	width: 100%;

	::ng-deep .dropdownbutton > button {
		border-radius: 4px 0px 0px 4px !important;

		icon-drop ::after {
			display: none;
		}
	}

	::ng-deep .observacao .pacto-no-border .pct,
	::ng-deep .observacao .pacto-no-border {
		color: $actionDefaultAble04;
	}

	::ng-deep .observacao .pacto-no-border {
		font-weight: 700;

		&:hover {
			background-color: rgba($azulim05, 0.15);
		}
	}

	::ng-deep .avisos-button .pacto-no-border .pct,
	::ng-deep .avisos-button .pacto-no-border {
		color: #fa1e1e;
	}

	::ng-deep .avisos-button .pacto-no-border {
		font-weight: 700;

		&:hover {
			background-color: rgba(#fa1e1e, 0.15);
		}
	}

	::ng-deep .pacto-primary {
		background-color: $actionDefaultAble04;
		border: 1px solid $actionDefaultAble04;
		font-family: Poppins, sans-serif !important;
		font-size: 12px !important;
		letter-spacing: 0.25px !important;
		font-weight: 600 !important;
	}
}

div > .row > * {
	padding: 0px;
	margin: 0px;
}

.cards-avisos-observacoes {
	width: 100%;
	padding: 8px 16px;
	border-radius: 0.5rem;
	background: #fff;
	color: #51555a;
	box-shadow: 0 1px 4px 0 #e4e5e6;
	margin-top: 41px;

	.link-cursor {
		cursor: pointer;
		color: $actionDefaultAble04;
		@extend .pct-overline2-bold;
	}

	.card-avisos {
		margin-bottom: 8px;

		.title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 8px;
		}

		.dados-cliente {
			display: flex;
			justify-content: space-between;
		}

		.card {
			padding: 8px 0;

			span {
				@extend .pct-overline1-bold;
				color: $feedbackLoss02;

				i {
					margin-right: 8px;
				}
			}

			.empty {
				font-family: Poppins;
				font-size: 14px;
				font-weight: 400;
				line-height: 18px;
				color: #494b50;
			}
		}
	}

	.card-observacoes {
		.title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 8px;
		}

		.card {
			padding: 8px 0;

			span {
				@extend .pct-body2;
				color: $typeDefaultText;
			}

			.dados-obs {
				.nome-data {
					font-size: 12px;
					text-decoration-skip-ink: none;
					color: #6d7078;
				}

				.mensagem {
					color: #494b50;
				}
			}

			.empty {
				font-family: Poppins;
				font-size: 14px;
				font-weight: 400;
				line-height: 18px;
				color: #494b50;
			}
		}
	}
}

.perfil-header {
	border-radius: 10px;
	background-color: #eff2f7;
	display: flex;
	flex-direction: column;
	width: 100%;
	margin: 5px 0;

	.compact-info {
		border-radius: 10px;
		box-shadow: 0 1px 4px 0 $geloPri;
		background-color: $branco;
		display: flex;
		flex-direction: column;
		width: 100%;
		padding: 16px;
		font-size: 14px;

		.user-objecao {
			padding: 8px 16px 8px 16px;
			background: #fee6e6;
			color: #e10505;
			margin: 0px 0px 15px 0px;
			border-radius: 5px;
			font-size: 14px;

			.icon {
				padding-right: 5px;
			}

			.titulo {
				font-weight: 700;
			}

			.objecao {
				font-weight: 400;
				padding-left: 5px;
			}

			.remover-objecao {
				text-align: right;
				cursor: pointer;
			}
		}

		.user-section {
			margin: 0px;

			.clienteTitularDiv {
				padding-top: 10px;
			}

			.clienteTitular {
				color: #80858c;
				font-size: 14px;
				font-weight: 400;
			}

			.clienteTitularNome {
				color: #1e60fa;
				font-size: 14px;
				cursor: pointer;
				font-weight: 600;
				padding-left: 5px;
			}

			.avatar {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0px;
				margin: 0px;

				.edit-overlay {
					width: 120px;
					height: 120px;
					position: absolute;
					display: flex;
					flex-direction: row;
					align-content: center;
					justify-content: center;
					align-items: center;
					background-color: #51555a;
					opacity: 0;
					border-radius: 50%;
					border: 1px solid white;

					i {
						font-size: 54px;
					}
				}

				&:hover {
					cursor: pointer;

					.edit-overlay {
						opacity: 0.6;
					}
				}
			}

			.userInfo {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: flex-start;
				padding-left: 16px;
				margin: 0px;

				.matricula {
					@extend .pct-overline1;
					color: $typeDefaultText;
				}

				.nome {
					@extend .pct-title2;
					color: $typeDefaultTitle;
					margin-top: 4px;
					margin-bottom: 11px;

					i {
						font-size: 16px;
						color: $actionDefaultAble04;
					}
				}

				.status-situacao {
					padding: 0px;
					margin: 0px;
					display: flex;
					justify-content: center;
					flex-direction: row;

					.situacao-aluno,
					.situacao-contrato,
					.situacao-pass {
						@extend .type-caption-rounded;
						padding: 5px 16px;
						color: $cinza06;
						background-color: $cinza01;
						border-radius: 50px;
						margin: 0 5px 0 0;
						width: 136px;
						height: 25px;
						display: flex;
						flex-direction: row;
						justify-content: center;

						&.vi.primario {
							color: #163e9c;
							background-color: #bccdf5;
						}

						&.at.primario {
							color: #037d03;
							background-color: #b4fdb4;
						}

						&.in.primario {
							color: #7d0303;
							background-color: #fdb4b4;
						}

						&.tr.primario {
							color: #797d86;
							background-color: #e4e5e7;
						}

						&.no.secundario {
							color: #0a4326;
							background-color: #8fefbf;
						}

						&.di.secundario {
							color: #0a4326;
							background-color: #63e9a6;
						}

						&.pe.secundario {
							color: #0a4326;
							background-color: #1dc973;
						}

						&.av.secundario {
							color: #705810;
							background-color: #efd78f;
						}

						&.cr.secundario {
							color: #105870;
							background-color: #8fd7ef;
						}

						&.ve.secundario {
							color: #705810;
							background-color: #e9c763;
						}

						&.tv.secundario {
							color: #705810;
							background-color: #e2b736;
						}

						&.ca.secundario {
							color: #701028;
							background-color: #f5bcca;
						}

						&.de.secundario {
							color: #701028;
							background-color: #ef8fa7;
						}

						&.in.secundario {
							color: #701028;
							background-color: #e96384;
						}

						&.ae.secundario {
							color: #105870;
							background-color: #63c7e9;
						}

						&.di.secundario {
							color: #0a4326;
							background-color: #63e9a6;
						}

						&.gympass.terciario {
							color: #9c5316;
							background-color: #f5d6bc;
						}

						&.totalPass.terciario {
							color: #9c5316;
							background-color: #efba8f;
						}

						&.freepass.terciario {
							color: #0a4326;
							background-color: #1dc973;
						}

						&.spc.terciario {
							color: #ffffff;
							background-color: #3d3f43;
						}

						&.dependente.terciario {
							color: #ffffff;
							background-color: #0a4326;
						}
					}
				}
			}

			.tags {
				display: flex;
				flex-direction: column;
				align-items: flex-end;
				margin-left: auto;

				.saldos {
					display: flex;
					margin: 16px 0;

					.pontos,
					.dinheiros {
						padding-top: 4px;
						border-radius: 8px;
						border: solid 1px $supportGray03;
						display: flex;
						align-items: center;
						flex-direction: column;
						gap: 8px;
					}

					.pontos {
						margin-right: 8px;
						color: $supportBlack03;
						display: flex;
						flex-direction: column;
						padding-bottom: 4px;
					}

					.saldo-positivo {
						color: $feedbackGain02;
					}

					.saldo-negativo {
						color: $feedbackLoss02;
					}

					.saldo-zero {
						color: $supportBlack03;
					}
				}

				.status {
					padding-top: calc(16px + 6.5px);
					display: flex;
					flex-direction: row;
					gap: 4px;

					div > i {
						font-size: 16px;
					}

					.status-badge {
						white-space: nowrap;
						display: flex;
						align-items: center;
						padding: 6.5px 16px;
						border-radius: 50px;
						background-color: $cinza01;
						color: $cinza06;
						width: 48px;
						overflow: hidden;

						.status-icon-wrapper {
							flex-shrink: 0;
						}

						&.checked {
							background-color: $supportBlue01;
							color: $supportBlue06;
						}

						&.positivo {
							background-color: $supportRed01;
							color: $supportRed05;
						}

						&:hover span.status-text {
							opacity: 1;
						}

						.status-text {
							display: flex;
							flex-direction: row;
							font-size: 12px;
							padding-left: 5px;
							opacity: 0;
							align-items: center;

							i {
								padding-left: 5px;
								font-size: 11px;
							}
						}
					}
				}
			}
		}

		.separator {
			border-top: 1px solid $cinza02;
			padding: 0 16px;
			width: calc(100% - 16 * 2px);
		}

		.functions-section {
			padding: 0px;
			// margin: 0 16px;

			.notifications {
				display: flex;

				.notification-button {
					padding-right: 8px;

					&:hover {
						cursor: pointer;
					}

					.notification-text {
						font-size: 12px;
						font-weight: 600;
						line-height: 12px;
						letter-spacing: 0.25px;
						text-align: center;
						color: $azulim05;
						padding: 8px 11px;
					}
				}
			}

			.functionalities {
				display: flex;
				justify-content: flex-end;

				.function-button + .function-button {
					padding-left: 8px;
				}

				.function-button {
					::ng-deep .pacto-outline {
						font-family: Poppins, sans-serif !important;
						font-size: 12px !important;
						letter-spacing: 0.25px !important;
						font-weight: 600 !important;
						border-color: #1e60fa !important;
						color: #1e60fa !important;
					}

					&.vendas,
					&.mais {
						.dropdown {
							display: flex;
							align-items: center;

							.dropdown__content {
								min-width: 230px;

								.parent-sub-menu {
									display: flex;
									align-items: center;
									justify-content: space-between;
								}

								.drowpdown-item-list {
									@extend .type-p-small-rounded;
									font-weight: 700;
									color: $actionDefaultAble04;
									cursor: pointer;

									&.disabled {
										cursor: not-allowed;
										border-color: $cinzaClaroPri;
										color: $cinza06;
										background-color: $cinzaClaroPri;
									}

									&.children-sub-menu {
										padding-left: 32px;
										font-weight: 400;
									}
								}
							}

							.dropdown__btn-wrapper {
								display: flex;
								height: 34px;

								.icon-drop {
									padding: 5.5px 4px 5px 3px;
									border-radius: 0 4px 4px 0;
									border-top: 1px solid $azulBorda;
									border-left: none;
									border-right: 1px solid $azulBorda;
									border-bottom: 1px solid $azulBorda;
									display: flex;
									align-items: center;
									justify-content: center;

									i {
										color: $azulBorda;
									}
								}

								.icon-drop-blue {
									background-color: $actionDefaultAble04;
									padding: 5.5px 4px 5px 3px;
									border-radius: 0 4px 4px 0;
									border-top: 1px solid $actionDefaultAble04;
									border-left: 1px solid white;
									border-right: 1px solid $actionDefaultAble04;
									border-bottom: 1px solid $actionDefaultAble04;
									display: flex;
									align-items: center;
									justify-content: center;

									i {
										color: white;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	.extra-info {
		margin: 15px 0;

		.info-cards {
			margin: 0;
			display: grid;
			gap: 16px;
			grid-template-columns: 1fr 1fr 1fr;

			.info-card {
				border-radius: 8px;
				border: 1px solid #dcdddf;
				padding: 16px;

				.info-title {
					@extend .pct-title3;
					color: #55585e;
					display: inline-block;

					.info-title-icon {
						display: inline-block;
						color: $actionDefaultAble04;
						cursor: pointer;
					}
				}

				.info-text {
					display: flex;
					align-items: center;
					padding: 8px 0px 0px 0px;
					width: fit-content;
					@extend .pct-body2-regular;
					color: $typeDefaultText;

					span {
						word-break: break-all;
						overflow-wrap: break-word;
					}

					&.side-by-side {
						float: left;
					}

					i,
					svg {
						font-size: 16px;
						margin: auto 0.5rem;
					}
				}

				.has-action {
					display: flex;
					align-items: start;

					> button {
						all: unset;

						> i {
							margin-left: 8px;
							font-size: 13px;
							margin-top: 2px;
						}
					}
				}
			}
		}
	}

	.dados-pagamento-title {
		margin-top: 16px;
	}

	.expander {
		display: flex;
		justify-content: center;
		padding: 5px 32px;
		border-radius: 0 0 32px 32px;
		width: 80px;
		margin: -25px auto auto auto;
		top: 25px;
		position: relative;
		box-shadow: 0 10px 5px 0 $geloPri;
		background-color: $branco;
	}

	.more-items {
		color: $actionDefaultAble04;
		text-align: center;
		font-size: 12px;
		font-weight: 600;
		line-height: 12px;
		letter-spacing: 0.25px;
	}
}

p {
	padding: 0px;
	margin: 0px;
}

.actionable {
	color: $actionDefaultAble04 !important;

	&:hover {
		cursor: pointer;
	}
}

::ng-deep {
	.design-system3-adjust {
		.modal-dialog {
			max-width: 1000px;
			max-height: 540px;
			width: 100%;
			height: auto;
			display: flex;
			flex-direction: column;

			pacto-dialog .pacto-modal-wrapper,
			pacto-modal-wrapper .pacto-modal-wrapper {
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 100%;

				.modal-titulo {
					padding: 1rem;
					padding-bottom: 0.5rem;
					font-size: 14px !important;
					font-weight: 700 !important;
				}

				.close-wrapper {
					top: 15px !important;
				}

				.modal-content {
					display: flex;
					flex-direction: column;
					flex-grow: 1;
					max-height: calc(540px - 70px);
				}

				.table-content {
					flex-grow: 1;
					overflow-y: auto;
					max-height: calc(540px - 150px);
				}
			}
		}
	}
}

::ng-deep .cdk-overlay-container {
	z-index: 1050;
}

::ng-deep.modal-time-line {
	&:has(~ .cdk-overlay-container > .cdk-overlay-backdrop) {
		z-index: 1049;
	}

	.modal-dialog {
		max-width: 690px;
		width: 100%;
		position: fixed;
		bottom: 0;
		right: 0;
		height: 100%;
		margin: 0px;

		.modal-content {
			height: 100%;
			position: absolute;

			pacto-modal-wrapper {
				max-height: 100%;
				height: 100%;
				position: relative;
				overflow-y: auto;

				.title {
					font-size: 32px;
					margin-top: 35px;
					margin-bottom: 12px;
					margin-left: 54px;
				}

				.close-wrapper {
					position: absolute !important;
					top: 46px !important;
					left: 36px !important;
					font-size: 28px !important;
				}
			}
		}
	}
}

::ng-deep .mdl-cliente-resumo-contrato {
	.modal-dialog {
		max-width: 1000px;
		width: 1000px;

		.modal-content {
			::ng-deep pacto-dialog {
				display: block;

				.pacto-modal-wrapper {
					.modal-titulo {
						@include apply-typography-style("title", 4);
						display: flex;
						align-items: center;
						padding: 16px;

						.close-wrapper {
							position: unset;
							margin-left: auto;
						}
					}
				}
			}
		}
	}
}

.text-liberada {
	color: #037d03;
}

.text-bloqueada {
	color: #af0404;
	font-weight: 700;
}

::ng-deep pacto-relatorio-renderer {
	table.table {
		tr:nth-child(even) {
			background-color: #fafafa;
		}

		tbody {
			tr {
				td {
					span {
						color: #40424c;
					}
				}
			}
		}
	}
}

.cursor-pointer {
	cursor: pointer;
}

.default-text-color {
	color: #494b50;
}

.font-display7 {
	font-family: Nunito Sans;
	font-weight: 600;
	font-style: SemiBold;
	font-size: 14px;
	line-height: 100%;
	letter-spacing: 0px;
	text-align: center;
}

.text {
	margin-left: 8px;
	width: 100%;
	@extend .pct-title5;
	color: $typeDefaultText;
}

.text-green {
	color: #04af04;
}
.text-red {
	color: #af0404;
}
.text-default-color {
	color: $typeDefaultText;
}
