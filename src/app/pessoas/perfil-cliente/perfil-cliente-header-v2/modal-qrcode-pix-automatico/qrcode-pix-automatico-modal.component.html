<div class="background-modal-atividades">
	<div class="content">
		<!-- Header removido para evitar X duplicado -->

		<div class="pos-header">
			<div class="text-center">
				<p>
					O aluno deve escanear o QR Code abaixo (com a câmera do celular ou app
					de leitura de QRCode) para autorizar o Pix recorrente:
				</p>
				<p>O link expira em 1 hora</p>
				<p style="font-weight: bold; color: #212529; margin-bottom: 20px">
					{{ getTipoRecorrenciaText() }}
				</p>

				<div class="qrcode-container">
					<!-- Usando uma API de QR code online para gerar a imagem -->
					<img [src]="getQrCodeUrl()" alt="QR Code" />
				</div>

				<!-- Status de consulta abaixo do QRCode -->
				<div
					*ngIf="codigoPixAutomatico && isPollingActive"
					class="polling-indicator mt-3 text-center">
					<i class="fa fa-spinner fa-spin"></i>
					<small class="text-muted ml-2">Consultando status...</small>
				</div>
			</div>
		</div>

		<div class="footer">
			<div class="share-buttons">
				<pacto-cat-button
					(click)="enviarViaWhatsapp()"
					[disabled]="!url"
					icon="pct pct-whatsapp"
					iconPosition="after"
					label="Enviar via WhatsApp"
					size="LARGE"
					type="OUTLINE"></pacto-cat-button>

				<pacto-cat-button
					(click)="copiarLink()"
					[disabled]="!url"
					icon="pct pct-copy"
					iconPosition="after"
					label="Copiar link"
					size="LARGE"
					type="OUTLINE"></pacto-cat-button>

				<pacto-cat-button
					(click)="enviarViaEmail()"
					[disabled]="!url"
					icon="pct pct-send"
					iconPosition="after"
					label="Enviar por email"
					size="LARGE"
					type="OUTLINE"></pacto-cat-button>
			</div>
			<div class="right">
				<button class="btn btn-primary" (click)="cancelar()" id="btn-fechar">
					Fechar
				</button>
			</div>
		</div>
	</div>
</div>
