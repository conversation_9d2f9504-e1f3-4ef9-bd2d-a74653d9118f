import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	On<PERSON><PERSON>roy,
	OnInit,
	Optional,
} from "@angular/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ZwPactoPayApiDashService } from "projects/zw-pactopay-api/src/public-api";
import { interval, Subscription } from "rxjs";
import { switchMap, takeWhile } from "rxjs/operators";
import { SnotifyService } from "ng-snotify";
import { ZWServletPixAutomaticoApiService } from "zw-servlet-api";
import { SessionService } from "@base-core/client/session.service";

@Component({
	selector: "qrcode-pix-automatico-modal",
	templateUrl: "./qrcode-pix-automatico-modal.component.html",
	styleUrls: ["./qrcode-pix-automatico-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QrcodePixAutomaticoModalComponent implements OnInit, On<PERSON><PERSON>roy {
	@Input() url: string;
	@Input() isNewAuthorization: boolean = true; // Por padrão, assume que é uma nova autorização
	@Input() parcelasSelecionadas: any[] = []; // Para fluxo de criação
	@Input() tipoRecorrencia: string = ""; // Para fluxo de visualização (vem do backend)
	@Input() codigoPixAutomatico: string; // Código do PIX Automático para consultar status
	@Input() zwServletPixAutomaticoApiService: ZWServletPixAutomaticoApiService; // Service passado do modal pai
	@Input() codPessoa: number; // Código da pessoa
	@Input() dadosPessoais: any; // Dados pessoais para obter telefone

	// Propriedades para polling
	private pollingSubscription: Subscription;
	public isPollingActive: boolean = true;
	public statusAtual: string = "Consultando...";
	public statusClass: string = "badge-secondary";
	private statusAnterior: string = null; // Para controlar mudanças de status

	constructor(
		private modal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		@Optional() private zwPactoPayApiDash: ZwPactoPayApiDashService,
		private snotifyService: SnotifyService,
		private pixAutomaticoService: ZWServletPixAutomaticoApiService,
		private sessionService: SessionService
	) {}

	ngOnInit() {
		// Usa o service passado como Input se o injetado não estiver disponível
		const serviceToUse =
			this.zwServletPixAutomaticoApiService || this.pixAutomaticoService;

		// Detecta mudanças para garantir que a URL seja exibida
		this.cd.detectChanges();

		// Emite evento global apenas se for uma nova autorização
		if (this.isNewAuthorization) {
			window.dispatchEvent(new CustomEvent("pixAutomaticoAutorizacaoCriada"));
		}

		// Inicia polling do status se tiver código do PIX Automático e service disponível
		if (this.codigoPixAutomatico && serviceToUse) {
			this.iniciarPollingStatus(serviceToUse);
		}
	}

	ngOnDestroy() {
		// Para o polling quando o componente é destruído
		this.isPollingActive = false;
		if (this.pollingSubscription) {
			this.pollingSubscription.unsubscribe();
		}

		// Dispara eventos para atualizar listas quando modal for destruído
		window.dispatchEvent(new CustomEvent("pixAutomaticoAutorizacaoCriada"));
		window.dispatchEvent(new CustomEvent("pixAutomaticoHistoricoAtualizar"));
	}

	getQrCodeUrl(): string {
		if (!this.url) {
			return "";
		}
		// Faz o encoding correto da URL para evitar truncamento
		const encodedUrl = encodeURIComponent(this.url);
		return `https://api.qrserver.com/v1/create-qr-code/?size=250x250&data=${encodedUrl}`;
	}

	getTipoRecorrenciaText(): string {
		if (this.isNewAuthorization) {
			// Fluxo de criação: verifica se há parcelas selecionadas
			if (this.parcelasSelecionadas && this.parcelasSelecionadas.length > 0) {
				return "Cobrança imediata + Autorização";
			} else {
				return "Somente Autorização";
			}
		} else {
			// Fluxo de visualização: usa o valor do backend
			return this.tipoRecorrencia || "Somente Autorização";
		}
	}

	private iniciarPollingStatus(service: ZWServletPixAutomaticoApiService) {
		if (!service) {
			return;
		}

		// Polling a cada 5 segundos
		this.pollingSubscription = interval(5000)
			.pipe(
				takeWhile(() => this.isPollingActive),
				switchMap(() =>
					service.consultarStatusPixAutomatico(this.codigoPixAutomatico)
				)
			)
			.subscribe(
				(response) => {
					this.atualizarStatus(response);
					this.cd.detectChanges();
				},
				(error) => {
					this.cd.detectChanges();
				}
			);

		// Primeira consulta imediata
		service.consultarStatusPixAutomatico(this.codigoPixAutomatico).subscribe(
			(response) => {
				this.atualizarStatus(response);
				this.cd.detectChanges();
			},
			(error) => {
				this.cd.detectChanges();
			}
		);
	}

	private atualizarStatus(response: any) {
		if (response && response.statusBelvo) {
			const novoStatus = response.statusBelvo;

			// Atualiza o status anterior para próxima comparação
			this.statusAnterior = novoStatus;

			// Se o status for diferente de AWAITING_AUTHORIZATION, finaliza o processo
			if (novoStatus !== "AWAITING_AUTHORIZATION") {
				this.snotifyService.success("Autorização atualizada!");

				// Para o polling
				this.isPollingActive = false;
				if (this.pollingSubscription) {
					this.pollingSubscription.unsubscribe();
				}

				// Dispara eventos para atualizar listas
				window.dispatchEvent(new CustomEvent("pixAutomaticoAutorizacaoCriada"));
				window.dispatchEvent(
					new CustomEvent("pixAutomaticoHistoricoAtualizar")
				);

				// Fecha o modal imediatamente
				this.modal.close();
			}
		}
	}

	// Método removido - não é mais necessário com a nova lógica

	cancelar() {
		// Dispara eventos para atualizar listas quando modal for fechado manualmente
		window.dispatchEvent(new CustomEvent("pixAutomaticoAutorizacaoCriada"));
		window.dispatchEvent(new CustomEvent("pixAutomaticoHistoricoAtualizar"));

		this.modal.close();
	}

	enviarViaWhatsapp() {
		if (this.url) {
			// Verificar se há telefone cadastrado
			if (
				!this.dadosPessoais ||
				!this.dadosPessoais.telefones ||
				this.dadosPessoais.telefones.length === 0
			) {
				this.snotifyService.error(
					"O aluno não possui nenhum telefone cadastrado"
				);
				return;
			}

			const telefone = this.dadosPessoais.telefones[0].numero;
			const mensagem = `Olá!
Para facilitar seus próximos pagamentos, você pode autorizar agora o PIX Automático de forma simples e segura.
Ao clicar no link abaixo, será aberta uma página com a solicitação, e nela você será direcionado para o aplicativo do seu banco para concluir a autorização:

${this.url}`;
			const mensagemEncoded = encodeURIComponent(mensagem);
			const whatsappUrl = `https://api.whatsapp.com/send?phone=${telefone}&text=${mensagemEncoded}`;
			window.open(whatsappUrl, "_blank");
		}
	}

	copiarLink() {
		if (this.url) {
			// Tenta usar a API moderna do clipboard
			if (navigator.clipboard && navigator.clipboard.writeText) {
				navigator.clipboard
					.writeText(this.url)
					.then(() => {
						this.snotifyService.success(
							"Link copiado para a área de transferência"
						);
					})
					.catch((error) => {
						this.copiarLinkFallback();
					});
			} else {
				this.copiarLinkFallback();
			}
		}
	}

	private copiarLinkFallback() {
		try {
			const textArea = document.createElement("textarea");
			textArea.value = this.url;
			textArea.style.position = "fixed";
			textArea.style.left = "-999999px";
			textArea.style.top = "-999999px";
			document.body.appendChild(textArea);
			textArea.focus();
			textArea.select();
			const successful = document.execCommand("copy");
			document.body.removeChild(textArea);

			if (successful) {
				this.snotifyService.success(
					"Link copiado para a área de transferência"
				);
			} else {
				this.snotifyService.error("Erro ao copiar link");
			}
		} catch (error) {
			this.snotifyService.error("Erro ao copiar link");
		}
	}

	enviarViaEmail() {
		if (this.url) {
			const payload = {
				url: this.url,
				chaveZW: this.getChaveFromUrl(),
				codUsuario: this.getCodUsuario(),
				codPessoa: this.getCodPessoa(),
			};

			this.pixAutomaticoService
				.enviarLinkAutorizacaoViaEmail(payload)
				.subscribe(
					(response) => {
						this.snotifyService.success("Email enviado com sucesso!");
					},
					(error) => {
						this.snotifyService.error("Erro ao enviar email. Tente novamente.");
					}
				);
		}
	}

	private getChaveFromUrl(): string {
		if (this.url) {
			// Extrai a chave da URL (parâmetro 'k')
			const urlParams = new URLSearchParams(this.url.split("?")[1]);
			return urlParams.get("k") || "";
		}
		return "";
	}

	private getCodUsuario(): number {
		return this.sessionService.loggedUser && this.sessionService.loggedUser.id
			? this.sessionService.loggedUser.id
			: 0;
	}

	private getCodPessoa(): number {
		// Se foi passado como Input, usa esse valor
		if (this.codPessoa) {
			return this.codPessoa;
		}

		// Caso contrário, retorna 0 (será passado sempre como Input nos fluxos)
		return 0;
	}
}
