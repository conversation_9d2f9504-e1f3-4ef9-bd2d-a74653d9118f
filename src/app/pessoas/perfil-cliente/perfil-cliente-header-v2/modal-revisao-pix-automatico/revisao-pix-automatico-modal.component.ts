import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
} from "@angular/core";

import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { PactoModalRef } from "ui-kit";
import { ZWServletPixAutomaticoApiService } from "zw-servlet-api";
import { QrcodePixAutomaticoModalComponent } from "../modal-qrcode-pix-automatico/qrcode-pix-automatico-modal.component";
import { IncluirParcelasPixAutomaticoModalComponent } from "../modal-incluir-parcelas-pix-automatico/incluir-parcelas-pix-automatico-modal.component";

@Component({
	selector: "pacto-modal-revisao-pix-automatico",
	templateUrl: "./revisao-pix-automatico-modal.component.html",
	styleUrls: ["./revisao-pix-automatico-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RevisaoPixAutomaticoModalComponent implements OnInit {
	@Input() dadosParaRevisao: any;

	formRevisao: FormGroup;
	valorMaximoRange: number = 1000; // Valor máximo do range slider
	valorMinimoAtual: number = 0.01;
	valorMaximoAtual: number = 10;
	periodoVigenciaAtual: number = 1; // 1=3meses, 2=6meses, 3=12meses, 4=24meses, 5=indeterminado
	opcoesVigencia = [3, 6, 12, 24, -1]; // Meses disponíveis (-1 = indeterminado)

	constructor(
		private modal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private fb: FormBuilder,
		private notifyService: SnotifyService,
		private modalService: ModalService,
		private readonly zwServletPixAutomaticoApi: ZWServletPixAutomaticoApiService
	) {}

	/**
	 * Mascara o ID do contrato exibindo asteriscos do 11º ao 27º caractere
	 */
	public obterIdContratoMascarado(): string {
		if (!this.dadosParaRevisao || !this.dadosParaRevisao.idContrato) {
			return "Não informado";
		}

		const idContrato = this.dadosParaRevisao.idContrato;

		// Se o ID tem menos de 11 caracteres, exibe sem máscara
		if (idContrato.length <= 10) {
			return idContrato;
		}

		// Mascara do 11º caractere (índice 10) até o 27º caractere (índice 26)
		const inicio = idContrato.substring(0, 10); // Primeiros 10 caracteres
		const fim = idContrato.length > 27 ? idContrato.substring(27) : ""; // A partir do 28º caractere

		// Calcula quantos asteriscos colocar (máximo 17 caracteres mascarados)
		const caracteresParaMascarar = Math.min(idContrato.length - 10, 17);
		const asteriscos = "*".repeat(caracteresParaMascarar);

		return inicio + asteriscos + fim;
	}

	ngOnInit() {
		// Cria o formulário imediatamente para evitar erros de undefined
		this.initForm();
	}

	private initForm() {
		// Define o range máximo baseado no dobro do valor da parcela maior
		if (this.dadosParaRevisao && this.dadosParaRevisao.valorMaximoSugerido) {
			this.valorMaximoRange = this.dadosParaRevisao.valorMaximoSugerido * 2;
			// Garante que o range máximo seja pelo menos 150
			if (this.valorMaximoRange < 200) {
				this.valorMaximoRange = 200;
			}
		} else {
			this.valorMaximoRange = 200; // Valor padrão se não houver dados
		}

		// Inicializa os valores atuais
		this.valorMinimoAtual = 0.01;
		this.valorMaximoAtual = this.valorMaximoRange; // Define como valor máximo calculado
		this.periodoVigenciaAtual = 5; // Padrão: Indeterminado (posição 5)

		// Cria o formulário
		this.formRevisao = this.fb.group({
			valorMinimo: [
				this.valorMinimoAtual,
				[Validators.required, Validators.min(0.01)],
			],
			valorMaximo: [
				this.valorMaximoAtual,
				[Validators.required, Validators.min(1.01)],
			],
			periodoVigencia: [this.periodoVigenciaAtual, [Validators.required]],
		});
	}

	onValorMinimoChange(event: any) {
		this.valorMinimoAtual = parseFloat(event.target.value);
		this.formRevisao.patchValue({
			valorMinimo: this.valorMinimoAtual,
		});
	}

	onValorMaximoChange(event: any) {
		this.valorMaximoAtual = parseFloat(event.target.value);
		this.formRevisao.patchValue({
			valorMaximo: this.valorMaximoAtual,
		});
	}

	onPeriodoVigenciaChange(event: any) {
		this.periodoVigenciaAtual = parseInt(event.target.value);
		this.formRevisao.patchValue({
			periodoVigencia: this.periodoVigenciaAtual,
		});
	}

	obterTextoVigencia(): string {
		const meses = this.opcoesVigencia[this.periodoVigenciaAtual - 1];
		if (meses === -1) {
			return "Indeterminado";
		}
		return `${meses} ${meses === 1 ? "mês" : "meses"}`;
	}

	calcularDataInicioAutorizacao(): string {
		const hoje = new Date();
		const dataInicio = new Date(hoje);
		dataInicio.setDate(dataInicio.getDate() + 2); // Adiciona 2 dias

		const dia = ("0" + dataInicio.getDate()).slice(-2);
		const mes = ("0" + (dataInicio.getMonth() + 1)).slice(-2);
		const ano = dataInicio.getFullYear();

		return `${dia}/${mes}/${ano}`;
	}

	calcularDataFimAutorizacao(): string {
		if (!this.opcoesVigencia || this.periodoVigenciaAtual < 1) {
			return "Não informado";
		}

		const meses = this.opcoesVigencia[this.periodoVigenciaAtual - 1];

		if (meses === -1) {
			return "Indeterminado (até que realize o cancelamento)";
		}

		// Calcula a partir da data de início (1 dia a partir de hoje)
		const hoje = new Date();
		const dataInicio = new Date(hoje);
		dataInicio.setDate(dataInicio.getDate() + 1);

		const dataFim = new Date(dataInicio);
		dataFim.setMonth(dataFim.getMonth() + meses);
		// Subtrai 1 dia para que a autorização termine no dia anterior ao aniversário
		dataFim.setDate(dataFim.getDate() - 1);

		const dia = ("0" + dataFim.getDate()).slice(-2);
		const mes = ("0" + (dataFim.getMonth() + 1)).slice(-2);
		const ano = dataFim.getFullYear();

		return `${dia}/${mes}/${ano}`;
	}

	obterParcelaPorCodigo(codigo: number): any {
		if (!this.dadosParaRevisao || !this.dadosParaRevisao.lista) {
			return null;
		}
		return this.dadosParaRevisao.lista.find(
			(parcela) => parcela.codigo === codigo
		);
	}

	calcularTotalParcelas(): string {
		if (
			!this.dadosParaRevisao ||
			!this.dadosParaRevisao.codsParcelasSelecionadas ||
			!this.dadosParaRevisao.lista
		) {
			return "R$ 0,00";
		}

		let total = 0;
		this.dadosParaRevisao.codsParcelasSelecionadas.forEach((codigo) => {
			const parcela = this.dadosParaRevisao.lista.find(
				(p) => p.codigo === codigo
			);
			if (parcela && parcela.valor && typeof parcela.valor === "string") {
				const valorNumerico = parseFloat(
					parcela.valor.replace(".", "").replace(",", ".")
				);
				if (!isNaN(valorNumerico)) {
					total += valorNumerico;
				}
			}
		});

		if (total > 0) {
			let valorFormatado = total.toFixed(2).replace(".", ",");
			// Adiciona ponto como separador de milhar se tiver mais de 6 caracteres
			if (valorFormatado.length > 6) {
				const partes = valorFormatado.split(",");
				const inteiro = partes[0];
				const decimal = partes[1];

				const comSeparador = inteiro.slice(0, -3) + "." + inteiro.slice(-3);
				valorFormatado = comSeparador + "," + decimal;
			}
			return "R$ " + valorFormatado;
		}

		return "R$ 0,00";
	}

	voltar() {
		// Fecha o modal atual e reabre o modal de parcelas
		this.modal.close({ voltarParaParcelas: true });

		// Reabre o modal de parcelas
		const modalParcelas: PactoModalRef = this.modalService.open(
			"Escolher parcelas para cobrar agora",
			IncluirParcelasPixAutomaticoModalComponent,
			PactoModalSize.LARGE
		);

		// Repassa os dados necessários
		if (this.dadosParaRevisao) {
			modalParcelas.componentInstance.pessoa = this.dadosParaRevisao.pessoa;
			modalParcelas.componentInstance.instituicao =
				this.dadosParaRevisao.instituicao;
			modalParcelas.componentInstance.codEmpresa =
				this.dadosParaRevisao.codEmpresa;
			modalParcelas.componentInstance.origem = this.dadosParaRevisao.origem;
			modalParcelas.componentInstance.convenio = this.dadosParaRevisao.convenio;
			modalParcelas.componentInstance.username = this.dadosParaRevisao.username;
			modalParcelas.componentInstance.lista = this.dadosParaRevisao.lista;
			modalParcelas.componentInstance.codsParcelasSelecionadas =
				this.dadosParaRevisao.codsParcelasSelecionadas;
			modalParcelas.componentInstance.tipoCobrar =
				this.dadosParaRevisao.tipoCobrar;
			modalParcelas.componentInstance.dadosPessoais =
				this.dadosParaRevisao.dadosPessoais;
			// Mantém a lista de produtos específicos ao voltar
			modalParcelas.componentInstance.listaProdutosSelecionados =
				this.dadosParaRevisao.listaProdutosSelecionados || [];
		}

		// Trata o resultado do modal de parcelas para evitar reabrir o modal de forma de cobrança
		modalParcelas.result
			.then((result) => {
				// Não faz nada aqui para evitar reabrir modais anteriores
				// O modal de parcelas já tem sua própria lógica de fechamento
			})
			.catch(() => {
				// Modal foi fechado sem resultado
			});
	}

	criarQrcodePixAutomatico() {
		if (this.formRevisao.invalid) {
			this.notifyService.error("Preencha todos os campos obrigatórios");
			return;
		}

		if (this.valorMinimoAtual >= this.valorMaximoAtual) {
			this.notifyService.error(
				"O valor mínimo deve ser menor que o valor máximo"
			);
			return;
		}

		if (this.valorMaximoAtual < 1.01) {
			this.notifyService.error("O valor máximo deve ser pelo menos R$ 1,01");
			return;
		}

		// Chama o método original com os dados de revisão
		const mesesVigencia = this.opcoesVigencia[this.periodoVigenciaAtual - 1];
		this.solicitarAutorizacaoPixAutomatico(
			this.valorMinimoAtual,
			this.valorMaximoAtual,
			mesesVigencia
		);
	}

	private solicitarAutorizacaoPixAutomatico(
		valorMinimo: number,
		valorMaximo: number,
		mesesVigencia: number
	) {
		const parcelasSelecionadasComInfo =
			this.dadosParaRevisao.codsParcelasSelecionadas.map((codigo) => {
				const parcela = this.dadosParaRevisao.lista.find(
					(p) => p.codigo === codigo
				);
				return {
					codigo,
					cobraragora:
						parcela && parcela.cobraragora ? parcela.cobraragora : false,
				};
			});

		const payload = {
			parcelas: parcelasSelecionadasComInfo,
			pessoa: this.dadosParaRevisao.pessoa,
			usuario: this.dadosParaRevisao.usuario,
			username: this.dadosParaRevisao.username,
			convenio: this.dadosParaRevisao.convenio,
			origem: this.dadosParaRevisao.origem,
			codEmpresa: this.dadosParaRevisao.codEmpresa,
			instituicaoBancaria: this.dadosParaRevisao.instituicao,
			valorMinimo,
			valorMaximo,
			mesesVigencia,
			idContrato: this.dadosParaRevisao.idContrato,
			tipoCobrar: this.dadosParaRevisao.tipoCobrar,
			// Adiciona a lista de produtos específicos (pode estar vazia)
			// Envia apenas os códigos dos produtos, seguindo o padrão do cartão de crédito
			listaProdutosSelecionados: (
				this.dadosParaRevisao.listaProdutosSelecionados || []
			).map((produto) => produto.codigo),
		};

		this.zwServletPixAutomaticoApi
			.solicitarAutorizacaoPixAutomatico(payload)
			.subscribe(
				(response) => {
					if (response.error) {
						this.notifyService.error(
							response.message ||
								"Erro ao solicitar autorização automatica Pix. Tente novamente."
						);
						return;
					}

					try {
						let contentObj;

						// Se a resposta for uma string, faz parse
						if (typeof response === "string") {
							contentObj = JSON.parse(response);
						}
						// Se a resposta tiver um campo content como string, faz parse
						else if (response.content && typeof response.content === "string") {
							contentObj = JSON.parse(response.content);
						}
						// Se a resposta já for um objeto
						else {
							contentObj = response;
						}

						if (contentObj.sucesso && contentObj.urlPixAutomaticoVendasOnline) {
							this.notifyService.success(
								"Solicitação de pix automático criada com sucesso!"
							);

							this.modal.close({
								parcelasSelecionadas:
									this.dadosParaRevisao.codsParcelasSelecionadas,
								valorMinimo: valorMinimo,
								valorMaximo: valorMaximo,
								closeParent: true,
							});

							this.abrirModalComQRCode(
								contentObj.urlPixAutomaticoVendasOnline,
								contentObj.codigoPixAutomatico,
								parcelasSelecionadasComInfo
							);
						} else {
							this.notifyService.error(
								"Erro ao processar a solicitação: " +
									(contentObj.mensagem || "Erro desconhecido")
							);
						}
					} catch (e) {
						this.notifyService.error("Erro ao processar resposta do servidor");
					}
				},
				(error) => {
					this.notifyService.error("Erro ao processar a solicitação");
				}
			);
	}

	private abrirModalComQRCode(
		urlPixAutomaticoVendasOnline: string,
		codigoPixAutomatico?: number,
		parcelasSelecionadas?: any[]
	) {
		// Fecha o modal atual antes de abrir o QRCode e sinaliza sucesso
		this.modal.close({
			status: "Salvo com sucesso!",
			qrcodeAberto: true,
			closeParent: true,
		});

		const modalQrCode: PactoModalRef = this.modalService.open(
			"Autorização Pix Automático",
			QrcodePixAutomaticoModalComponent,
			PactoModalSize.LARGE
		);

		modalQrCode.componentInstance.url = urlPixAutomaticoVendasOnline;
		modalQrCode.componentInstance.isNewAuthorization = true; // É uma nova autorização

		// Passa as parcelas selecionadas para determinar o tipo de recorrência
		modalQrCode.componentInstance.parcelasSelecionadas =
			parcelasSelecionadas || [];

		// Passa o código do PIX Automático para o modal do QRCode
		if (codigoPixAutomatico) {
			modalQrCode.componentInstance.codigoPixAutomatico =
				codigoPixAutomatico.toString();
		}

		// Passa o service para o modal do QRCode
		modalQrCode.componentInstance.zwServletPixAutomaticoApiService =
			this.zwServletPixAutomaticoApi;

		// Passa o código da pessoa
		if (this.dadosParaRevisao && this.dadosParaRevisao.pessoa) {
			modalQrCode.componentInstance.codPessoa = this.dadosParaRevisao.pessoa;
		}

		// Passa os dados pessoais para obter telefone
		if (this.dadosParaRevisao && this.dadosParaRevisao.dadosPessoais) {
			modalQrCode.componentInstance.dadosPessoais =
				this.dadosParaRevisao.dadosPessoais;
		}

		// Captura fechamento do modal QRCode (tanto por botão quanto por X)
		modalQrCode.result
			.then(() => {
				// Modal fechado com sucesso (botão Fechar)
				// Eventos já são disparados pelo próprio modal
			})
			.catch(() => {
				// Modal fechado com dismiss (botão X ou ESC)
				// Dispara eventos para atualizar listas quando modal for fechado pelo X
				window.dispatchEvent(new CustomEvent("pixAutomaticoAutorizacaoCriada"));
				window.dispatchEvent(
					new CustomEvent("pixAutomaticoHistoricoAtualizar")
				);
			});
	}
}
