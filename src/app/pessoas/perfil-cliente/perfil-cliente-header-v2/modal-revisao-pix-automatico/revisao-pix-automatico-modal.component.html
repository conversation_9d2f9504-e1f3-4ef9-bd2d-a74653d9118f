<div class="background-modal-atividades">
	<div class="pos-header">
		<form *ngIf="formRevisao" [formGroup]="formRevisao" class="form-revisao">
			<!-- Campos de configuração em linha -->
			<div class="row configuracao-campos">
				<div class="col-md-4">
					<div class="form-group-compact">
						<label for="valorMinimo">Valor <PERSON> (R$)</label>
						<div class="valor-display-compact">
							R$ {{ valorMinimoAtual | number : "1.2-2" }}
						</div>
						<input
							type="range"
							id="valorMinimo"
							class="form-range-compact"
							[(ngModel)]="valorMinimoAtual"
							[ngModelOptions]="{ standalone: true }"
							min="0.01"
							max="1.00"
							step="0.01"
							(input)="onValorMinimoChange($event)" />
						<div class="range-labels-compact">
							<span>R$ 0,01</span>
							<span>R$ 1,00</span>
						</div>
					</div>
				</div>

				<div class="col-md-4">
					<div class="form-group-compact">
						<label for="valorMaximo">Valor Máximo (R$)</label>
						<div class="valor-display-compact">
							R$ {{ valorMaximoAtual | number : "1.2-2" }}
						</div>
						<input
							type="range"
							id="valorMaximo"
							class="form-range-compact"
							[(ngModel)]="valorMaximoAtual"
							[ngModelOptions]="{ standalone: true }"
							min="1.01"
							max="{{ valorMaximoRange }}"
							step="0.01"
							(input)="onValorMaximoChange($event)" />
						<div class="range-labels-compact">
							<span>R$ 1,01</span>
							<span>R$ {{ valorMaximoRange | number : "1.2-2" }}</span>
						</div>
					</div>
				</div>

				<div class="col-md-4">
					<div class="form-group-compact">
						<label for="periodoVigencia">Vigência da Autorização</label>
						<div class="valor-display-compact">
							{{ obterTextoVigencia() }}
						</div>
						<input
							type="range"
							id="periodoVigencia"
							class="form-range-compact"
							[(ngModel)]="periodoVigenciaAtual"
							[ngModelOptions]="{ standalone: true }"
							min="1"
							max="5"
							step="1"
							(input)="onPeriodoVigenciaChange($event)" />
						<div class="range-labels-compact">
							<span>3 meses</span>
							<span>Indeterminado</span>
						</div>
					</div>
				</div>
			</div>

			<div class="row mt-3">
				<div class="col-12">
					<!-- Resumo quando NÃO há parcelas selecionadas -->
					<div
						*ngIf="
							!dadosParaRevisao ||
							!dadosParaRevisao.codsParcelasSelecionadas ||
							dadosParaRevisao.codsParcelasSelecionadas.length === 0
						"
						class="info-parcelas">
						<h6>Resumo da autorização:</h6>
						<div class="row">
							<div class="col-md-6 info-esquerda">
								<p>
									<strong>Tipo da autorização:</strong>
									Somente autorização
								</p>
								<p>
									<strong>Frequência da cobrança:</strong>
									Mensal
								</p>
								<p>
									<strong>ID contrato:</strong>
									{{ obterIdContratoMascarado() }}
								</p>
							</div>
							<div class="col-md-6 info-direita">
								<p>
									<strong>Início da autorização:</strong>
									{{ calcularDataInicioAutorizacao() }}
								</p>
								<p>
									<strong>Fim da autorização:</strong>
									{{ calcularDataFimAutorizacao() }}
								</p>
							</div>
						</div>
					</div>

					<!-- Resumo quando HÁ parcelas selecionadas -->
					<!-- Bloco 1: Tipo da autorização -->
					<div
						*ngIf="
							dadosParaRevisao &&
							dadosParaRevisao.codsParcelasSelecionadas &&
							dadosParaRevisao.codsParcelasSelecionadas.length > 0
						"
						class="info-parcelas">
						<div class="row">
							<div class="col-md-6 info-esquerda">
								<p>
									<strong>Tipo da autorização:</strong>
									Pagamento imediato + autorização
								</p>
								<p>
									<strong>Frequência da cobrança:</strong>
									Mensal
								</p>
								<p>
									<strong>ID contrato:</strong>
									{{ obterIdContratoMascarado() }}
								</p>
							</div>
							<div class="col-md-6 info-direita">
								<p>
									<strong>Início da autorização:</strong>
									{{ calcularDataInicioAutorizacao() }}
								</p>
								<p>
									<strong>Fim da autorização:</strong>
									{{ calcularDataFimAutorizacao() }}
								</p>
							</div>
						</div>
					</div>

					<!-- Bloco 2: Detalhes das parcelas -->
					<div
						*ngIf="
							dadosParaRevisao &&
							dadosParaRevisao.codsParcelasSelecionadas &&
							dadosParaRevisao.codsParcelasSelecionadas.length > 0
						"
						class="info-parcelas">
						<div class="parcelas-detalhes">
							<strong>Será cobrado imediato:</strong>
							<div class="parcelas-lista">
								<div
									*ngFor="
										let codigoParcela of dadosParaRevisao.codsParcelasSelecionadas;
										let i = index
									"
									class="parcela-item">
									<span class="parcela-info">
										{{
											obterParcelaPorCodigo(codigoParcela)?.descricao ||
												"Parcela " + codigoParcela
										}}
										-
										<span class="parcela-valor">
											{{
												obterParcelaPorCodigo(codigoParcela)?.valor || "R$ 0,00"
											}}
										</span>
										-
										<span class="parcela-vencimento">
											{{
												obterParcelaPorCodigo(codigoParcela)?.dataVencimento ||
													"Data não informada"
											}}
										</span>
									</span>
								</div>
							</div>
							<div class="total-parcelas">
								<strong>
									Total:
									{{ dadosParaRevisao.codsParcelasSelecionadas.length }}
									parcelas | Valor: {{ calcularTotalParcelas() }}
								</strong>
							</div>
						</div>
					</div>
				</div>
			</div>
		</form>
	</div>

	<div class="rodape">
		<div class="right">
			<pacto-cat-button
				(click)="voltar()"
				i18n-label="@@label-voltar"
				id="btn-voltar"
				label="Voltar"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>

			<pacto-cat-button
				(click)="criarQrcodePixAutomatico()"
				[disabled]="!formRevisao || formRevisao.invalid"
				i18n-label="@@label-criar-qrcode"
				id="btn-criar-qrcode"
				label="Criar QRCode para pix automático"
				size="LARGE"
				type="PRIMARY"></pacto-cat-button>
		</div>
	</div>
</div>
<ng-snotify></ng-snotify>
