// Força o modal pai a ter largura maior
::ng-deep .modal-dialog {
	max-width: 1200px !important;
	width: 1200px !important;
	min-width: 1200px !important;
}

::ng-deep .modal-content {
	width: 100% !important;
}

.background-modal-atividades {
	width: 100%;
	max-width: 100%;
	margin: 0 auto;
	position: relative;
	overflow: hidden;

	// Remove margens negativas das rows do Bootstrap
	.row {
		margin-left: 0 !important;
		margin-right: 0 !important;
	}

	// Controla padding das colunas
	[class*="col-"] {
		padding-left: 10px;
		padding-right: 10px;
	}

	.pos-header {
		padding: 30px;
		width: 100%;
		box-sizing: border-box;

		.form-revisao {
			.configuracao-campos {
				background: #f8f9fa;
				border: 1px solid #e9ecef;
				border-radius: 8px;
				padding: 20px;
				margin-bottom: 25px;
				margin-left: 0;
				margin-right: 0;

				.col-md-4 {
					padding-left: 10px;
					padding-right: 10px;
				}
			}

			.form-group-compact {
				margin-bottom: 0;

				label {
					font-weight: 600;
					color: #495057;
					margin-bottom: 6px;
					display: block;
					font-size: 13px;
				}

				.valor-display-compact {
					background: #fff;
					border: 1px solid #e9ecef;
					border-radius: 4px;
					padding: 6px 8px;
					margin-bottom: 8px;
					font-weight: 600;
					color: #007bff;
					text-align: center;
					font-size: 14px;
					min-height: 32px;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.form-range-compact {
					width: 100%;
					height: 4px;
					background: #ddd;
					border-radius: 2px;
					outline: none;
					margin: 8px 0;
					-webkit-appearance: none;

					&::-webkit-slider-thumb {
						-webkit-appearance: none;
						appearance: none;
						width: 16px;
						height: 16px;
						border-radius: 50%;
						background: #007bff;
						cursor: pointer;
						border: 2px solid #fff;
						box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
					}

					&::-moz-range-thumb {
						width: 16px;
						height: 16px;
						border-radius: 50%;
						background: #007bff;
						cursor: pointer;
						border: 2px solid #fff;
						box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
					}

					&:focus {
						&::-webkit-slider-thumb {
							box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
						}
					}
				}

				.range-labels-compact {
					display: flex;
					justify-content: space-between;
					font-size: 10px;
					color: #6c757d;
					margin-top: 3px;
				}
			}
			.form-group {
				margin-bottom: 25px;

				label {
					font-weight: 600;
					color: #495057;
					margin-bottom: 8px;
					display: block;
				}

				.valor-display {
					background: #f8f9fa;
					border: 1px solid #e9ecef;
					border-radius: 4px;
					padding: 8px 12px;
					margin-bottom: 10px;
					font-weight: 600;
					color: #007bff;
					text-align: center;
					font-size: 16px;
				}

				.form-range {
					width: 100%;
					height: 6px;
					background: #ddd;
					border-radius: 3px;
					outline: none;
					margin: 10px 0;
					-webkit-appearance: none;

					&::-webkit-slider-thumb {
						-webkit-appearance: none;
						appearance: none;
						width: 20px;
						height: 20px;
						border-radius: 50%;
						background: #007bff;
						cursor: pointer;
						border: 2px solid #fff;
						box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
					}

					&::-moz-range-thumb {
						width: 20px;
						height: 20px;
						border-radius: 50%;
						background: #007bff;
						cursor: pointer;
						border: 2px solid #fff;
						box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
					}

					&:focus {
						&::-webkit-slider-thumb {
							box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
						}
					}
				}

				.range-labels {
					display: flex;
					justify-content: space-between;
					font-size: 12px;
					color: #6c757d;
					margin-top: 5px;
				}

				.form-control {
					border: 1px solid #ced4da;
					border-radius: 4px;
					padding: 10px 12px;
					font-size: 14px;
					transition: border-color 0.15s ease-in-out,
						box-shadow 0.15s ease-in-out;

					&:focus {
						border-color: #007bff;
						box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
						outline: 0;
					}

					&.is-invalid {
						border-color: #dc3545;
					}
				}

				.text-danger {
					margin-top: 5px;

					small {
						font-size: 12px;
					}
				}
			}

			.info-parcelas {
				background: #f8f9fa;
				padding: 15px;
				border-radius: 4px;
				border-left: 4px solid #007bff;

				h6 {
					margin-bottom: 15px;
					color: #495057;
					font-weight: 600;
				}

				p {
					margin: 5px 0;
					color: #6c757d;
					font-size: 14px;
				}

				.info-esquerda {
					position: relative;
				}

				.info-direita {
					position: relative;

					&::before {
						content: "";
						position: absolute;
						left: 0;
						top: 0;
						bottom: 0;
						width: 1px;
						background-color: #dee2e6;
						opacity: 0.7;
					}

					padding-left: 20px;
				}

				.row {
					margin-left: 0;
					margin-right: 0;
				}

				.parcelas-detalhes {
					margin-top: 0px;

					> strong {
						color: #6c757d;
						font-size: 14px;
						display: block;
						margin-bottom: 8px;
					}

					.parcelas-lista {
						background: #fff;
						border: 1px solid #e9ecef;
						border-radius: 4px;
						padding: 10px;
						max-height: 115px;
						overflow-y: auto;

						.parcela-item {
							padding: 4px 0;
							border-bottom: 1px solid #f1f3f4;

							&:last-child {
								border-bottom: none;
							}

							.parcela-info {
								font-size: 12px;
								color: #495057;
								line-height: 1.3;

								.parcela-valor {
									font-weight: 600;
									color: #28a745;
								}

								.parcela-vencimento {
									color: #6c757d;
									font-style: italic;
								}
							}
						}
					}

					.total-parcelas {
						margin-top: 8px;
						padding: 8px 0;
						text-align: left;

						strong {
							color: #495057;
							font-size: 14px;
						}
					}
				}
			}
		}
	}

	.rodape {
		padding: 20px 30px;
		border-top: 1px solid #e9ecef;
		background: #f8f9fa;
		width: 100%;
		box-sizing: border-box;

		.right {
			display: flex;
			justify-content: flex-end;
			gap: 10px;
		}
	}
}
