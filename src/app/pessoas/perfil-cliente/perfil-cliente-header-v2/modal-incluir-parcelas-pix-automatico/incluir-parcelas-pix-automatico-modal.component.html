<div class="background-modal-atividades">
	<div class="content">
		<div class="header">
			<div class="row">
				<div class="col-md-9">
					<div class="row">
						<div class="titulo-msg" style="margin-left: 20px">
							Nova autorização de cobrança para Pix Automático
						</div>
					</div>
				</div>

				<div class="close-wrapper">
					<i (click)="cancelar()" class="pct pct-x"></i>
				</div>
			</div>
		</div>

		<div class="mensagem-alerta">
			<div class="alerta-conteudo">
				<i class="pct pct-warning"></i>
				<span style="display: flex; margin-left: 30px; margin-top: 10px">
					Selecione parcela(s) abaixo somente se desejar que o aluno pague-as de
					imediato antes de autorizar o pix automático.
				</span>
			</div>
		</div>

		<div class="pos-header pretty-scroll">
			<div #listadiv class="lista">
				<table class="scrollable-table">
					<thead>
						<tr>
							<th class="tdcodigo">
								<div class="containerth">
									<i
										(click)="desmarcarTodos()"
										*ngIf="todosMarcados"
										class="pct pct-check-square"></i>
									<i
										(click)="marcarTodos()"
										*ngIf="!todosMarcados"
										class="pct pct-square"></i>
									<span>Código</span>
								</div>
							</th>
							<th class="tddesc">
								<div class="containerth">
									<span>Descrição</span>
								</div>
							</th>
							<th class="tdvenc">
								<div class="containerth">
									<span>Data de vencimento</span>
								</div>
							</th>
							<th class="tdvalor">
								<div class="containerth">
									<span>Valor</span>
								</div>
							</th>
						</tr>
					</thead>
					<tbody>
						<tr
							*ngFor="
								let parcela of lista;
								let even = even;
								let lastRow = last;
								let rowIndex = index
							"
							[ngClass]="{ 'zebra-row': even }">
							<td class="tdcodigo">
								<div (click)="toggle(parcela.codigo)" style="cursor: pointer">
									<i
										*ngIf="marcado(parcela.codigo)"
										class="pct pct-check-square"></i>
									<i
										*ngIf="!marcado(parcela.codigo)"
										class="pct pct-square"></i>
									<span>{{ parcela.codigo }}</span>
								</div>
							</td>
							<td class="tddesc">
								<div>
									{{ parcela.descricao }}
								</div>
							</td>
							<td class="tdvenc">
								<div>
									{{ parcela.dataVencimento }}
								</div>
							</td>
							<td class="tdvalor">
								<div>
									{{ parcela.valor }}
								</div>
							</td>
						</tr>
						<!-- Mensagem quando não há parcelas -->
						<tr *ngIf="!lista || lista.length === 0">
							<td
								colspan="4"
								style="text-align: center; padding: 20px; color: #666">
								Aluno não possui parcelas em aberto
							</td>
						</tr>
					</tbody>
				</table>
			</div>

			<div class="rodape">
				<div class="container-conteudo-lado-esquerdo">
					<div class="alunos-selecionados" style="width: 260px">
						<span>Selecionadas:</span>
						<span class="total">
							{{ codsParcelasSelecionadas.length }} | R$
							{{ valorTotalParcelasSelecionadas }}
						</span>
					</div>
				</div>

				<div class="right">
					<pacto-cat-button
						(click)="voltar()"
						i18n-label="@@label-voltar"
						id="btn-voltar"
						label="Voltar"
						size="LARGE"
						type="OUTLINE"></pacto-cat-button>

					<pacto-cat-button
						(click)="abrirModalRevisao()"
						i18n-label="@@label-revisar-btn"
						id="btn-revisar"
						label="Próxima etapa"
						size="LARGE"
						type="PRIMARY"></pacto-cat-button>
				</div>
			</div>
		</div>
	</div>
</div>
<ng-snotify></ng-snotify>
