import {
	ChangeDetectionStrategy,
	ChangeDetectorRef,
	Component,
	ElementRef,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";

import { DecimalPipe } from "@angular/common";
import { FormControl } from "@angular/forms";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { SnotifyService } from "ng-snotify";
import { GridFilterConfig, PactoDataGridConfig, PactoModalRef } from "ui-kit";
import { ZwPactoPayApiDashService } from "zw-pactopay-api";
import { ClienteService } from "../../../../microservices/personagem/cliente/cliente.service";
import { QrcodePixAutomaticoModalComponent } from "../modal-qrcode-pix-automatico/qrcode-pix-automatico-modal.component";
import { RevisaoPixAutomaticoModalComponent } from "../modal-revisao-pix-automatico/revisao-pix-automatico-modal.component";

@Component({
	selector: "pacto-modal-incluir-parcelas-pix-automatico",
	templateUrl: "./incluir-parcelas-pix-automatico-modal.component.html",
	styleUrls: ["./incluir-parcelas-pix-automatico-modal.component.scss"],
	changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IncluirParcelasPixAutomaticoModalComponent implements OnInit {
	@ViewChild("listadiv", { static: true }) listadiv: ElementRef;
	ready = false;
	table: PactoDataGridConfig;
	filterConfig: GridFilterConfig;
	pessoa: number;
	@Input()
	instituicao: string;
	@Input()
	codEmpresa: number;
	@Input()
	origem: number;
	@Input()
	tipoCobrar: number;
	usuario: number;
	chave: string;
	tipoControl = new FormControl("1");
	todosMarcados = false;
	lista: Array<any> = [];
	codsParcelasSelecionadas: Array<number> = [];
	data: any = {
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		content: 0,
		page: 0,
	};
	@Input()
	convenio: any;
	valorTotalParcelasSelecionadas: string = "0,00";
	@Input()
	username: string;
	@Input()
	dadosPessoais: any;
	@Input()
	listaProdutosSelecionados: any[] = [];

	private idContrato: string;

	constructor(
		private modal: NgbActiveModal,
		private cd: ChangeDetectorRef,
		private decimalPipe: DecimalPipe,
		private modalService: ModalService
	) {}

	ngOnInit() {
		setTimeout(() => {
			this.tipoControl = new FormControl(3);
			this.ready = true;

			// Gera o ID do contrato com a matrícula do aluno
			this.idContrato = this.gerarIdContrato();

			this.calcularValorTotalParcelasSelecionadas();
			this.cd.detectChanges();
		}, 100);
	}

	public abrirModalRevisao() {
		// Calcula o valor da maior parcela
		let maiorValor = 0;
		this.lista.forEach((parcela) => {
			if (parcela.valor && typeof parcela.valor === "string") {
				const valorNumerico = parseFloat(
					parcela.valor.replace(".", "").replace(",", ".")
				);
				if (!isNaN(valorNumerico) && valorNumerico > maiorValor) {
					maiorValor = valorNumerico;
				}
			}
		});

		// Prepara os dados para enviar ao modal de revisão
		const dadosParaRevisao = {
			pessoa: this.pessoa,
			usuario: this.usuario,
			username: this.username,
			instituicao: this.instituicao,
			codEmpresa: this.codEmpresa,
			origem: this.origem,
			convenio: this.convenio,
			lista: this.lista,
			codsParcelasSelecionadas: [...this.codsParcelasSelecionadas],
			valorMinimoSugerido: 0.01,
			valorMaximoSugerido: maiorValor,
			idContrato: this.idContrato,
			tipoCobrar: this.tipoCobrar,
			dadosPessoais: this.dadosPessoais,
			// Passa a lista de produtos específicos
			listaProdutosSelecionados: this.listaProdutosSelecionados,
		};

		// Abre o modal de revisão primeiro
		const modalRevisao: PactoModalRef = this.modalService.open(
			"Nova autorização de cobrança para Pix Automático",
			RevisaoPixAutomaticoModalComponent,
			PactoModalSize.LARGE
		);

		// Passa os dados para o modal de revisão
		modalRevisao.componentInstance.dadosParaRevisao = dadosParaRevisao;

		// Fecha o modal atual após abrir o modal de revisão
		this.modal.close({ abrirRevisao: true });

		// Trata o resultado do modal de revisão
		modalRevisao.result
			.then((result) => {
				if (result && result.closeParent) {
					// Se o modal de revisão foi fechado com sucesso (QRCode criado), não precisa fazer nada
					// pois este modal já foi fechado
				}
			})
			.catch(() => {
				// Modal foi fechado sem resultado (cancelado ou voltou)
			});
	}

	marcarTodos() {
		this.codsParcelasSelecionadas = this.lista.map((parcela) => parcela.codigo);
		this.todosMarcados = true;

		// Verifica parcelas do mesmo mês e atualiza totais
		this.calcularValorTotalParcelasSelecionadas();
		this.cd.detectChanges();
	}

	desmarcarTodos() {
		this.codsParcelasSelecionadas = [];
		this.todosMarcados = false;
		this.calcularValorTotalParcelasSelecionadas();
		this.cd.detectChanges();
	}

	transformValueMoney(v: number): string {
		return this.decimalPipe.transform(
			parseFloat(v.toString().replace(",", ".")),
			"1.2-2"
		);
	}

	marcado(matricula): boolean {
		// tslint:disable-next-line:radix
		return this.codsParcelasSelecionadas.includes(parseInt(matricula));
	}

	toggle(codigo: number) {
		const index = this.codsParcelasSelecionadas.indexOf(codigo);

		if (index === -1) {
			// Se não estiver selecionado, adiciona
			this.codsParcelasSelecionadas.push(codigo);
		} else {
			// Se já estiver selecionado, remove
			this.codsParcelasSelecionadas.splice(index, 1);
		}

		// Atualiza o valor total
		this.calcularValorTotalParcelasSelecionadas();

		// Atualiza a flag de todos marcados
		this.todosMarcados =
			this.codsParcelasSelecionadas.length === this.lista.length;

		this.cd.detectChanges();
	}

	calcularValorTotalParcelasSelecionadas() {
		this.valorTotalParcelasSelecionadas = "0,00";
		let total = 0;

		this.codsParcelasSelecionadas.forEach((parcelaSelecionada) => {
			const parcelaEncontrada = this.lista.find(
				(parcela) => parcela.codigo === parcelaSelecionada
			);
			if (parcelaEncontrada && typeof parcelaEncontrada.valor === "string") {
				const valorNumerico = parseFloat(
					parcelaEncontrada.valor.replace(".", "").replace(",", ".")
				);
				if (!isNaN(valorNumerico)) {
					total += valorNumerico;
				}
			}
		});

		if (total > 0) {
			let valorFormatado = total.toFixed(2).replace(".", ",");
			// Adiciona ponto como separador de milhar se tiver mais de 6 caracteres
			if (valorFormatado.length > 6) {
				const partes = valorFormatado.split(",");
				const inteiro = partes[0];
				const decimal = partes[1];

				const comSeparador = inteiro.slice(0, -3) + "." + inteiro.slice(-3);
				valorFormatado = comSeparador + "," + decimal;
			}
			this.valorTotalParcelasSelecionadas = valorFormatado;
		}

		this.cd.detectChanges();
	}

	private gerarIdContrato(): string {
		// Gera um UUID e remove os hífens para economizar espaço
		const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"
			.replace(/[xy]/g, function (c) {
				const r = (Math.random() * 16) | 0;
				const v = c === "x" ? r : (r & 0x3) | 0x8;
				return v.toString(16);
			})
			.replace(/-/g, ""); // Remove hífens

		// Obtém a matrícula do aluno
		const matricula =
			this.dadosPessoais && this.dadosPessoais.matricula
				? this.dadosPessoais.matricula
				: "";

		// Prefixo "PIXAUT" + UUID + matrícula
		const prefixo = "PIXAUT";

		// Calcula o espaço disponível para o UUID considerando o prefixo e a matrícula
		const espacoDisponivel = 35 - prefixo.length - matricula.length;

		// Se não há espaço suficiente, usa apenas prefixo + matrícula
		if (espacoDisponivel <= 0) {
			return prefixo + matricula;
		}

		// Trunca o UUID para caber no espaço disponível
		const uuidTruncado = uuid.substring(0, espacoDisponivel);

		return prefixo + uuidTruncado + matricula;
	}

	public cancelar(): void {
		this.modal.close();
	}

	public voltar(): void {
		// Fecha o modal atual e sinaliza que deve reabrir o modal de forma de cobrança
		this.modal.close({ voltarParaFormaCobranca: true });
	}
}
