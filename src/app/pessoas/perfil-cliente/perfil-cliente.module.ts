import { AmChartsModule } from "@amcharts/amcharts3-angular";
import {
	CommonModule,
	DecimalPipe,
	DeprecatedI18NPipesModule,
} from "@angular/common";
import { HTTP_INTERCEPTORS } from "@angular/common/http";
import { NgModule, NO_ERRORS_SCHEMA } from "@angular/core";
import { AvaliacaoFisicaCoreModule } from "@avaliacao-fisica-core/avaliacao-fisica-core.module";
import { AddIdEmpresaHeadersInterceptor } from "@base-core/rest/add-id-empresa-headers.interceptor";
import { EntityConlictInteceptor } from "@base-core/rest/entity-conlict.inteceptor";
import { ExpiredTokenInterceptor } from "@base-core/rest/expired-token.interceptor";
import { LoaderInterceptor } from "@base-core/rest/loader.interceptor";
import { MessageNotTratedInteceptor } from "@base-core/rest/message-not-trated.inteceptor";
import { ShareInterceptor } from "@base-core/rest/share.interceptor";
import { BaseSharedModule } from "src/app/base/base-shared/base-shared.module";
import { LogAtividadeComponent } from "src/app/base/log-atividade/components/log-atividade.component";
import { CobrancaModule } from "src/app/cobranca/cobranca.module";
import { AcoesDeDetalhamentoModule } from "src/app/cobranca/components/acoes-de-detalhamento/acoes-de-detalhamento.module";
import { RelatorioCobrancaModule } from "src/app/cobranca/components/relatorio-cobranca/relatorio-cobranca.module";
import { StatusParcelaModule } from "src/app/cobranca/components/status-parcela/status-parcela.module";
import { StatusPixModule } from "src/app/cobranca/components/status-pix/status-pix.module";
import { StatusTransacaoModule } from "src/app/cobranca/components/status-transacao/status-transacao.module";
import { GraduacaoModule } from "src/app/graduacao/graduacao.module";
import { TokenInterceptor } from "src/app/microservices/client-discovery/token.interceptor";
import { GraduacaoMsModule } from "src/app/microservices/graduacao/graduacao-ms.module";
import { MontagemTreinoModule } from "src/app/treino/montagem-treino/montagem-treino.module";
import { CatAutocompleteModule, CatTolltipModule } from "ui-kit";
import { AcessosComponent } from "./acessos/acessos.component";
import { DetalhesDoAcessoComponent } from "./acessos/detalhes-do-acesso/detalhes-do-acesso.component";
import { VerAcessosComponent } from "./acessos/ver-acessos/ver-acessos.component";
import { AvaliacaoContainerComponent } from "./avaliacao/avaliacao-container/avaliacao-container.component";
import { CardPerfilAvaliacaoComponent } from "./avaliacao/card-perfil-avaliacao/card-perfil-avaliacao.component";

import { ModalReferenciaImcComponent } from "./avaliacao/modal-referencia-imc/modal-referencia-imc.component";
import { ModalReferenciaPercentualGorduraComponent } from "./avaliacao/modal-referencia-percentual-gordura/modal-referencia-percentual-gordura.component";
import { UltimaAvaliacaoComponent } from "./avaliacao/ultima-avaliacao/ultima-avaliacao.component";
import { BoletimVisitaComponent } from "./boletim-visita/boletim-visita.component";
import { ModalDetalharBvComponent } from "./boletim-visita/modal-detalhar-bv/modal-detalhar-bv.component";
import { AcessoCatracaComponent } from "./configuracoes-cliente/acesso-catraca/acesso-catraca.component";
import { ConfiguracoesClienteComponent } from "./configuracoes-cliente/configuracoes-cliente.component";
import { DadosAcessosComponent } from "./configuracoes-cliente/dados-acessos/dados-acessos.component";
import { DadosBasicosComponent } from "./configuracoes-cliente/dados-basicos/dados-basicos.component";
import { DadosFinanceirosComponent } from "./configuracoes-cliente/dados-financeiros/dados-financeiros.component";
import { FamiliaresComponent } from "./configuracoes-cliente/familiares/familiares.component";
import { GruposClassificacaoComponent } from "./configuracoes-cliente/grupos-classificacao/grupos-classificacao.component";
import { ModalAdicionarPeriodoComponent } from "./configuracoes-cliente/modal-adicionar-periodo/modal-adicionar-periodo.component";
import { ModalAlteracaoSenhaComponent } from "./configuracoes-cliente/modal-alteracao-senha/modal-alteracao-senha.component";
import { ModalHistoricoAcessoComponent } from "./configuracoes-cliente/modal-historico-acesso/modal-historico-acesso.component";
import { ReplicarEmpresaComponent } from "./configuracoes-cliente/replicar-empresa/replicar-empresa.component";
import { ConfiguracoesRhComponent } from "./configuracoes-cliente/rh/rh.component";
import { TabelaAfastamentoFeriasComponent } from "./configuracoes-cliente/rh/tabela-afastamento-ferias/tabela-afastamento-ferias.component";
import { TabelaDocumentosComponent } from "./configuracoes-cliente/rh/tabela-documentos/tabela-documentos.component";
import { TirarFotoComponent } from "./configuracoes-cliente/tirar-foto/tirar-foto.component";
import { UploadDocsComponent } from "./configuracoes-cliente/upload-docs/upload-docs.component";
import { HistoricoVinculoComponent } from "./configuracoes-cliente/vinculos/historico-vinculo.component";
import { VinculosComponent } from "./configuracoes-cliente/vinculos/vinculos.component";
import { PerfilClienteContratosModule } from "./contratos/perfil-cliente-contratos.module";
import { CadastrarConvidadoComponent } from "./convidados/cadastrar-convidado/cadastrar-convidado.component";
import { ConsultarCepComponent } from "./convidados/consultar-cep/consultar-cep.component";
import { HistoricoConvidadosComponent } from "./convidados/historico-convidados/historico-convidados.component";
import { CrmActionComponent } from "./crm/crm-action/crm-action.component";
import { CrmAgendaComponent } from "./crm/crm-agenda/crm-agenda.component";
import { CrmAppComponent } from "./crm/crm-app/crm-app.component";
import { CrmContainerComponent } from "./crm/crm-container/crm-container.component";
import { CrmEmailComponent } from "./crm/crm-email/crm-email.component";
import { CrmListaComponent } from "./crm/crm-lista/crm-lista.component";
import { CrmObjecaoComponent } from "./crm/crm-objecao/crm-objecao.component";
import { CrmPessoalComponent } from "./crm/crm-pessoal/crm-pessoal.component";
import { CrmSmsComponent } from "./crm/crm-sms/crm-sms.component";
import { CrmTelefoneComponent } from "./crm/crm-telefone/crm-telefone.component";
import { CrmWhatsappComponent } from "./crm/crm-whatsapp/crm-whatsapp.component";
import { AtestadoMedicoTableComponent } from "./documentos/documentos-container/atestado-medico-table/atestado-medico-table.component";
import { DocumentosContainerComponent } from "./documentos/documentos-container/documentos-container.component";
import { DocumentosTableComponent } from "./documentos/documentos-container/documentos-table/documentos-table.component";
import { HistoricoAnexosTableComponent } from "./documentos/documentos-container/historico-anexos-table/historico-anexos-table.component";
import { ModalComprovanteVacinaComponent } from "./documentos/documentos-container/modal-comprovante-vacina/modal-comprovante-vacina.component";
import { ModalUploadArquivosComponent } from "./documentos/documentos-container/modal-upload-arquivos/modal-upload-arquivos.component";
import { FinanceiroComprasComponent } from "./financeiro/financeiro-compras/financeiro-compras.component";
import { ImprimirContratoPrestacaoServicoComponent } from "./financeiro/financeiro-compras/imprimir-contrato-prestacao-servico/imprimir-contrato-prestacao-servico.component";
import { ModalActionDetailParcelaComponent } from "./financeiro/financeiro-compras/modals/modal-action-detail-parcela/modal-action-detail-parcela.component";
import { ModalEditFormaPagamentoComponent } from "./financeiro/financeiro-compras/modals/modal-action-edit/modal-edit-forma-pagamento.component";
import { ModalActionEstornarComponent } from "./financeiro/financeiro-compras/modals/modal-action-estornar/modal-action-estornar.component";
import { ModalActionNfceComponent } from "./financeiro/financeiro-compras/modals/modal-action-nfce/modal-action-nfce.component";
import { ModalActionNfseComponent } from "./financeiro/financeiro-compras/modals/modal-action-nfse/modal-action-nfse.component";
import { ModalActionSendComponent } from "./financeiro/financeiro-compras/modals/modal-action-send/modal-action-send.component";
import { ModalDetalheParcelasComponent } from "./financeiro/financeiro-compras/modals/modal-detalhe-parcelas/modal-detalhe-parcelas.component";
import { ParcelasCobradasNoProdutoComponent } from "./financeiro/financeiro-compras/parcelas-cobradas-no-produto/produtos-cobrados-na-parcela.component";
import { FinanceiroContainerComponent } from "./financeiro/financeiro-container/financeiro-container.component";
import { FinanceiroProdutosVencimentosComponent } from "./financeiro/financeiro-produtos-vencimentos/financeiro-produtos-vencimentos.component";
import { ModalActionEditVencimentosComponent } from "./financeiro/financeiro-produtos-vencimentos/modals/modal-action-edit-vencimentos/modal-action-edit-vencimentos.component";
import { ModalActionRenovarProdutoComponent } from "./financeiro/financeiro-produtos-vencimentos/modals/modal-action-renovar-produto/modal-action-renovar-produto.component";
import { FinanceiroRecibosComponent } from "./financeiro/financeiro-recibos/financeiro-recibos.component";
import { TelaClienteConsultaReciboComponent } from "./financeiro/financeiro-recibos/tela-cliente-consulta-recibo/tela-cliente-consulta-recibo.component";
import { GraduacaoComponent } from "./graduacao/graduacao.component";
import { GympassComponent } from "./gympass/gympass.component";
import { HistoricoIndicacoesComponent } from "./historico-indicacoes/historico-indicacoes.component";
import { IntegracoesGympassComponent } from "./integracoes/integracoes-gympass/integracoes-gympass.component";
import { IntegracoesTotalpassComponent } from "./integracoes/integracoes-totalpass/integracoes-totalpass.component";
import { ModalConvidadosComponent } from "./modal-convidados/modal-convidados.component";
import { AutorizacaoDeCobrancaComponent } from "./pacto-pay/autorizacao-de-cobranca/autorizacao-de-cobranca.component";
import { DualMultiSelectComponent } from "./pacto-pay/autorizacao-de-cobranca/dual-multi-select/dual-multi-select.component";
import { FormaDeCobrancaComponent } from "./pacto-pay/autorizacao-de-cobranca/forma-de-cobranca/forma-de-cobranca.component";
import { ModalCobrancaAutomaticaComponent } from "./pacto-pay/autorizacao-de-cobranca/modal-cobranca-automatica/modal-cobranca-automatica.component";
import { ModalNotificacaoComponent } from "./pacto-pay/autorizacao-de-cobranca/modal-notificacao/modal-notificacao.component";
import { ModalProdutosEspecificosComponent } from "./pacto-pay/autorizacao-de-cobranca/modal-produtos-especificos/modal-produtos-especificos.component";
import { PactoPayContainerComponent } from "./pacto-pay/pacto-pay-container/pacto-pay-container.component";
import { PerfilClienteContainerComponent } from "./perfil-cliente-container/perfil-cliente-container.component";
import { AlterarMatriculaComponent } from "./perfil-cliente-header-v2/alterar-matricula/alterar-matricula.component";
import { AvisosComponent } from "./perfil-cliente-header-v2/avisos/avisos.component";
import { LinhaDoTempoComponent } from "./perfil-cliente-header-v2/linha-do-tempo/linha-do-tempo.component";
import { LogAtividadeModalComponent } from "./perfil-cliente-header-v2/log-atividades-modal/log-atividade-modal.component";
import { ModalAvisoConsultorComponent } from "./perfil-cliente-header-v2/modal-aviso-consultor/modal-aviso-consultor.component";
import { ModalBloqueioAcessoCatracaComponent } from "./perfil-cliente-header-v2/modal-bloqueio-acesso-catraca/modal-bloqueio-acesso-catraca.component";
import { ModalDefinirSenhaAcessoComponent } from "./perfil-cliente-header-v2/modal-definir-senha-acesso/modal-definir-senha-acesso.component";
import { ModalGympassComponent } from "./perfil-cliente-header-v2/modal-gympass/modal-gympass.component";
import { ModalImagemComponent } from "./perfil-cliente-header-v2/modal-imagem/modal-imagem.component";
import { ModalObjetivoAlunoComponent } from "./perfil-cliente-header-v2/modal-objetivo-aluno/modal-objetivo-aluno.component";
import { ModalAcessosDiaComponent } from "./perfil-cliente-header-v2/modal-registrar-acesso-manual/modal-acessos-dia/modal-acessos-dia.component";
import { ModalRegistrarAcessoManualComponent } from "./perfil-cliente-header-v2/modal-registrar-acesso-manual/modal-registrar-acesso-manual.component";
import { ModalShareLinkClienteComponent } from "./perfil-cliente-header-v2/modal-share-link-cliente/modal-share-link-cliente.component";
//import { ObservacoesComponent } from "./perfil-cliente-header-v2/observacoes/observacoes.component";
import { PerfilClienteHeaderV2Component } from "./perfil-cliente-header-v2/perfil-cliente-header-v2.component";
import { ModalArmarioComponent } from "./perfil-cliente-header/modal-armario/modal-armario.component";
import { ModalAvisoAlunoComponent } from "./perfil-cliente-header/modal-aviso-aluno/modal-aviso-aluno.component";
import { ModalVisualizacaoHistoricoContratoComponent } from "./perfil-cliente-header/modal-visualizacao-historico-contrato/modal-visualizacao-historico-contrato.component";
import { PainelSaldoPontosComponent } from "./perfil-cliente-header/painel-saldo-pontos/painel-saldo-pontos.component";
import { PerfilClienteHeaderComponent } from "./perfil-cliente-header/perfil-cliente-header.component";
import { PerfilClienteOutletComponent } from "./perfil-cliente-outlet.component";
import { PerfilClienteRoutingModule } from "./perfil-cliente-routing.module";
import { ModalDetalheLogtotalpassComponent } from "./totalpass/modal-detalhe-logtotalpass/modal-detalhe-logtotalpass.component";
import { ProdutosComponent } from "./produtos/produtos.component";
import { ModalCadastroStatusComponent } from "./perfil-cliente-header-v2/modal-cadastro-status/modal-cadastro-status.component";
import { ModalDetalharDocumentoComponent } from "./documentos/documentos-container/modal-detalhar-documento/modal-detalhar-documento.component";
import { ModalResponsavelComponent } from "./configuracoes-cliente/modal-responsavel/modal-responsavel.component";
import { DiffBetweenDatesPipe } from "./perfil-cliente-header-v2/date-in-quantity.pipe";
import { ModalCobrancaComponent } from "./perfil-cliente-header-v2/modal-cobranca/modal-cobranca.component";
import { ModalAtestadoAptidaoFisicaComponent } from "./perfil-cliente-header-v2/modal-atestado-aptidao-fisica/modal-atestado-aptidao-fisica.component";
import { AsFormGroupPipe } from "./configuracoes-cliente/pipes/as-form-group.pipe";
import { ControlsToArrayPipe } from "./configuracoes-cliente/pipes/controls-to-array.pipe";
import { ModalDetalharPerimetroComponent } from "./avaliacao/modal-detalhar-perimetro/modal-detalhar-perimetro.component";
import { ModalDetalharDobraCutaneaComponent } from "./avaliacao/modal-detalhar-dobra-cutanea/modal-detalhar-dobra-cutanea.component";
import { ModalLogAvaliacaoComponent } from "./avaliacao/modal-log-avaliacao/modal-log-avaliacao.component";
import { ModalMensagemProdutoVencidoComponent } from "./perfil-cliente-header-v2/modal-mensagem-produto-vencido/modal-mensagem-produto-vencido.component";
import { FinanceiroComprasEstornarProdutoComponent } from "./financeiro/financeiro-compras/financeiro-compras-estornar-produto/financeiro-compras-estornar-produto.component";
import { ModalNivelClienteComponent } from "./perfil-cliente-header-v2/modal-nivel-cliente/modal-nivel-cliente.component";
import { ModalObjecaoDefinitivaComponent } from "./perfil-cliente-header/modal-objecao-definitiva/modal-objecao-definitiva.component";
import { ModalContatoClienteComponent } from "./perfil-cliente-header/modal-contato-cliente/modal-contato-cliente.component";
import { NotaFiscalContainerComponent } from "./nota-fiscal/nota-fiscal-container/nota-fiscal-container.component";
import { DependentesComponent } from "./configuracoes-cliente/dependentes/dependentes.component";
import { ModalScriptComponent } from "./crm/modals/modal-script/modal-script.component";
import { ModalIndicacaoComponent } from "./crm/modals/modal-indicacao/modal-indicacao.component";
import { ModalObjecaoComponent } from "./crm/modals/modal-objecao/modal-objecao.component";
import { ModalAgendarVisitaComponent } from "./crm/modals/modal-agendar-visita/modal-agendar-visita.component";
import { ModalAgendarLigacaoComponent } from "./crm/modals/modal-agendar-ligacao/modal-agendar-ligacao.component";
import { ModalAgendarAulaExperimentalComponent } from "./crm/modals/modal-agendar-aula-experimental/modal-agendar-aula-experimental.component";
import { PactoComponent } from "./configuracoes-cliente/pacto/pacto.component";
import { ModalCrmAgendarComponent } from "./crm/modals/modal-crm-agendar/modal-crm-agendar.component";
import { ModalHistoricoReposicaoAulasColetivasComponent } from "./perfil-cliente-header-v2/modal-historico-reposicao-aulas-coletivas/modal-historico-reposicao-aulas-coletivas.component";
import { TreinoContainerComponent } from "./treino/treino-container/treino-container.component";
import { CorpoFrontalComponent } from "./treino/corpo-frontal/corpo-frontal.component";
import { CorpoPosteriorComponent } from "./treino/corpo-posterior/corpo-posterior.component";
import { ImprimirFichaComponent } from "./treino/imprimir-ficha/imprimir-ficha.component";
import { TotalpassComponent } from "./totalpass/totalpass.component";
import { CorpoFrontalSimplificadoComponent } from "./treino/corpo-frontal-simplificado/corpo-frontal-simplificado.component";
import { CorpoPosteriorSimplificadoComponent } from "./treino/corpo-posterior-simplificado/corpo-posterior-simplificado.component";
import { HistoricoParqTableComponent } from "./documentos/documentos-container/historico-parq-table/historico-parq-table.component";
import { ModalClienteRestricaoComponent } from "./perfil-cliente-header-v2/modal-cliente-restricao/modal-cliente-restricao.component";
import { ModalClienteRestricaoMsgBloqueioComponent } from "./perfil-cliente-header-v2/modal-cliente-restricao-msg-bloqueio/modal-cliente-restricao-msg-bloqueio.component";
import { IncluirParcelasLinkModalComponent } from "./perfil-cliente-header-v2/modal-incluir-parcelas-link/incluir-parcelas-link-modal.component";
import { RiscoChurnAlunoComponent } from "./perfil-cliente-header-v2/risco-churn-aluno/risco-churn-aluno.component";
import { MdlClienteResumoContratoComponent } from "./modals/mdl-cliente-resumo-contrato/mdl-cliente-resumo-contrato.component";
import { PerfilClienteSharedModule } from "./perfil-cliente-shared/perfil-cliente-shared.module";
import { ModalConvidadosExistentesComponent } from "./convidados/modal-convidados-existentes/modal-convidados-existentes.component";
import { IntegracoesGogoodComponent } from "./integracoes/integracoes-gogood/integracoes-gogood.component";
import { ModalHistoricoCarteiraComponent } from "./perfil-cliente-header/modal-historico-carteira/modal-historico-carteira.component";
import { ModalDesativarTelaNovaAlunoComponent } from "./perfil-cliente-header-v2/modal-desativar-tela-nova-aluno/modal-desativar-tela-nova-aluno.component";
import { RawDatePipe } from "./configuracoes-cliente/pipes/raw-date-pipe";
import { IncluirParcelasPixAutomaticoModalComponent } from "./perfil-cliente-header-v2/modal-incluir-parcelas-pix-automatico/incluir-parcelas-pix-automatico-modal.component";
import { QrcodePixAutomaticoModalComponent } from "./perfil-cliente-header-v2/modal-qrcode-pix-automatico/qrcode-pix-automatico-modal.component";
import { RevisaoPixAutomaticoModalComponent } from "./perfil-cliente-header-v2/modal-revisao-pix-automatico/revisao-pix-automatico-modal.component";

@NgModule({
	imports: [
		CommonModule,
		PerfilClienteRoutingModule,
		AvaliacaoFisicaCoreModule,
		BaseSharedModule,
		CobrancaModule,
		MontagemTreinoModule,
		GraduacaoMsModule,
		GraduacaoModule,
		AcoesDeDetalhamentoModule,
		RelatorioCobrancaModule,
		StatusTransacaoModule,
		StatusPixModule,
		StatusParcelaModule,
		CatTolltipModule,
		PerfilClienteContratosModule,
		AmChartsModule,
		CatAutocompleteModule,
		DeprecatedI18NPipesModule,
		PerfilClienteSharedModule,
	],
	entryComponents: [
		FormaDeCobrancaComponent,
		DualMultiSelectComponent,
		ModalProdutosEspecificosComponent,
		ModalNotificacaoComponent,
		ModalCobrancaAutomaticaComponent,
		ModalAvisoAlunoComponent,
		ModalUploadArquivosComponent,
		ModalComprovanteVacinaComponent,
		ModalReferenciaImcComponent,
		ModalReferenciaPercentualGorduraComponent,
		ModalDetalharBvComponent,
		ModalEditFormaPagamentoComponent,
		ModalActionNfceComponent,
		ModalActionNfseComponent,
		ModalActionSendComponent,
		ModalActionEditVencimentosComponent,
		ModalActionDetailParcelaComponent,
		TelaClienteConsultaReciboComponent,
		ModalActionRenovarProdutoComponent,
		ModalGympassComponent,
		ModalShareLinkClienteComponent,
		IncluirParcelasLinkModalComponent,
		ModalDefinirSenhaAcessoComponent,
		ModalBloqueioAcessoCatracaComponent,
		ModalRegistrarAcessoManualComponent,
		ModalAcessosDiaComponent,
		HistoricoVinculoComponent,
		UploadDocsComponent,
		ModalAdicionarPeriodoComponent,
		ModalAlteracaoSenhaComponent,
		ModalHistoricoAcessoComponent,
		TirarFotoComponent,
		AvisosComponent,
		AlterarMatriculaComponent,
		AcessosComponent,
		VerAcessosComponent,
		DetalhesDoAcessoComponent,
		LogAtividadeModalComponent,
		ModalAvisoConsultorComponent,
		ModalObjetivoAlunoComponent,
		ModalConvidadosComponent,
		ModalConvidadosExistentesComponent,
		ModalActionEstornarComponent,
		ImprimirContratoPrestacaoServicoComponent,
		ModalArmarioComponent,
		ModalHistoricoCarteiraComponent,
		ModalVisualizacaoHistoricoContratoComponent,
		HistoricoConvidadosComponent,
		ModalImagemComponent,
		ConsultarCepComponent,
		ModalDetalheParcelasComponent,
		ModalCadastroStatusComponent,
		ModalDetalharDocumentoComponent,
		ModalResponsavelComponent,
		ModalDetalheLogtotalpassComponent,
		ModalCobrancaComponent,
		ModalAtestadoAptidaoFisicaComponent,
		ModalDetalharPerimetroComponent,
		ModalDetalharDobraCutaneaComponent,
		ModalLogAvaliacaoComponent,
		ModalMensagemProdutoVencidoComponent,
		FinanceiroComprasEstornarProdutoComponent,
		ModalNivelClienteComponent,
		ModalObjecaoDefinitivaComponent,
		LinhaDoTempoComponent,
		ModalContatoClienteComponent,
		ModalScriptComponent,
		ModalIndicacaoComponent,
		ModalObjecaoComponent,
		ModalAgendarVisitaComponent,
		ModalAgendarLigacaoComponent,
		ModalAgendarAulaExperimentalComponent,
		ModalCrmAgendarComponent,
		ModalHistoricoReposicaoAulasColetivasComponent,
		CorpoFrontalComponent,
		CorpoFrontalSimplificadoComponent,
		CorpoPosteriorSimplificadoComponent,
		ModalClienteRestricaoComponent,
		ModalClienteRestricaoMsgBloqueioComponent,
		RiscoChurnAlunoComponent,
		MdlClienteResumoContratoComponent,
		ModalDesativarTelaNovaAlunoComponent,
		IncluirParcelasPixAutomaticoModalComponent,
		QrcodePixAutomaticoModalComponent,
		RevisaoPixAutomaticoModalComponent,
	],
	declarations: [
		PerfilClienteOutletComponent,
		PerfilClienteContainerComponent,
		PerfilClienteHeaderComponent,
		TreinoContainerComponent,
		AvaliacaoContainerComponent,
		PactoPayContainerComponent,
		GraduacaoComponent,
		FinanceiroContainerComponent,
		CrmContainerComponent,
		DocumentosContainerComponent,
		CardPerfilAvaliacaoComponent,
		UltimaAvaliacaoComponent,
		FormaDeCobrancaComponent,
		DualMultiSelectComponent,
		ModalProdutosEspecificosComponent,
		AutorizacaoDeCobrancaComponent,
		ModalNotificacaoComponent,
		ModalCobrancaAutomaticaComponent,
		ModalAvisoAlunoComponent,
		PainelSaldoPontosComponent,
		CrmWhatsappComponent,
		CrmTelefoneComponent,
		CrmEmailComponent,
		CrmAppComponent,
		CrmSmsComponent,
		CrmAgendaComponent,
		CrmObjecaoComponent,
		CrmPessoalComponent,
		CrmActionComponent,
		CrmListaComponent,
		ModalUploadArquivosComponent,
		HistoricoAnexosTableComponent,
		AtestadoMedicoTableComponent,
		DocumentosTableComponent,
		ModalComprovanteVacinaComponent,
		LogAtividadeComponent,
		GympassComponent,
		TotalpassComponent,
		ModalReferenciaImcComponent,
		ModalReferenciaPercentualGorduraComponent,
		BoletimVisitaComponent,
		ModalDetalharBvComponent,
		ConfiguracoesClienteComponent,
		VinculosComponent,
		HistoricoIndicacoesComponent,
		FinanceiroComprasComponent,
		FinanceiroProdutosVencimentosComponent,
		ModalActionNfceComponent,
		ModalActionNfseComponent,
		ModalActionSendComponent,
		ModalEditFormaPagamentoComponent,
		ModalActionEditVencimentosComponent,
		ModalActionDetailParcelaComponent,
		FinanceiroRecibosComponent,
		ParcelasCobradasNoProdutoComponent,
		TelaClienteConsultaReciboComponent,
		ModalActionRenovarProdutoComponent,
		PerfilClienteHeaderV2Component,
		ModalGympassComponent,
		ModalShareLinkClienteComponent,
		IncluirParcelasLinkModalComponent,
		ModalDefinirSenhaAcessoComponent,
		ModalBloqueioAcessoCatracaComponent,
		ModalRegistrarAcessoManualComponent,
		ModalAcessosDiaComponent,
		GruposClassificacaoComponent,
		DadosFinanceirosComponent,
		FamiliaresComponent,
		DadosBasicosComponent,
		DadosAcessosComponent,
		PactoComponent,
		AcessoCatracaComponent,
		HistoricoVinculoComponent,
		ConfiguracoesRhComponent,
		UploadDocsComponent,
		ModalAdicionarPeriodoComponent,
		ModalAlteracaoSenhaComponent,
		ModalHistoricoAcessoComponent,
		ReplicarEmpresaComponent,
		TirarFotoComponent,
		AvisosComponent,
		TabelaDocumentosComponent,
		TabelaAfastamentoFeriasComponent,
		AlterarMatriculaComponent,
		AcessosComponent,
		VerAcessosComponent,
		DetalhesDoAcessoComponent,
		LogAtividadeModalComponent,
		ModalAvisoConsultorComponent,
		ModalObjetivoAlunoComponent,
		ModalConvidadosComponent,
		ModalActionEstornarComponent,
		ImprimirFichaComponent,
		ModalArmarioComponent,
		ModalHistoricoCarteiraComponent,
		ModalVisualizacaoHistoricoContratoComponent,
		ImprimirContratoPrestacaoServicoComponent,
		CadastrarConvidadoComponent,
		ConsultarCepComponent,
		HistoricoConvidadosComponent,
		ModalImagemComponent,
		CorpoFrontalComponent,
		CorpoFrontalSimplificadoComponent,
		CorpoPosteriorSimplificadoComponent,
		CorpoPosteriorComponent,
		ModalDetalheParcelasComponent,
		ProdutosComponent,
		ModalCadastroStatusComponent,
		ModalDetalharDocumentoComponent,
		ModalResponsavelComponent,
		IntegracoesGympassComponent,
		IntegracoesGogoodComponent,
		IntegracoesTotalpassComponent,
		DiffBetweenDatesPipe,
		ModalDetalheLogtotalpassComponent,
		ModalCobrancaComponent,
		ModalAtestadoAptidaoFisicaComponent,
		AsFormGroupPipe,
		ControlsToArrayPipe,
		ModalDetalharPerimetroComponent,
		ModalDetalharDobraCutaneaComponent,
		ModalLogAvaliacaoComponent,
		ModalMensagemProdutoVencidoComponent,
		FinanceiroComprasEstornarProdutoComponent,
		ModalNivelClienteComponent,
		ModalObjecaoDefinitivaComponent,
		LinhaDoTempoComponent,
		ModalContatoClienteComponent,
		NotaFiscalContainerComponent,
		ModalHistoricoReposicaoAulasColetivasComponent,
		DependentesComponent,
		ModalScriptComponent,
		ModalIndicacaoComponent,
		ModalObjecaoComponent,
		ModalAgendarVisitaComponent,
		ModalAgendarLigacaoComponent,
		ModalAgendarAulaExperimentalComponent,
		ModalCrmAgendarComponent,
		HistoricoParqTableComponent,
		ModalClienteRestricaoComponent,
		ModalClienteRestricaoMsgBloqueioComponent,
		RiscoChurnAlunoComponent,
		MdlClienteResumoContratoComponent,
		ModalConvidadosExistentesComponent,
		ModalDesativarTelaNovaAlunoComponent,
		RawDatePipe,
		IncluirParcelasPixAutomaticoModalComponent,
		QrcodePixAutomaticoModalComponent,
		RevisaoPixAutomaticoModalComponent,
	],
	exports: [
		TreinoContainerComponent,
		CorpoFrontalComponent,
		CorpoFrontalSimplificadoComponent,
		CorpoPosteriorSimplificadoComponent,
		CorpoPosteriorComponent,
	],
	providers: [
		// TODO (Lucas Feijão) Verificar uma forma melhor de usar os interceptors sem a necessidade de duplicar? Avaliar
		{
			provide: HTTP_INTERCEPTORS,
			useClass: AddIdEmpresaHeadersInterceptor,
			multi: true,
		},
		{
			provide: HTTP_INTERCEPTORS,
			useClass: LoaderInterceptor,
			multi: true,
		},
		{ provide: HTTP_INTERCEPTORS, useClass: ShareInterceptor, multi: true },
		{
			provide: HTTP_INTERCEPTORS,
			useClass: ExpiredTokenInterceptor,
			multi: true,
		},
		{ provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },
		{
			provide: HTTP_INTERCEPTORS,
			useClass: EntityConlictInteceptor,
			multi: true,
		},
		{
			provide: HTTP_INTERCEPTORS,
			useClass: MessageNotTratedInteceptor,
			multi: true,
		},
		DecimalPipe,
	],
	schemas: [NO_ERRORS_SCHEMA],
})
export class PerfilClienteModule {}
