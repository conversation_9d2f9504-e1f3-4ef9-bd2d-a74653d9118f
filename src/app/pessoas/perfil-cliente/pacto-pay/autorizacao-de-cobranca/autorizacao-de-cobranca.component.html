<div [hidden]="dataCobranca">
	<div class="title-text-empty">Autorizações de cobrança</div>
	<div class="d-flex flex-column align-items-center mb-3">
		<img class="icon-empty" src="assets/images/empty-state-m-card.svg" />
		<div class="text-empty mt-3 body-text-empty mb-3">
			O aluno ainda não possui nenhuma autorização de cobrança cadastrada!
		</div>
		<button
			id="pay-btn-new-forma-cobranca"
			class="btn btn-primary"
			style="color: #fff"
			(click)="verificarConveniosAntesDeAbrir()">
			Adicionar autorização de cobrança
		</button>
	</div>
</div>

<div [hidden]="!dataCobranca">
	<!-- Título principal antes das duas listas -->
	<div class="d-flex justify-content-between align-items-center mb-3">
		<div class="table-title">Autorizações de cobrança</div>
		<div>
			<ng-container *ngTemplateOutlet="cobrancaAutomatica"></ng-container>
		</div>
	</div>

	<!-- Se tem PIX Automático: mostra as duas seções com títulos -->
	<div *ngIf="hasPixAutomatico">
		<!-- Seção PIX Automático -->
		<div class="mb-2">
			<h6 class="text-secondary mb-1" style="margin-left: 11px">
				PIX Automático
			</h6>
			<pacto-relatorio-cobranca
				#relatorioPixAutomatico
				[showShare]="false"
				[table]="relatorioPixAutomaticoGridConfig"
				[tableTitle]="''"
				[customActions]="null"></pacto-relatorio-cobranca>
		</div>

		<!-- Seção Outras (Cartão e Boleto) - só mostra se houver dados -->
		<div class="mb-4" *ngIf="hasOutrasAutorizacoes">
			<h6 class="text-secondary mb-1" style="margin-left: 11px">Outras</h6>
			<pacto-relatorio-cobranca
				#relatorio
				[showShare]="false"
				[table]="relatorioGridConfig"
				[tableTitle]="''"
				[customActions]="null"
				[accordion]="accordionEditarAutorizacao"></pacto-relatorio-cobranca>
		</div>
	</div>

	<!-- Se NÃO tem PIX Automático: mostra apenas a tabela principal sem subtítulo -->
	<div *ngIf="!hasPixAutomatico" class="mb-4">
		<!-- Componente PIX Automático oculto para detectar dados -->
		<div style="display: none">
			<pacto-relatorio-cobranca
				#relatorioPixAutomatico
				[showShare]="false"
				[table]="relatorioPixAutomaticoGridConfig"
				[tableTitle]="''"
				[customActions]="null"></pacto-relatorio-cobranca>
		</div>

		<pacto-relatorio-cobranca
			#relatorio
			[showShare]="false"
			[table]="relatorioGridConfig"
			[tableTitle]="''"
			[customActions]="null"
			[accordion]="accordionEditarAutorizacao"></pacto-relatorio-cobranca>
	</div>

	<!-- Botão no final, depois das duas listas -->
	<div class="d-flex justify-content-end mt-3">
		<ng-container *ngTemplateOutlet="novaFormaDeCobranca"></ng-container>
	</div>

	<ng-template #tipoAutorizacao let-cobranca="item">
		<span *ngIf="cobranca.tipo === 1 && !cobranca.utilizandoIdVindiPessoa">
			Cartão de crédito
		</span>
		<div
			*ngIf="cobranca.tipo === 1 && cobranca.utilizandoIdVindiPessoa"
			[ds3Tooltip]="tooltipVindi">
			<span>Autorização utilizando IdVindi</span>
			<br />
			<span>IdVindi: {{ dadosPessoais?.idVindi }}</span>
		</div>
		<span *ngIf="cobranca.tipo === 2">Débito em conta</span>
		<span *ngIf="cobranca.tipo === 3">Boleto bancário</span>
		<span
			*ngIf="cobranca.tipo === 5"
			[ngbTooltip]="
				'idRec: ' + cobranca.pixAutomatico?.idRec || 'ID não disponível'
			"
			placement="right"
			container="body"
			tooltipClass="tooltip-pix-automatico">
			Pix automático
		</span>
	</ng-template>

	<ng-template #tipoDeParcela let-cobranca="item">
		<span *ngIf="cobranca.tipoCobrar === 1">Em Aberto</span>
		<span *ngIf="cobranca.tipoCobrar === 2">Planos</span>
		<span
			*ngIf="cobranca.tipoCobrar === 3"
			[ngbTooltip]="
				cobranca.listaObjetosACobrarApresentar || 'Produtos não especificados'
			"
			placement="top">
			Produtos Especificados
		</span>
		<span *ngIf="cobranca.tipoCobrar === 4">Contratos Autorrenováveis</span>
	</ng-template>

	<ng-template #cobrancaAutomatica>
		<div class="cobranca-automatica">
			<!-- <pacto-cat-button (click)="openModalCobrancaAutomatica()" size="LARGE"
            [label]="autorizada?'Bloquear cobrança automática':'Desbloquear cobrança automática'"></pacto-cat-button> -->
			<!-- <div class="t-status t-status-paid" *ngIf="autorizada">
            <span>AUTORIZADA</span>
        </div> -->
			<!-- tooltipClass="my-custom-class" -->
			<!--    <div [ngbTooltip]="tipContent" class="t-status t-status-error" *ngIf="!autorizada">-->
			<!-- <div class="t-status t-status-error" *ngIf="!autorizada">
            <span>BLOQUEADA</span>
        </div> -->
		</div>

		<ng-template #tipContent>
			<!-- <b>Todas</b> as parcelas com vencimento superior ao dia
    <b>{{ aluno.dataBloqueioCobrancaAutomatica.split(' ')[0] }}</b> estão bloqueadas para cobrança automática.<br />
    <b>CLIQUE PARA DESBLOQUEAR</b> -->
		</ng-template>
	</ng-template>

	<ng-template #novaFormaDeCobranca>
		<button
			id="pay-btn-new-forma-cobranca"
			class="btn btn-primary"
			style="color: #fff; margin-bottom: 0.5rem"
			(click)="verificarConveniosAntesDeAbrir()">
			Nova autorização de cobrança
		</button>
	</ng-template>

	<ng-template #accordionEditarAutorizacao let-cobranca="item">
		<pacto-forma-de-cobranca
			[cobranca]="cobranca"
			(sendModificacao)="reiniciarRelatorio($event)"
			[idVindi]="dadosPessoais?.idVindi"
			[possuiIdVindi]="dadosPessoais?.possuiIdVindi"
			[pessoa]="aluno?.pessoa"
			[empresa]="dadosPessoais?.empresa?.codigo"
			[editar]="true"></pacto-forma-de-cobranca>
	</ng-template>

	<!-- Template removido - usando botão único -->

	<ng-template #accordionEditarAutorizacaoPixAutomatico let-cobranca="item">
		<pacto-forma-de-cobranca
			[cobranca]="cobranca"
			(sendModificacao)="reiniciarRelatorioPixAutomatico($event)"
			[idVindi]="dadosPessoais?.idVindi"
			[possuiIdVindi]="dadosPessoais?.possuiIdVindi"
			[pessoa]="aluno?.pessoa"
			[empresa]="dadosPessoais?.empresa?.codigo"
			[editar]="true"></pacto-forma-de-cobranca>
	</ng-template>

	<!-- Templates para colunas específicas do PIX Automático -->
	<ng-template #dataCriacaoPixAutomatico let-cobranca="item">
		<span>{{ formatarDataCriacao(cobranca) }}</span>
	</ng-template>

	<ng-template #dataInicioPixAutomatico let-cobranca="item">
		<span>{{ formatarDataInicio(cobranca) }}</span>
	</ng-template>

	<ng-template #dataFimPixAutomatico let-cobranca="item">
		<span ngbTooltip="{{ getTooltipDataFim(cobranca) }}">
			{{ formatarDataFim(cobranca) }}
		</span>
	</ng-template>

	<ng-template #statusPixAutomatico let-cobranca="item">
		<div
			class="status-pix-automatico"
			[ngClass]="getStatusClass(cobranca.pixAutomatico?.statusBelvo)"
			ngbTooltip="{{ getStatusText(cobranca) }}"
			placement="left">
			{{ getStatusNome(cobranca.pixAutomatico?.statusBelvo) }}
		</div>
	</ng-template>

	<ng-template #bancoPixAutomatico let-cobranca="item">
		<div class="d-flex justify-content-center align-items-center">
			<img
				*ngIf="cobranca.pixAutomatico && cobranca.pixAutomatico.icon_logo"
				[src]="cobranca.pixAutomatico.icon_logo"
				[alt]="
					(cobranca.pixAutomatico &&
						cobranca.pixAutomatico.legal_entity_name) ||
					'Logo do banco'
				"
				[ngbTooltip]="
					(cobranca.pixAutomatico &&
						cobranca.pixAutomatico.legal_entity_name) ||
					'Banco não identificado'
				"
				placement="top"
				class="bank-logo"
				style="
					width: 32px;
					height: 32px;
					object-fit: contain;
					border-radius: 4px;
				"
				(error)="onImageError($event)" />
			<span
				*ngIf="!cobranca.pixAutomatico || !cobranca.pixAutomatico.icon_logo"
				class="text-muted"
				style="font-size: 12px">
				N/A
			</span>
		</div>
	</ng-template>

	<ng-template #tipoPixAutomatico let-cobranca="item">
		<div class="d-flex justify-content-center align-items-center">
			<span
				class="tipo-autorizacao"
				[ngbTooltip]="getTipoAutorizacaoTooltip(cobranca)"
				placement="left">
				{{ getTipoAutorizacao(cobranca) }}
			</span>
		</div>
	</ng-template>

	<ng-template #acoesPixAutomatico let-cobranca="item">
		<div class="d-flex justify-content-center gap-2">
			<button
				class="btn btn-link d-flex align-items-center justify-content-center p-0"
				[disabled]="!cobranca.pixAutomatico?.podeGerarQRCode"
				(click)="
					cobranca.pixAutomatico?.podeGerarQRCode &&
						abrirQRCodePixAutomatico(cobranca)
				"
				[ngbTooltip]="
					cobranca.pixAutomatico?.podeGerarQRCode
						? 'Visualizar QR Code'
						: 'A situação atual da autorização não permite gerar o QRCode'
				"
				placement="left"
				style="
					width: 32px;
					height: 32px;
					border: none;
					background: none;
					text-decoration: none;
					cursor: pointer;
				"
				[style.cursor]="
					cobranca.pixAutomatico?.podeGerarQRCode ? 'pointer' : 'not-allowed'
				">
				<i
					class="fa fa-qrcode"
					[style.color]="
						cobranca.pixAutomatico?.podeGerarQRCode ? '#6c757d' : '#adb5bd'
					"
					style="font-size: 18px"></i>
			</button>
			<button
				class="btn btn-link d-flex align-items-center justify-content-center p-0"
				[disabled]="!cobranca.pixAutomatico?.permiteCancelar"
				(click)="confirmarExclusaoPixAutomatico(cobranca)"
				[ngbTooltip]="
					cobranca.pixAutomatico?.permiteCancelar
						? 'Cancelar Autorização'
						: 'A situação atual da autorização não permite cancelar a autorização'
				"
				placement="left"
				style="
					width: 32px;
					height: 32px;
					border: none;
					background: none;
					text-decoration: none;
					cursor: pointer;
				"
				[style.cursor]="
					cobranca.pixAutomatico?.permiteCancelar ? 'pointer' : 'not-allowed'
				">
				<i
					class="pct pct-x-circle cor-hellboy05"
					[style.color]="
						cobranca.pixAutomatico?.permiteCancelar ? '#dc3545' : '#adb5bd'
					"></i>
			</button>
		</div>
	</ng-template>
</div>

<!-- Template para coluna Código PIX Automático -->
<ng-template #codigoPixAutomatico let-cobranca="item">
	<span
		[ngbTooltip]="
			'Identificador: ' +
			mascarIdContrato(
				(cobranca.pixAutomatico && cobranca.pixAutomatico.contrato) || 'N/A'
			)
		"
		placement="right"
		container="body"
		tooltipClass="tooltip-high-z-index">
		{{ cobranca.codigo || "N/A" }}
	</span>
</ng-template>

<!-- Template para coluna idRec PIX Automático -->
<ng-template #idRecPixAutomatico let-cobranca="item">
	<span
		[ngbTooltip]="
			'Identificador: ' +
			mascarIdContrato(
				(cobranca.pixAutomatico && cobranca.pixAutomatico.contrato) || 'N/A'
			)
		"
		placement="top"
		container="body">
		{{ (cobranca.pixAutomatico && cobranca.pixAutomatico.idRec) || "N/A" }}
	</span>
</ng-template>

<ng-template #tooltipVindi>
	A transação será enviada utilizando somente o IdVindi do cliente.
	<br />
	A Vindi irá realizar a tentativa de cobrança utilizando o cartão que o cliente
	tiver cadastrado na Vindi.
</ng-template>
