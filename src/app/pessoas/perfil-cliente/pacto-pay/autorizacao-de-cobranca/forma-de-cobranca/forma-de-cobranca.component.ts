import { IntegracaoKobanaFormCadastroComponent } from "@adm/configuracao/integracoes-v2/components/pactopay/integracao-kobana-modal/integracao-kobana-form-cadastro/integracao-kobana-form-cadastro.component";
import { DecimalPipe } from "@angular/common";
import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
} from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { SessionService } from "@base-core/client/session.service";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {
	Autorizacao,
	MsPactoPayApiCobrancaService,
	MsPactopayApiConvenioCobrancaService,
} from "ms-pactopay-api";
import { SnotifyService } from "ng-snotify";
import { BUTTON_SIZE, BUTTON_TYPE, PactoModalRef } from "ui-kit";
import {
	AutorizacaoBoleto,
	AutorizacaoCartao,
	AutorizacaoDebito,
	ZwPactoPayApiDashService,
} from "zw-pactopay-api";
import { ZWServletPixAutomaticoApiService } from "zw-servlet-api";
import { PerfilRecursoPermissoTipo } from "../../../../../../../projects/sdk/src/lib/services/models/perfil-acesso-recurso.model";
import { ClienteService } from "../../../../../microservices/personagem/cliente/cliente.service";
import { IncluirParcelasPixAutomaticoModalComponent } from "../../../perfil-cliente-header-v2/modal-incluir-parcelas-pix-automatico/incluir-parcelas-pix-automatico-modal.component";
import { PactoDualMultiSelectConfig } from "../dual-multi-select/pacto-dual-multi-select.model";
import { ModalNotificacaoComponent } from "../modal-notificacao/modal-notificacao.component";
import { ModalProdutosEspecificosComponent } from "../modal-produtos-especificos/modal-produtos-especificos.component";

@Component({
	selector: "pacto-forma-de-cobranca",
	templateUrl: "./forma-de-cobranca.component.html",
	styleUrls: ["./forma-de-cobranca.component.scss"],
})
export class FormaDeCobrancaComponent implements OnInit, AfterViewInit {
	@Input()
	public cobranca: Autorizacao;
	@Output() abrirNovaFormaCobranca = new EventEmitter<void>();
	@Input() idVindi: number;
	@Input() possuiIdVindi: boolean;
	@Input()
	public pessoa: number;
	@Input()
	public empresa: number;
	@Input()
	public chave: string;
	@Input()
	public dadosPessoais: any;
	data: any = {
		totalElements: 0,
		totalPages: 0,
		numberOfElements: 0,
		size: 0,
		content: 0,
		page: 0,
	};
	lista: Array<any> = [];

	@Input()
	public editar = false;

	@Output()
	public sendModificacao: EventEmitter<any> = new EventEmitter();

	public listaProdutosSelecionados = [];

	public parcelas = [];

	public convenios = [];

	public instituicoes: any[] = [];

	public formulario: FormGroup;

	permissaoCliente5_73: any;

	public bandeira: string;

	public tiposDeAutorizacao: any = {
		cartaoCredito: false,
		boleto: false,
		debitoConta: false,
		pixAutomatico: false,
		id: false,
	};
	tipoString: string;
	pixAutomatico: boolean;
	tiposId: Array<{ codigo: string; descricao: string }> = new Array<{
		codigo: string;
		descricao: string;
	}>();

	constructor(
		private readonly zwPactoPayApiDash: ZwPactoPayApiDashService,
		private readonly msPactoPayApiCobranca: MsPactoPayApiCobrancaService,
		private readonly msPactopayApiConvenioCobranca: MsPactopayApiConvenioCobrancaService,
		private readonly session: SessionService,
		private readonly cd: ChangeDetectorRef,
		private readonly modalService: ModalService,
		private readonly openModal: NgbActiveModal,
		private readonly notify: SnotifyService,
		private readonly formBuilder: FormBuilder,
		private clienteService: ClienteService,
		private decimalPipe: DecimalPipe,
		private readonly zwServletPixAutomaticoApi: ZWServletPixAutomaticoApiService
	) {}

	ngOnInit() {
		console.log("=== FORMA DE COBRANÇA INICIANDO ===");
		console.log(
			"Chave:",
			this.chave,
			"Pessoa:",
			this.pessoa,
			"Empresa:",
			this.empresa
		);
		this.formulario = this.formBuilder.group({
			convenio: this.formBuilder.control(""),
			tipo: this.formBuilder.control(""),
			tipoCobrar: this.formBuilder.control(""),
			titular: this.formBuilder.control(""),
			documento: this.formBuilder.control(""),
			card: this.formBuilder.control(""),
			cvv: this.formBuilder.control(""),
			vencimento: this.formBuilder.control(""),
			clienteTitular: this.formBuilder.control(""),
			agencia: this.formBuilder.control(""),
			contacorrente: this.formBuilder.control(""),
			autorizarclientedebito: this.formBuilder.control(""),
			bandeira: this.formBuilder.control(""),
			idVindi: this.formBuilder.control(null),
			tipoId: this.formBuilder.control(null),
			instituicao: this.formBuilder.control(""),
		});
		this.formulario.get("idVindi").disable();
		this._populateTiposId();
		this.loadForm();
		this.obterTiposDeAutorizacao();

		// Listener para detectar mudanças no tipo de autorização
		this.formulario.get("tipo").valueChanges.subscribe((tipo) => {
			if (tipo === 5 && this.parcelas && this.parcelas.length > 0) {
				// Se for pix automático, marca "Em aberto" por padrão (codigo = 1)
				this.formulario.patchValue({
					tipoCobrar: 1,
				});
			}
		});

		// Listener para detectar mudanças na instituição selecionada
		this.formulario.get("instituicao").valueChanges.subscribe((value) => {
			console.log(
				"Instituição selecionada:",
				value,
				"PIX Automático:",
				this.pixAutomatico
			);
			if (value && this.pixAutomatico) {
				this.acaoSelecionouInstituicaoPixAutomatico();
			}
		});
	}

	ngAfterViewInit(): void {
		this.permissaoCliente5_73 =
			this.session.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "5.73"
			);
	}

	private _populateTiposId() {
		this.tiposId.push({
			codigo: "idVindi",
			descricao: "IdVindi",
		});
	}

	private loadForm() {
		if (this.editar) {
			this.formulario.patchValue(this.cobranca);
			if (this.cobranca && this.cobranca.utilizandoIdVindiPessoa) {
				this.tipoString = "idVindi";
				this.formulario.get("convenio").disable();
				this.formulario.get("tipoId").disable();
				this.cd.detectChanges();
			}
			this.escolherTipoDeAutorizacao(this.cobranca.tipo, this.tipoString);
			this.aplicarMascaraPeloTipo(this.cobranca.tipo);
		}

		if (this.possuiIdVindi) {
			this.formulario.get("idVindi").setValue(this.idVindi);
		}

		this.msPactoPayApiCobranca.getAllParcelasCobrar().subscribe((parcelas) => {
			this.parcelas = parcelas;

			// Define valor padrão para tipoCobrar baseado no tipo selecionado
			const tipoSelecionado = this.formulario.get("tipo").value;
			if (tipoSelecionado === 5) {
				// PIX Automático: marca "Em aberto" por padrão (codigo = 1)
				this.formulario.patchValue({
					tipoCobrar: 1,
				});
			} else if (tipoSelecionado === 1 || tipoSelecionado === 3) {
				// Cartão ou Boleto: marca "Em aberto" por padrão (codigo = 1)
				this.formulario.patchValue({
					tipoCobrar: 1,
				});
			}

			this.cd.detectChanges();
		});
	}

	public salvar() {
		this.formulario.markAllAsTouched();
		if (this.formulario.valid) {
			switch (this.formulario.get("tipo").value) {
				case 1: // cartão
					this.salvarCartao(this.formulario.getRawValue(), true);
					break;
				case 2: // débito
					this.salvarDebito(this.formulario.value);
					break;
				case 3: // boleto
					this.salvarBoleto(this.formulario.value);
					break;
				default:
			}
		} else {
			Object.keys(this.formulario.controls).forEach((key) => {
				const control = this.formulario.get(key);
				if (control && control.errors) {
				}
			});
		}
	}

	private salvarCartao(formulario: AutorizacaoCartao, verificar: boolean) {
		if (
			formulario.tipoCobrar === 3 &&
			this.listaProdutosSelecionados.length === 0
		) {
			this.notify.error(
				"Você não selecionou os produtos específicados. Por favor, refaça a autorização ou selecione outro tipo de parcela."
			);
		} else {
			if (this.editar) {
				formulario.codigo = this.cobranca.codigo;
			}
			formulario.pessoa = this.pessoa;
			formulario.verificar = verificar;
			formulario.produtos = this.listaProdutosSelecionados.map(
				(produto) => produto.codigo
			);
			this.sendAutorizacao(formulario);
		}
	}

	private salvarDebito(formulario: AutorizacaoDebito) {
		if (this.editar) {
			formulario.codigo = this.cobranca.codigo;
		}
		formulario.pessoa = this.pessoa;
		this.sendAutorizacao(formulario);
	}

	private salvarBoleto(formulario: AutorizacaoBoleto) {
		if (this.editar) {
			formulario.codigo = this.cobranca.codigo;
		}
		formulario.pessoa = this.pessoa;
		this.sendAutorizacao(formulario);
	}

	private sendAutorizacao(
		formulario: AutorizacaoCartao | AutorizacaoDebito | AutorizacaoBoleto
	) {
		formulario.usuario = this.session.loggedUser.professorResponse.id;
		formulario.username = this.session.loggedUser.username;
		if (this.tipoString === "idVindi") {
			formulario.usarIdVindi = true;
		}
		this.zwPactoPayApiDash.sendAutorizacao(formulario).subscribe((res) => {
			const modal: PactoModalRef = this.modalService.open(
				"Informativo",
				ModalNotificacaoComponent,
				PactoModalSize.MEDIUM
			);
			if (res.error) {
				modal.componentInstance.type = "pct-alert-triangle";
				modal.componentInstance.title = "Atenção!";
				modal.componentInstance.subtitle =
					res.message ||
					"As alterações feitas não foram salvas. Tente novamente.";
				modal.componentInstance.actions = [
					{
						clickHandler() {
							modal.close(true);
						},
						type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
						size: BUTTON_SIZE.LARGE,
						label: "Tentar novamente",
						full: true,
					},
				];
			} else {
				if (
					res.hasOwnProperty("apresentarConfirmar") &&
					res.hasOwnProperty("permiteAdicionar")
				) {
					this.tratarVerificacaoNegada(res, formulario, modal);
				} else {
					modal.componentInstance.type = "pct-check";
					modal.componentInstance.title = "Salvo com sucesso!";
					modal.componentInstance.subtitle = res.mensagem;
					modal.componentInstance.actions = [
						{
							clickHandler: () => {
								this.sendModificacao.emit({
									status: "Salvo com sucesso!",
								});
								// Emite evento global para atualizar listas
								window.dispatchEvent(
									new CustomEvent("autorizacaoCobrancaCriada")
								);
								modal.close(true);
								this.openModal.close(true);
							},
							type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
							size: BUTTON_SIZE.LARGE,
							label: "Ok",
							full: true,
						},
					];
				}
			}
		});
	}

	private tratarVerificacaoNegada(
		{ permiteAdicionar, mensagem },
		formulario: AutorizacaoCartao,
		modal: PactoModalRef
	) {
		modal.componentInstance.type = "pct-x-circle";
		modal.componentInstance.title = "Transação não autorizada!";
		modal.componentInstance.subtitle = mensagem;
		const actions = [];
		if (permiteAdicionar) {
			modal.componentInstance.body = "Deseja salvar o cartão mesmo assim?";
			actions.push(
				{
					clickHandler: () => {
						this.salvarCartao(formulario, false);
						modal.close(true);
					},
					type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
					size: BUTTON_SIZE.LARGE,
					label: "Salvar",
					full: true,
				},
				{
					clickHandler() {
						modal.close(true);
					},
					type: BUTTON_TYPE.OUTLINE_ACTION,
					size: BUTTON_SIZE.LARGE,
					label: "Cancelar",
					full: true,
				}
			);
		} else {
			modal.componentInstance.body =
				"Não será possível prosseguir, revise as informações do cartão e tente novamente.";
			actions.push({
				clickHandler() {
					modal.close(true);
				},
				type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
				size: BUTTON_SIZE.LARGE,
				label: "Ok",
				full: true,
			});
		}
		modal.componentInstance.actions = actions;
	}

	public cancelar() {
		this.ngOnInit();
		this.openModal.close(true);
	}

	public confirmarExclusao() {
		if (!this.permiteExcluirAutorizacao()) {
			this.notify.error(
				'Você não possui a permissão "5.73 - Cadastro de autorização de cobrança cliente (Excluir)"',
				{
					timeout: undefined,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		const modal: PactoModalRef = this.modalService.open(
			"Informativo",
			ModalNotificacaoComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.type = "pct-alert-triangle";
		modal.componentInstance.title = "Atenção!";
		modal.componentInstance.subtitle =
			"Tem certeza que deseja excluir essa autorização?";
		modal.componentInstance.actions = [
			{
				clickHandler: this.excluir(modal),
				type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
				size: BUTTON_SIZE.LARGE,
				label: "Excluir",
				full: true,
			},
			{
				clickHandler() {
					modal.close(true);
				},
				type: BUTTON_TYPE.OUTLINE_ACTION,
				size: BUTTON_SIZE.LARGE,
				label: "Cancelar",
				full: true,
			},
		];
	}

	permiteExcluirAutorizacao(): any {
		const permition = this.permissaoCliente5_73;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	private excluir(modalInstance) {
		return () => {
			this.msPactoPayApiCobranca
				.excluirAutorizacao(this.cobranca.codigo)
				.subscribe(
					(result) => {
						modalInstance.close(true);
						const modal: PactoModalRef = this.modalService.open(
							"Informativo",
							ModalNotificacaoComponent,
							PactoModalSize.MEDIUM
						);
						if (result.error) {
							modal.componentInstance.type = "pct-alert-triangle";
							modal.componentInstance.title = "Atenção!";
							modal.componentInstance.subtitle =
								"Erro ao tentar excluir. Tente novamente.";
							modal.componentInstance.actions = [
								{
									clickHandler() {
										modal.close(true);
									},
									type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
									size: BUTTON_SIZE.LARGE,
									label: "Tentar novamente",
									full: true,
								},
								{
									clickHandler() {
										modal.close(true);
									},
									type: BUTTON_TYPE.OUTLINE_ACTION,
									size: BUTTON_SIZE.LARGE,
									label: "Cancelar",
									full: true,
								},
							];
						} else {
							modal.componentInstance.type = "pct-check";
							modal.componentInstance.title = "Excluído com sucesso!";
							modal.componentInstance.subtitle = result.content;
							modal.componentInstance.actions = [
								{
									clickHandler: () => {
										this.sendModificacao.emit({
											status: "Excluído com sucesso!",
										});
										modal.close(true);
										this.openModal.close(true);
									},
									type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
									size: BUTTON_SIZE.LARGE,
									label: "Ok",
									full: true,
								},
							];
						}
					},
					(httpErrorResponse) => {
						const err = httpErrorResponse.error;
						if (err.meta && err.meta.messageValue) {
							this.notify.error(err.meta.messageValue);
						}
					}
				);
		};
	}

	public limparDados() {
		const modal: PactoModalRef = this.modalService.open(
			"Informativo",
			ModalNotificacaoComponent,
			PactoModalSize.SMALL
		);
		modal.componentInstance.type = "pct-alert-triangle";
		modal.componentInstance.title = "Atenção!";
		modal.componentInstance.subtitle =
			"Tem certeza que deseja limpar os dados da cobrança?";
		modal.componentInstance.actions = [
			{
				clickHandler: () => {
					this.formulario.reset();
					if (this.editar) {
						this.formulario.patchValue({
							tipo: this.cobranca.tipo,
						});
						this.configurarValidacoes(this.cobranca.tipo);
					}
					modal.close(true);
				},
				type: BUTTON_TYPE.SUCCESS,
				size: BUTTON_SIZE.LARGE,
				label: "Sim, limpar dados",
				full: true,
			},
			{
				clickHandler() {
					modal.close(true);
				},
				type: BUTTON_TYPE.OUTLINE_ACTION,
				size: BUTTON_SIZE.LARGE,
				label: "Cancelar",
				full: true,
			},
		];
	}

	public escolherTipoDeAutorizacao(tipo, tipoString?: string) {
		this.pixAutomatico = false;
		this.tipoString = tipoString;

		this.formulario.patchValue({ tipo });
		if (tipo === 5) {
			console.log("=== PIX AUTOMÁTICO SELECIONADO ===");
			this.convenios = [];
			this.pixAutomatico = true;
		}
		this.configurarValidacoes(tipo);
		this.zwPactoPayApiDash
			.getConvenioPorTipo(tipo, this.empresa)
			.subscribe((convenios) => {
				console.log("=== DEBUG Convênios carregados ===");
				console.log("Tipo:", tipo);
				console.log("Empresa:", this.empresa);
				console.log("Convênios recebidos:", convenios);
				console.log("Quantidade:", convenios ? convenios.length : 0);
				this.convenios = convenios;
				if (this.editar) {
					this.formulario.patchValue({
						convenio: this.convenios.find(
							(item) => item.descricao === this.cobranca.convenio
						).codigo,
					});
				}

				// Se houver apenas um convênio, seleciona automaticamente
				if (this.convenios && this.convenios.length === 1) {
					console.log(
						"Selecionando convênio automaticamente:",
						this.convenios[0]
					);
					this.formulario.patchValue({
						convenio: this.convenios[0].codigo,
					});

					// Se for pix automático, chama a ação específica
					if (tipo === 5) {
						this.acaoSelecionouConvenioPixAutomatico();
					}
				} else if (tipo === 5) {
					console.log(
						"Não selecionou convênio automaticamente. Quantidade:",
						this.convenios ? this.convenios.length : 0
					);
				}

				// Se for pix automático, marca "Em aberto" por padrão (codigo = 1)
				if (tipo === 5) {
					this.formulario.patchValue({
						tipoCobrar: 1,
					});
				}

				this.cd.detectChanges();
			});
	}

	public acaoSelecionouConvenioPixAutomatico(): void {
		console.log("=== INÍCIO acaoSelecionouConvenioPixAutomatico ===");
		this.data.size = 100;
		this.data.page = 1;
		this.lista = [];
		this.data.content = [];

		const convenioSelecionado = this.formulario.get("convenio").value;
		console.log("Convênio selecionado:", convenioSelecionado);

		if (!convenioSelecionado) {
			console.log("Nenhum convênio selecionado, limpando instituições");
			this.instituicoes = [];
			this.cd.detectChanges();
			return;
		}

		const payload = {
			convenio: convenioSelecionado,
		};
		console.log("Payload para buscar instituições:", payload);

		this.zwServletPixAutomaticoApi
			.obterInstituicoesDisponiveisPixAutomatico(payload)
			.subscribe(
				(response) => {
					console.log("Resposta do serviço de instituições:", response);
					const instituicoesData = response;

					if (!instituicoesData) {
						console.error("Dados de instituições não encontrados na resposta");
						this.instituicoes = [];
						this.cd.detectChanges();
						return;
					}

					console.log("Mapeando", instituicoesData.length, "instituições");
					this.instituicoes = instituicoesData.map((inst) => ({
						institution_type: inst.institution_type,
						name: inst.name,
						description: inst.description,
						active: inst.active,
						logo: inst.logo,
						return_url: inst.return_url,
						id: inst.id,
						display_name: inst.display_name,
						legal_entity_name: inst.legal_entity_name,
						icon_logo: inst.icon_logo,
					}));

					console.log("Instituições mapeadas:", this.instituicoes);
					this.cd.detectChanges();
				},
				(error) => {
					this.notify.error("Erro ao buscar instituições");
					console.error("=== ERRO ao carregar instituições ===");
					console.error("Erro completo:", error);
					console.error(
						"Status:",
						error && error.status ? error.status : "N/A"
					);
					console.error(
						"Message:",
						error && error.message ? error.message : "N/A"
					);
					this.instituicoes = [];
					this.cd.detectChanges();
				}
			);
	}

	public acaoSelecionouInstituicaoPixAutomatico(): void {
		console.log("=== INÍCIO acaoSelecionouInstituicaoPixAutomatico ===");
		console.log("Chave:", this.chave, "Pessoa:", this.pessoa);
		this.data.size = 100;
		this.data.page = 1;
		this.lista = [];
		this.data.content = [];
		this.clienteService
			.obterParcelasAbertoCliente(this.chave, this.pessoa)
			.subscribe(
				(data) => {
					// Permite abrir o modal mesmo sem parcelas
					const parcelas = data || [];

					parcelas.forEach((value) => {
						value.valor = this.transformValueMoney(value.valor);
						this.lista.push(value);
					});
					this.data.content = parcelas;
					this.data.totalElements = parcelas.length;

					// Fecha o modal atual antes de abrir o modal de parcelas
					this.openModal.close({ abrindoParcelas: true });

					const modalParcelasPixAut: PactoModalRef = this.modalService.open(
						"Escolha as parcelas",
						IncluirParcelasPixAutomaticoModalComponent,
						PactoModalSize.LARGE
					);

					// Configuração do modal...
					modalParcelasPixAut.componentInstance.lista = this.lista;
					modalParcelasPixAut.componentInstance.data = this.data;
					modalParcelasPixAut.componentInstance.pessoa = this.pessoa;
					modalParcelasPixAut.componentInstance.usuario =
						this.session.loggedUser.professorResponse.id;
					modalParcelasPixAut.componentInstance.username =
						this.session.loggedUser.username;
					modalParcelasPixAut.componentInstance.convenio =
						this.formulario.get("convenio").value;
					modalParcelasPixAut.componentInstance.codEmpresa =
						this.session.empresaId;
					modalParcelasPixAut.componentInstance.origem = 23; // ZW_MANUAL_AUTORIZACAO_COBRANCA
					modalParcelasPixAut.componentInstance.instituicao =
						this.formulario.get("instituicao").value;
					modalParcelasPixAut.componentInstance.tipoCobrar =
						this.formulario.get("tipoCobrar").value;
					modalParcelasPixAut.componentInstance.dadosPessoais =
						this.dadosPessoais;
					// Passa a lista de produtos específicos para PIX Automático
					modalParcelasPixAut.componentInstance.listaProdutosSelecionados =
						this.listaProdutosSelecionados;

					// Trata o resultado do modal de parcelas
					modalParcelasPixAut.result
						.then((result) => {
							if (result && result.voltarParaFormaCobranca) {
								// Reabre o modal de forma de cobrança
								this.reabrirModalFormaCobranca();
							} else if (result && result.canceled) {
								// Emite o evento para abrir nova forma de cobrança
								this.abrirNovaFormaCobranca.emit();
							}
						})
						.catch(() => {
							// Modal foi fechado sem resultado
						});
					this.cd.detectChanges();
				},
				(error) => {
					this.notify.error("Erro ao consultar parcelas em aberto do cliente");
					console.error("Erro ao obter parcelas em aberto:", error);
					this.cd.detectChanges();
				}
			);
	}

	private reabrirModalFormaCobranca(): void {
		// Emite o evento para abrir um modal totalmente novo de forma de cobrança
		this.abrirNovaFormaCobranca.emit();
	}

	transformValueMoney(v: number): string {
		return this.decimalPipe.transform(
			parseFloat(v.toString().replace(",", ".")),
			"1.2-2"
		);
	}

	public produtosEspecificosHandler() {
		if (this.formulario.get("tipoCobrar").value === "3") {
			this.formulario.get("tipoCobrar").markAsTouched();
			this.getProdutosEspecificos();
		}
	}

	private getProdutosEspecificos() {
		this.msPactoPayApiCobranca.getProdutosEspecificos().subscribe((data) => {
			const prodEspecif: PactoModalRef = this.modalService.open(
				"Escolha os produtos",
				ModalProdutosEspecificosComponent,
				PactoModalSize.LARGE
			);
			prodEspecif.componentInstance.dataConfig = new PactoDualMultiSelectConfig(
				{
					data,
					id: "codigo",
					value: "descricao",
				}
			);
			prodEspecif.result.then(
				(result) => (this.listaProdutosSelecionados = result)
			);
		});
	}

	public get formatoDoGrid() {
		switch (this.formulario.get("tipo").value) {
			case 1:
				if (this.tipoString && this.tipoString === "idVindi") {
					return "grid-id-vindi";
				}
				return "grid-credito";
			case 2:
				return "grid-debito";
			case 3:
				return "grid-boleto";
			case 5:
				return "grid-pix-automatico";
			default:
				return "grid-nenhum";
		}
	}

	public mascaraParaCartao() {
		let value = this.formulario.get("card").value;
		value = value.replace(/\s|[a-z]/g, ""); // remove todo espaço em branco
		value = value.slice(0, 16);
		if (value.length > 15) {
			value = value.replace(/(.{4})(.{4})(.{4})(\d{4})/, "$1 $2 $3 $4");
		} else if (value.length > 14) {
			value = value.replace(/(.{4})(.{6})(\d{5})/, "$1 $2 $3");
		} else {
			value = value.replace(/(.{4})(.{6})(\d{4})/, "$1 $2 $3");
		}
		this.formulario.get("card").setValue(value);
	}

	public mascaraParaNome() {
		const nomeCompleto = this.formulario
			.get("titular")
			.value.replace(/\d/g, "")
			.toLowerCase()
			.split(" ");
		const formatado = nomeCompleto
			.map((palavra) => palavra.charAt(0).toUpperCase() + palavra.substring(1))
			.join(" ");
		this.formulario.get("titular").setValue(formatado);
	}

	public mascaraParaDocumento() {
		let value = this.formulario.get("documento").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 14);
		if (value.length > 11) {
			value = value.replace(
				/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/g,
				"$1.$2.$3/$4-$5"
			);
		} else {
			value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/g, "$1.$2.$3-$4");
		}
		this.formulario.get("documento").setValue(value);
	}

	public mascaraParaVencimento() {
		let value = this.formulario.get("vencimento").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 6);
		value = value.replace(/(\d{2})(\d{2})?(\d{2})/, "$1/$3");
		this.formulario.get("vencimento").setValue(value);
	}

	public mascaraParaCVV() {
		let value = this.formulario.get("cvv").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 4);
		this.formulario.get("cvv").setValue(value);
	}

	public mascaraParaAgencia() {
		let value = this.formulario.get("agencia").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 6);
		value = value.replace(/(\d)(\d{2})$/g, "$1-$2");
		this.formulario.get("agencia").setValue(value);
	}

	public mascaraParaContaCorrente() {
		let value = this.formulario.get("contacorrente").value;
		value = value.replace(/\D/g, ""); // Remove tudo o que não é dígito
		value = value.slice(0, 16);
		value = value.replace(/(\d)(\d{2})$/g, "$1-$2");
		this.formulario.get("contacorrente").setValue(value);
	}

	private configurarValidacoes(tipo) {
		this.formulario.clearValidators();
		switch (tipo) {
			case 1: // cartão
				this.formulario.get("convenio").setValidators([Validators.required]);
				this.formulario.get("tipoCobrar").setValidators([Validators.required]);
				if (!this.tipoString) {
					this.formulario
						.get("titular")
						.setValidators([Validators.required, Validators.minLength(3)]);
					this.formulario
						.get("documento")
						.setValidators([Validators.minLength(11)]);
					this.formulario
						.get("card")
						.setValidators([Validators.required, Validators.minLength(16)]);
					this.formulario
						.get("cvv")
						.setValidators([Validators.minLength(3), Validators.maxLength(4)]);
					this.formulario
						.get("vencimento")
						.setValidators([Validators.required, Validators.minLength(4)]);
				}
				break;
			case 2: // débito
				this.formulario.get("convenio").setValidators([Validators.required]);
				this.formulario.get("tipoCobrar").setValidators([Validators.required]);
				this.formulario
					.get("titular")
					.setValidators([Validators.required, Validators.minLength(3)]);
				this.formulario
					.get("documento")
					.setValidators([Validators.minLength(11)]);
				this.formulario.get("agencia").setValidators([Validators.required]);
				this.formulario
					.get("contacorrente")
					.setValidators([Validators.required]);
				break;
			case 3: // boleto
				this.formulario.get("convenio").setValidators([Validators.required]);
				break;
		}

		// Atualiza a validação de todos os campos
		Object.keys(this.formulario.controls).forEach((key) => {
			this.formulario.get(key).updateValueAndValidity();
		});
	}

	private aplicarMascaraPeloTipo(tipo) {
		switch (tipo) {
			case 1: // cartão
				this.mascaraParaNome();
				this.mascaraParaDocumento();
				this.mascaraParaCartao();
				this.mascaraParaVencimento();
				break;
			case 2: // débito
				this.mascaraParaNome();
				this.mascaraParaDocumento();
				this.mascaraParaAgencia();
				this.mascaraParaContaCorrente();
				break;
		}
	}

	public validarCartao(evt) {
		this.zwPactoPayApiDash.validarCartao(evt.target.value).subscribe((res) => {
			if (res.valido) {
				this.formulario.get("bandeira").setValue(res.bandeira);
				if (res.bandeira) {
					this.bandeira = res.bandeira;
				}
				this.cd.detectChanges();
			} else {
				this.formulario.get("card").setValue("");
				this.notify.error(res.mensagem);
			}
		});
	}

	public get getPlaceholderCVV() {
		if (this.bandeira && this.bandeira === "AMEX") {
			return "****";
		}
		return "***";
	}

	private obterTiposDeAutorizacao() {
		this.msPactopayApiConvenioCobranca
			.obterTiposDeAutorizacao(this.empresa, Number(0), Number(1))
			.subscribe((res) => {
				this.tiposDeAutorizacao = res.content;
				this.cd.detectChanges();
			});
	}
}
