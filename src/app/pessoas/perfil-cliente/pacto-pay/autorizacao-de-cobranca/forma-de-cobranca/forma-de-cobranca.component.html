<div class="form">
	<div class="row">
		<div *ngIf="editar" class="col-lg-4 titulo">
			Editar autorização de cobrança
		</div>
	</div>

	<div *ngIf="!editar" class="row tabs-container">
		<span class="titulo-autorizacao">Tipo de autorização:</span>
		<ng-container
			*ngIf="
				tiposDeAutorizacao.hasOwnProperty('cartaoCredito') &&
				tiposDeAutorizacao['cartaoCredito']
			">
			<input
				(click)="escolherTipoDeAutorizacao(1)"
				class="tabs"
				id="tab_credito"
				name="tipo"
				type="radio" />
			<label for="tab_credito">
				<i class="pct pct-credit-card"></i>
				<span>Cartão de crédito</span>
			</label>
		</ng-container>
		<ng-container
			*ngIf="
				tiposDeAutorizacao.hasOwnProperty('debitoConta') &&
				tiposDeAutorizacao['debitoConta']
			">
			<input
				(click)="escolherTipoDeAutorizacao(2)"
				class="tabs"
				id="tab_debito-em-conta"
				name="tipo"
				type="radio" />
			<label for="tab_debito-em-conta">
				<i class="pct pct-dollar-sign"></i>
				<span>Débito em conta</span>
			</label>
		</ng-container>
		<ng-container
			*ngIf="
				tiposDeAutorizacao.hasOwnProperty('boleto') &&
				tiposDeAutorizacao['boleto']
			">
			<input
				(click)="escolherTipoDeAutorizacao(3)"
				class="tabs"
				id="tab_boleto-bancario"
				name="tipo"
				type="radio" />
			<label for="tab_boleto-bancario">
				<i class="pct pct-unpin"></i>
				<span>Boleto bancário</span>
			</label>
		</ng-container>
		<ng-container
			*ngIf="
				tiposDeAutorizacao.hasOwnProperty('pixAutomatico') &&
				tiposDeAutorizacao['pixAutomatico']
			">
			<input
				(click)="escolherTipoDeAutorizacao(5)"
				class="tabs"
				id="tab_pix-automatico"
				name="tipo"
				type="radio" />
			<label for="tab_pix-automatico">
				<i class="pct">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						viewBox="0 0 512 512"
						width="16"
						height="16"
						style="fill: currentColor">
						<path
							d="M242.4 292.5C247.8 287.1 257.1 287.1 262.5 292.5L339.5 369.5C353.7 383.7 372.6 391.5 392.6 391.5H407.7L310.6 488.6C280.3 518.1 231.1 518.1 200.8 488.6L103.3 391.2H112.6C132.6 391.2 151.5 383.4 165.7 369.2L242.4 292.5zM262.5 218.9C256.1 224.4 247.9 224.5 242.4 218.9L165.7 142.2C151.5 127.1 132.6 120.2 112.6 120.2H103.3L200.7 22.8C231.1-7.6 280.3-7.6 310.6 22.8L407.8 119.9H392.6C372.6 119.9 353.7 127.7 339.5 141.9L262.5 218.9zM112.6 142.7C126.4 142.7 139.1 148.3 149.7 158.1L226.4 234.8C233.6 241.1 243 245.6 252.5 245.6C261.9 245.6 271.3 241.1 278.5 234.8L355.5 157.8C365.3 148.1 378.8 142.5 392.6 142.5H430.3L488.6 200.8C518.9 231.1 518.9 280.3 488.6 310.6L430.3 368.9H392.6C378.8 368.9 365.3 363.3 355.5 353.5L278.5 276.5C264.6 262.6 240.3 262.6 226.4 276.6L149.7 353.2C139.1 363 126.4 368.6 112.6 368.6H80.8L22.8 310.6C-7.6 280.3-7.6 231.1 22.8 200.8L80.8 142.7H112.6z" />
					</svg>
				</i>
				<span>Pix Automático</span>
			</label>
		</ng-container>
		<ng-container
			*ngIf="
				possuiIdVindi &&
				tiposDeAutorizacao.hasOwnProperty('id') &&
				tiposDeAutorizacao['id']
			">
			<input
				(click)="escolherTipoDeAutorizacao(1, 'idVindi')"
				class="tabs"
				id="tab_id"
				name="tipo"
				type="radio" />
			<label for="tab_id">
				<i class="pct pct-key"></i>
				<span>Id</span>
			</label>
		</ng-container>
	</div>

	<!-- ------------------------------------------------------------------------------------------------- -->

	<div *ngIf="formatoDoGrid !== 'grid-nenhum'" class="row">
		<div [class.col-4]="tipoString === 'idVindi'" [class.col-6]="!tipoString">
			<pacto-cat-form-select
				(change)="produtosEspecificosHandler()"
				[disabled]="formulario.get('tipoCobrar').disabled"
				[control]="formulario.get('tipoCobrar')"
				[errorMsg]="'Informe o tipo de parcelas'"
				[id]="'parcelas-a-cobrar'"
				[items]="parcelas"
				[label]="'Parcelas a cobrar'"
				idKey="codigo"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div
			*ngIf="!pixAutomatico"
			[class.col-4]="tipoString === 'idVindi'"
			[class.col-6]="!tipoString">
			<pacto-cat-form-select
				[disabled]="formulario.get('convenio').disabled"
				[control]="formulario.get('convenio')"
				[errorMsg]="'Informe o convênio'"
				[id]="'convenio-da-cobranca'"
				[items]="convenios"
				[label]="'Convênio de Cobrança'"
				idKey="codigo"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div
			*ngIf="pixAutomatico"
			[class.col-4]="tipoString === 'idVindi'"
			[class.col-6]="!tipoString">
			<pacto-cat-form-select
				(change)="acaoSelecionouConvenioPixAutomatico()"
				[errorMsg]="'Informe o convênio'"
				[id]="'convenio-da-cobranca'"
				[items]="convenios"
				[label]="'Convênio de Cobrança'"
				[control]="formulario.get('convenio')"
				idKey="codigo"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
		<div
			*ngIf="pixAutomatico"
			[class.col-4]="tipoString === 'idVindi'"
			[class.col-6]="!tipoString">
			<pacto-cat-form-select-filter
				[errorMsg]="'Informe o banco que o aluno irá utilizar para recorrência'"
				[id]="'instituicao-da-cobranca'"
				[options]="instituicoes"
				[label]="'Instituição bancária do pagador'"
				[control]="formulario.get('instituicao')"
				[idKey]="'id'"
				[labelKey]="'display_name'"
				[imageKey]="'icon_logo'"
				[hasClearAction]="false"
				[placeholder]="
					'Selecione uma instituição'
				"></pacto-cat-form-select-filter>
		</div>
		<div class="col-4" *ngIf="tipoString === 'idVindi'">
			<pacto-cat-form-select
				[disabled]="formulario.get('tipoId').disabled"
				[control]="formulario.get('tipoId')"
				[errorMsg]="'Informe o tipo do id'"
				[id]="'tipo-id'"
				[items]="tiposId"
				[label]="'Tipo de Id'"
				idKey="codigo"
				labelKey="descricao"></pacto-cat-form-select>
		</div>
	</div>

	<div
		*ngIf="
			(formatoDoGrid === 'grid-credito' || formatoDoGrid === 'grid-debito') &&
			formulario.get('tipoCobrar').value === '3'
		"
		class="row produtos-especificados">
		<span class="titulo-produtos-especificados">Produtos especificados</span>
	</div>

	<hr />

	<div
		[class]="formatoDoGrid"
		[style.width]="editar && tipoString !== 'idVindi' ? '80%' : '100%'">
		<pacto-cat-form-input
			(focusout)="validarCartao($event)"
			(keyup)="mascaraParaCartao()"
			*ngIf="!tipoString && formatoDoGrid === 'grid-credito'"
			[control]="formulario.get('card')"
			[errorMsg]="'Informe o número do cartão'"
			[id]="'numero-do-cartao'"
			[label]="'Número do cartão'"
			[placeholder]="'Digite o número do cartão'"
			class="celula-card"></pacto-cat-form-input>
		<div
			*ngIf="!tipoString && formatoDoGrid === 'grid-credito'"
			class="card celula-cartao">
			<div class="dados-card">
				<div class="nome-titular">
					{{
						formulario.value.titular
							? formulario.value.titular
							: "Nome do titular"
					}}
				</div>
				<div class="info">
					<span>
						{{
							formulario.value.card
								? formulario.value.card
								: "0000 0000 0000 0000"
						}}
					</span>
					<span>
						{{
							formulario.value.vencimento
								? formulario.value.vencimento
								: "00/00"
						}}
					</span>
				</div>
				<div class="bandeira">
					<img
						[src]="
							formulario.value.bandeira
								? 'assets/images/bandeiras/' +
								  formulario.value.bandeira +
								  '.png'
								: ''
						" />
				</div>
			</div>
			<img [src]="'assets/images/cartao.png'" class="card-detail" />
		</div>
		<pacto-cat-form-input
			(focusout)="mascaraParaNome()"
			*ngIf="
				!tipoString &&
				(formatoDoGrid === 'grid-credito' || formatoDoGrid === 'grid-debito')
			"
			[control]="formulario.get('titular')"
			[errorMsg]="'Informe o nome assim como está impresso no cartão'"
			[id]="'nome-do-titular'"
			[label]="'Nome do titular do cartão'"
			[placeholder]="'Digite o nome do titular do cartão'"
			class="celula-titular"></pacto-cat-form-input>
		<pacto-cat-form-input
			(keyup)="mascaraParaVencimento()"
			*ngIf="!tipoString && formatoDoGrid === 'grid-credito'"
			[control]="formulario.get('vencimento')"
			[errorMsg]="'Informe a validade'"
			[id]="'validade'"
			[label]="'Validade'"
			[placeholder]="'MM/AA'"
			class="celula-vencimento"></pacto-cat-form-input>
		<pacto-cat-form-input
			(keyup)="mascaraParaCVV()"
			*ngIf="!tipoString && formatoDoGrid === 'grid-credito'"
			[control]="formulario.get('cvv')"
			[errorMsg]="'Informe o CVV'"
			[id]="'cvv'"
			[label]="'CVV'"
			[placeholder]="getPlaceholderCVV"
			[type]="'password'"
			class="celula-cvv"></pacto-cat-form-input>
		<pacto-cat-form-input
			(keyup)="mascaraParaDocumento()"
			*ngIf="
				!tipoString &&
				(formatoDoGrid === 'grid-credito' || formatoDoGrid === 'grid-debito')
			"
			[control]="formulario.get('documento')"
			[errorMsg]="'Informe o CPF ou CNPJ do titular'"
			[id]="'documento'"
			[label]="'CPF ou CNPJ do titular'"
			[placeholder]="'Forneça o CPF ou CNPJ do titular'"
			class="celula-documento"></pacto-cat-form-input>
		<pacto-cat-form-input
			(keyup)="mascaraParaAgencia()"
			*ngIf="!tipoString && formatoDoGrid === 'grid-debito'"
			[control]="formulario.get('agencia')"
			[errorMsg]="'Informe o N° da agência assim como está impresso no cartão'"
			[id]="'n-da-agencia'"
			[label]="'N° da agência'"
			[placeholder]="'0000-00'"
			class="celula-agencia"></pacto-cat-form-input>
		<pacto-cat-form-input
			(keyup)="mascaraParaContaCorrente()"
			*ngIf="!tipoString && formatoDoGrid === 'grid-debito'"
			[control]="formulario.get('contacorrente')"
			[errorMsg]="
				'Informe o N° da conta corrente assim como está impresso no cartão'
			"
			[id]="'n-da-conta-corrente'"
			[label]="'N° da conta corrente'"
			[placeholder]="'00.000.000-00'"
			class="celula-conta-corrente"></pacto-cat-form-input>
		<pacto-cat-form-input
			*ngIf="tipoString"
			[control]="formulario.get('idVindi')"
			[id]="'id-vindi'"
			label="IdVindi"></pacto-cat-form-input>
		<!-- <div *ngIf="formatoDoGrid === 'grid-credito' || formatoDoGrid === 'grid-debito'" class="clear celula-limpar"
            (click)="limparDados()">
            <i class="pct pct-trash-2"></i>
            <span class="text-limpar">Limpar dados</span>
        </div> -->
		<div class="form-group celula-flag">
			<div *ngIf="!tipoString && formatoDoGrid === 'grid-credito'">
				<pacto-cat-checkbox
					[control]="formulario.get('clienteTitular')"
					class="custom-chk"
					id="chk-titular"
					label="O cliente é o titular do cartão"></pacto-cat-checkbox>
			</div>

			<div *ngIf="!tipoString && formatoDoGrid === 'grid-debito'">
				<pacto-cat-checkbox
					[control]="formulario.get('autorizarclientedebito')"
					id="chk-autorizar-debito"
					label="Autorizar débito cliente no banco"></pacto-cat-checkbox>
			</div>
		</div>
	</div>
	<!-- ------------------------------------------------------------------------------------------------- -->
	<div class="row button-row">
		<div>
			<pacto-cat-button
				*ngIf="!pixAutomatico"
				[label]="'Salvar'"
				(click)="salvar()"
				[size]="'LARGE'"
				[type]="'PRIMARY_NO_TEXT_TRANSFORM'"
				id="btn-save"
				style="margin-right: 10px"></pacto-cat-button>
			<pacto-cat-button
				*ngIf="!cobranca?.utilizandoIdVindiPessoa"
				(click)="cancelar()"
				[label]="'Cancelar'"
				[size]="'LARGE'"
				[type]="'OUTLINE_ACTION'"
				id="btn-cancel"
				style="margin-right: 10px"></pacto-cat-button>
		</div>
		<div>
			<pacto-cat-button
				(click)="confirmarExclusao()"
				*ngIf="editar"
				[label]="'Excluir autorização'"
				[size]="'LARGE'"
				[type]="'ALERT'"
				id="btn-delete"></pacto-cat-button>
		</div>
	</div>
</div>
