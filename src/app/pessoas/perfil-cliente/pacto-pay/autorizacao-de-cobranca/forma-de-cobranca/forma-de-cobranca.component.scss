@import "~src/assets/scss/pacto/plataforma-import.scss";

.form {
	padding: 15px 15px 30px;
	position: relative;
	min-height: 55px;
}

// TAB
.tabs-container {
	display: flex;
	flex-wrap: wrap;
	padding-right: 15px;
	padding-left: 15px;
	grid-column-gap: 30px;

	.titulo-autorizacao {
		width: 100%;
		color: #a6aab1;
		line-height: 2em;
		font-size: 16px;
		font-weight: 400;
	}
}

input.tabs {
	display: none;
}

input.tabs + label {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 30%;
	padding: 0.6rem 1rem;
	background-color: #fafafa;
	border: 1px solid #b4b7bb;
	box-sizing: border-box;
	border-radius: 4px;
	font-weight: bold;
	text-align: center;
	cursor: pointer;
	transition: ease-in 0.2s;

	span {
		padding-left: 0.5rem;
	}
}

input.tabs + label:hover {
	box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.3);
}

input.tabs:checked + label {
	color: #026abc;
	border: 1px solid #026abc;
}

// FIM TAB

.produtos-especificados {
	padding-right: 15px;
	padding-left: 15px;

	.titulo-produtos-especificados {
		width: 100%;
		color: #a6aab1;
		line-height: 2em;
		font-size: 16px;
		font-weight: 400;
	}
}

// GRID
.grid-nenhum,
.grid-credito,
.grid-debito,
.grid-pix-automatico,
.grid-boleto {
	display: grid;
	grid-gap: 0px 1rem;
	align-items: center;
	justify-content: space-around;
	padding-top: 1rem;
}

.grid-nenhum {
	display: none;
}

.grid-credito {
	grid-template-columns: repeat(8, 1fr);
	grid-template-areas:
		"card       card       card       card       cartao     cartao     cartao     cartao"
		"titular    titular    titular    titular    cartao     cartao     cartao     cartao"
		"vencimento cvv        documento  documento  limpar     limpar     limpar     limpar"
		"flag       flag       flag       flag       flag       flag       flag       flag";
}

.grid-id-vindi {
	grid-template-columns: 1fr;
}

.grid-debito {
	grid-template-columns: repeat(2, 1fr);
	grid-template-areas:
		"titular        documento"
		"agencia        conta-corrente"
		"flag           limpar";
}

.grid-boleto {
	display: none;
}

.celula-card,
.celula-cartao,
.celula-titular,
.celula-vencimento,
.celula-cvv,
.celula-documento,
.celula-limpar,
.celula-flag,
.celula-agencia,
.celula-conta-corrente {
	margin: 0px;
}

.celula-card {
	grid-area: card;
}

.celula-cartao {
	grid-area: cartao;
}

.celula-titular {
	grid-area: titular;
}

.celula-vencimento {
	grid-area: vencimento;
}

.celula-cvv {
	grid-area: cvv;
}

.celula-documento {
	grid-area: documento;
}

.celula-limpar {
	grid-area: limpar;
}

.celula-flag {
	grid-area: flag;
}

.celula-agencia {
	grid-area: agencia;
}

.celula-conta-corrente {
	grid-area: conta-corrente;
}

// FIM DO GRID

.clear {
	width: 140px;
	display: flex;
	margin-left: 2.5rem;
	align-items: center;
	font-size: 17px;
	color: $preto02;
	cursor: pointer;

	.text-limpar {
		margin-left: 3px;
	}
}

.card {
	width: 320px;
	height: 200px;
	margin-left: 2.5rem;
	border-radius: 26px;
	background: -moz-linear-gradient(
		180deg,
		rgba(25, 123, 189, 1) 0%,
		rgba(51, 145, 208, 1) 100%
	);
	background: -webkit-linear-gradient(
		180deg,
		rgba(25, 123, 189, 1) 0%,
		rgba(51, 145, 208, 1) 100%
	);
	background: linear-gradient(
		180deg,
		rgba(25, 123, 189, 1) 0%,
		rgba(51, 145, 208, 1) 100%
	);
	box-shadow: 0px 5px 20px 0px rgba(0, 0, 0, 0.3);

	.card-detail {
		width: 69%;
		margin-top: 57px;
		margin-left: 100px;
	}
}

.dados-card {
	color: white;
	margin-top: 70px;
	margin-left: 20px;
	position: absolute;
	max-width: 300px;

	.nome-titular {
		font-weight: 600;
		font-size: 18px;
	}

	.info {
		display: flex;
		margin-top: 12px;
		width: 280px;
		justify-content: space-between;
	}

	.bandeira {
		display: flex;
		justify-content: flex-end;

		img {
			width: 80px;
		}
	}
}

.button-row {
	margin-left: 0px;
	margin-top: 15px;
	// width: 169%;
	justify-content: space-between;

	:last-child {
		margin-left: auto;
	}
}

.titulo {
	font-weight: bold;
	font-size: 17px;
	margin-bottom: 15px;
	margin-top: 10px;
}

hr {
	border: none;
	border-top: 1px dotted $cinzaPri;
	color: #fff;
	background-color: #fff;
	height: 1px;
	margin-top: 0px;
	margin-bottom: 0px;
}

:host ::ng-deep pacto-cat-button .pacto-button {
	text-transform: none;
}

::ng-deep .pacto-label > span,
::ng-deep .nome > span {
	font-family: Nunito Sans !important;
	font-size: 14px !important;
	font-weight: 400 !important;
	line-height: 20px !important;
	letter-spacing: 0px !important;
	text-align: left !important;
	color: #5f6369 !important;
}

:host ::ng-deep #chk-titular {
	.text {
		margin-top: 4px;
	}
}

::ng-deep pacto-forma-de-cobranca {
	.grid-credito {
		align-items: initial !important;
	}
}
