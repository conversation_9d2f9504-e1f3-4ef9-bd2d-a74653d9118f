@import "~src/assets/scss/pacto/plataforma-import.scss";

.cobranca-automatica {
	display: flex;
	flex-direction: column;
	align-items: center;

	a {
		font-size: 14px;
		color: $azulim07;
		cursor: pointer;
	}
}

.t-status {
	color: #ffffff;
	text-align: center;
	border-radius: 100px;
	font-weight: bold;
	font-size: 12px;
	line-height: 14px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100px;
	height: 24px;
	padding: 15px;
}

.t-status-paid {
	background: #28ab45;
}

.t-status-send {
	background: #026abc;
}

.t-status-error {
	background: #cd1f00;
}

.t-status-pac {
	background: #d6a10f;
}

.t-status-can {
	background: #ff5b92;
}

.t-status-est {
	background: #914391;
}

.t-status-in {
	background: #b4b7bb;
}

// Status customizados para PIX Automático
.status-pix-automatico {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 95%;
	min-height: 26px;
	border-radius: 100px;
	font-weight: bold;
	font-size: 12px;
	line-height: 14px;
	color: #fff;
	white-space: normal;
	word-wrap: break-word;
	text-align: center;
	padding: 4px 8px;
}

.status-nenhum {
	background-color: #6c757d; // Cinza
}

.status-aguardando {
	background-color: #0380e3; // Azul
}

.status-falha {
	background-color: #dc3545; // Vermelho
}

.status-autorizado {
	background-color: #2cca4e; // Verde
}

.status-autorizado-parcial {
	background-color: #0380e3; // azul
}

.status-expirada {
	background-color: #fd7e14; // Laranja
}

.status-negada {
	background-color: #dc3545; // Vermelho
}

// Tooltip com z-index alto para ficar por cima do menu lateral
:host ::ng-deep .tooltip-high-z-index {
	z-index: 9999 !important;
}

:host ::ng-deep .tooltip-high-z-index .tooltip-inner {
	white-space: nowrap !important;
	max-width: none !important;
	word-wrap: normal !important;
	word-break: normal !important;
}

// Garantir que todos os tooltips fiquem em uma linha
:host ::ng-deep .tooltip .tooltip-inner {
	white-space: nowrap !important;
	max-width: none !important;
	word-wrap: normal !important;
	word-break: normal !important;
}

// Força todos os tooltips do NgBootstrap a ficarem em uma linha
::ng-deep .tooltip .tooltip-inner {
	white-space: nowrap !important;
	max-width: none !important;
	word-wrap: normal !important;
	word-break: normal !important;
}

// Ajuste do ícone de exclusão PIX Automático
.pct-trash-2 {
	margin-top: -4px;
}

// Tooltip customizado para PIX Automático
::ng-deep .tooltip-pix-automatico {
	z-index: 9999 !important;

	.tooltip-inner {
		background-color: #333 !important;
		color: #fff !important;
		font-size: 12px !important;
		padding: 8px 12px !important;
		border-radius: 4px !important;
		max-width: 300px !important;
		word-break: break-all !important;
		white-space: normal !important;
	}

	&.bs-tooltip-right .arrow::before {
		border-right-color: #333 !important;
	}

	&.bs-tooltip-top .arrow::before {
		border-top-color: #333 !important;
	}

	&.bs-tooltip-bottom .arrow::before {
		border-bottom-color: #333 !important;
	}

	&.bs-tooltip-left .arrow::before {
		border-left-color: #333 !important;
	}
}

// Garantir que tooltip funcione em botões desabilitados
.btn:disabled {
	pointer-events: auto !important;
}

// Modal de notificação com largura reduzida
::ng-deep .modal-notificacao-pequeno .modal-dialog {
	max-width: 600px !important;
	width: 600px !important;
}
