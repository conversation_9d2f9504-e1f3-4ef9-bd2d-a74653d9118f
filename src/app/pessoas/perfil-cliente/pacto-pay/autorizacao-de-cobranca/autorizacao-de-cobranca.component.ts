import {
	AfterViewInit,
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
	TemplateRef,
	ViewChild,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { ModalService } from "@base-core/modal/modal.service";
import { RestService } from "@base-core/rest/rest.service";
import * as moment from "moment";
import {
	AlunoPactoPay,
	MsPactopayApiConvenioCobrancaService,
	MsPactoPayApiCobrancaService,
} from "ms-pactopay-api";
import {
	PactoDataGridConfig,
	PactoModalRef,
	PactoModalSize,
	BUTTON_SIZE,
	BUTTON_TYPE,
} from "ui-kit";
import { RelatorioCobrancaComponent } from "../../../../cobranca/components/relatorio-cobranca/relatorio-cobranca.component";
import { FormaDeCobrancaComponent } from "./forma-de-cobranca/forma-de-cobranca.component";
import { ModalCobrancaAutomaticaComponent } from "./modal-cobranca-automatica/modal-cobranca-automatica.component";
import { ClienteDadosPessoais } from "adm-core-api";
import { SessionService } from "@base-core/client/session.service";
import { PerfilRecursoPermissoTipo } from "../../../../../../projects/sdk/src/lib/services/models/perfil-acesso-recurso.model";
import { SnotifyService } from "ng-snotify";
import { ModalNotificacaoComponent } from "./modal-notificacao/modal-notificacao.component";
import { QrcodePixAutomaticoModalComponent } from "../../perfil-cliente-header-v2/modal-qrcode-pix-automatico/qrcode-pix-automatico-modal.component";
import { ZwPactoPayApiDashService } from "zw-pactopay-api";
import { ZWServletPixAutomaticoApiService } from "zw-servlet-api";

@Component({
	selector: "pacto-autorizacao-de-cobranca",
	templateUrl: "./autorizacao-de-cobranca.component.html",
	styleUrls: ["./autorizacao-de-cobranca.component.scss"],
})
export class AutorizacaoDeCobrancaComponent
	implements OnInit, AfterViewInit, OnDestroy
{
	@Input() aluno: AlunoPactoPay;
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Input() autorizada: boolean;
	@Output() hasDataCobranca = new EventEmitter();
	@ViewChild("relatorio", { static: true })
	relatorio: RelatorioCobrancaComponent;
	@ViewChild("relatorioPixAutomatico", { static: false })
	relatorioPixAutomatico: RelatorioCobrancaComponent;
	@ViewChild("tipoAutorizacao", { static: true })
	tipoAutorizacao: TemplateRef<any>;
	@ViewChild("tipoDeParcela", { static: true }) tipoDeParcela: TemplateRef<any>;
	@ViewChild("pixAutomatico", { static: true }) pixAutomatico: TemplateRef<any>;
	@ViewChild("dataCriacaoPixAutomatico", { static: true })
	dataCriacaoPixAutomatico: TemplateRef<any>;
	@ViewChild("dataInicioPixAutomatico", { static: true })
	dataInicioPixAutomatico: TemplateRef<any>;
	@ViewChild("dataFimPixAutomatico", { static: true })
	dataFimPixAutomatico: TemplateRef<any>;
	@ViewChild("statusPixAutomatico", { static: true })
	statusPixAutomatico: TemplateRef<any>;
	@ViewChild("bancoPixAutomatico", { static: true })
	bancoPixAutomatico: TemplateRef<any>;
	@ViewChild("tipoPixAutomatico", { static: true })
	tipoPixAutomatico: TemplateRef<any>;
	@ViewChild("acoesPixAutomatico", { static: true })
	acoesPixAutomatico: TemplateRef<any>;
	@ViewChild("idRecPixAutomatico", { static: true })
	idRecPixAutomatico: TemplateRef<any>;
	@ViewChild("codigoPixAutomatico", { static: true })
	codigoPixAutomatico: TemplateRef<any>;
	public relatorioGridConfig: PactoDataGridConfig;
	public relatorioPixAutomaticoGridConfig: PactoDataGridConfig;
	dataCobranca = false;
	hasPixAutomatico = false;
	hasOutrasAutorizacoes = false;
	permissaoCliente5_73: any;
	private pixAutomaticoListener: () => void;
	private autorizacaoCobrancaListener: () => void;

	constructor(
		private rest: RestService,
		private route: ActivatedRoute,
		private modalService: ModalService,
		private cd: ChangeDetectorRef,
		private readonly session: SessionService,
		private readonly notify: SnotifyService,
		private readonly msPactopayApiConvenioCobranca: MsPactopayApiConvenioCobrancaService,
		private readonly msPactoPayApiCobranca: MsPactoPayApiCobrancaService,
		private readonly zwServletPixAutomaticoApi: ZWServletPixAutomaticoApiService
	) {}

	/**
	 * Mascara o ID do contrato exibindo asteriscos do 11º ao 27º caractere
	 */
	public mascarIdContrato(idContrato: string): string {
		if (!idContrato) {
			return "N/A";
		}

		// Se o ID tem menos de 11 caracteres, exibe sem máscara
		if (idContrato.length <= 10) {
			return idContrato;
		}

		// Mascara do 11º caractere (índice 10) até o 27º caractere (índice 26)
		const inicio = idContrato.substring(0, 10); // Primeiros 10 caracteres
		const fim = idContrato.length > 27 ? idContrato.substring(27) : ""; // A partir do 28º caractere

		// Calcula quantos asteriscos colocar (máximo 17 caracteres mascarados)
		const caracteresParaMascarar = Math.min(idContrato.length - 10, 17);
		const asteriscos = "*".repeat(caracteresParaMascarar);

		return inicio + asteriscos + fim;
	}

	ngOnInit() {
		this.initGridConfigRelatorio();

		// Listener para atualizar listas quando uma autorização PIX é criada
		this.pixAutomaticoListener = () => {
			this.atualizarTodasAsListas();
		};
		window.addEventListener(
			"pixAutomaticoAutorizacaoCriada",
			this.pixAutomaticoListener
		);

		// Listener para atualizar listas quando qualquer autorização é criada
		this.autorizacaoCobrancaListener = () => {
			this.atualizarTodasAsListas();
		};
		window.addEventListener(
			"autorizacaoCobrancaCriada",
			this.autorizacaoCobrancaListener
		);
	}

	ngAfterViewInit(): void {
		this.permissaoCliente5_73 =
			this.session.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "5.73"
			);
	}

	private initGridConfigRelatorio() {
		this.relatorioGridConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPay(
				`aluno/autorizacoes?matricula=${this.route.snapshot.params["aluno-matricula"]}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: false,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				// Filtra apenas cartão, débito e boleto (tipos 1, 2, 3) - exclui PIX Automático (tipo 5)
				const dadosFiltrados = serveData.content
					? serveData.content.filter((item) => [1, 2, 3].includes(item.tipo))
					: [];

				// Verifica se há PIX Automático nos dados originais
				const pixAutomaticoData = serveData.content
					? serveData.content.filter((item) => item.tipo === 5)
					: [];

				const dadosProcessados = {
					...serveData,
					content: dadosFiltrados,
					totalElements: dadosFiltrados.length,
				};

				// Atualiza as propriedades de controle
				const totalGeral = dadosFiltrados.length + pixAutomaticoData.length;
				this.dataCobranca = totalGeral > 0;
				this.hasOutrasAutorizacoes = dadosFiltrados.length > 0;
				if (this.dataCobranca) {
					this.hasDataCobranca.emit(true);
				}

				this.cd.detectChanges();
				return dadosProcessados;
			},
			columns: [
				{
					nome: "tipo",
					titulo: "Forma de cobrança",
					celula: this.tipoAutorizacao,
					visible: true,
					ordenavel: false,
					width: "30%",
				},
				{
					nome: "tipoCobrar",
					titulo: "Tipo de parcela",
					celula: this.tipoDeParcela,
					visible: true,
					ordenavel: false,
					width: "25%",
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
					width: "25%",
				},
				{
					nome: "empresa",
					titulo: "Empresa",
					visible: true,
					ordenavel: false,
					width: "15%",
				},
			],
		});

		// Grid para PIX Automático (tipo 5)
		this.relatorioPixAutomaticoGridConfig = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlPactoPay(
				`aluno/autorizacoes?matricula=${this.route.snapshot.params["aluno-matricula"]}`
			),
			quickSearch: false,
			exportButton: false,
			pagination: false,
			showFilters: false,
			rowClick: false,
			totalRow: false,
			actions: [],
			dataAdapterFn: (serveData) => {
				// Filtra apenas PIX Automático (tipo 5)
				const dadosFiltrados = serveData.content
					? serveData.content.filter((item) => item.tipo === 5)
					: [];
				this.hasPixAutomatico = dadosFiltrados.length > 0;
				this.cd.detectChanges();
				return {
					...serveData,
					content: dadosFiltrados,
					totalElements: dadosFiltrados.length,
				};
			},
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					celula: this.codigoPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "5%",
				},
				{
					nome: "idRec",
					titulo: "idRec",
					celula: this.idRecPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "10%",
				},
				{
					nome: "dataCriacao",
					titulo: "Criação",
					celula: this.dataCriacaoPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "13%",
				},
				{
					nome: "dataInicio",
					titulo: "Início",
					celula: this.dataInicioPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "12%",
				},
				{
					nome: "dataFim",
					titulo: "Fim",
					celula: this.dataFimPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "12%",
				},
				{
					nome: "tipoCobrar",
					titulo: "Tipo de parcela",
					celula: this.tipoDeParcela,
					visible: true,
					ordenavel: false,
					width: "15%",
				},
				{
					nome: "convenio",
					titulo: "Convênio",
					visible: true,
					ordenavel: false,
					width: "12%",
				},
				{
					nome: "status",
					titulo: "Status",
					celula: this.statusPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "20%",
					classes: ["center"],
				},
				{
					nome: "banco",
					titulo: "Banco",
					celula: this.bancoPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "8%",
					classes: ["center"],
				},
				{
					nome: "tipo",
					titulo: "Tipo",
					celula: this.tipoPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "12%",
					classes: ["center"],
					styleClass: "center",
				},
				{
					nome: "acoes",
					titulo: "Ações",
					celula: this.acoesPixAutomatico,
					visible: true,
					ordenavel: false,
					width: "7%",
				},
			],
		});
	}

	public reiniciarRelatorio(res) {
		if (
			res.status === "Salvo com sucesso!" ||
			res.status === "Excluído com sucesso!"
		) {
			this.forcarAtualizacaoCompleta();
		}
	}

	public openModalNovaFormaDeCobranca() {
		if (!this.permiteExcluirAutorizacao()) {
			this.notify.error(
				'Você não possui a permissão "5.73 - Cadastro de autorização de cobrança cliente (Incluir)"',
				{
					timeout: undefined,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		const modal: PactoModalRef = this.modalService.open(
			"Nova autorização de cobrança",
			FormaDeCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.editar = false;
		modal.componentInstance.pessoa = this.aluno.pessoa;
		modal.componentInstance.empresa = this.dadosPessoais.empresa.codigo;
		modal.componentInstance.possuiIdVindi = this.dadosPessoais.possuiIdVindi;
		modal.componentInstance.idVindi = this.dadosPessoais.idVindi;
		modal.componentInstance.chave = this.session.chave;
		modal.componentInstance.dadosPessoais = this.dadosPessoais; // ← Adicionado
		modal.componentInstance.sendModificacao.subscribe((res) =>
			this.reiniciarRelatorio(res)
		);

		// Adiciona o listener para o evento abrirNovaFormaCobranca
		modal.componentInstance.abrirNovaFormaCobranca.subscribe(() => {
			this.openModalNovaFormaDeCobranca();
		});

		// Adiciona listener para quando o modal é fechado (para capturar PIX Automático)
		modal.result
			.then((result) => {
				if (result && result.abrindoParcelas) {
					// Modal foi fechado para abrir parcelas PIX Automático
					// Não fazemos nada aqui, pois o modal de parcelas vai emitir o resultado
				}
			})
			.catch(() => {
				// Modal foi cancelado
			});
	}

	permiteExcluirAutorizacao(): any {
		const permition = this.permissaoCliente5_73;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.INCLUIR_CONSULTAR
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	public openModalCobrancaAutomatica() {
		const modal: PactoModalRef = this.modalService.open(
			this.autorizada
				? "Bloquear cobrança automática"
				: "Autorizar cobrança automática",
			ModalCobrancaAutomaticaComponent,
			PactoModalSize.MEDIUM
		);
		modal.componentInstance.autorizada = this.autorizada;
		modal.componentInstance.pessoa = this.aluno.pessoa;
		modal.componentInstance.cobrancaAutomatica.subscribe((res) => {
			if (res.status === "bloqueada") {
				this.aluno.data_bloqueio_cobranca_automatica = res.data
					? moment(res.data).format("DD/MM/YYYY")
					: moment(new Date()).format("DD/MM/YYYY");
				this.autorizada = false;
			} else {
				this.autorizada = true;
			}
			this.cd.detectChanges();
		});
	}

	public openModalNovaFormaDeCobrancaPixAutomatico() {
		if (!this.permiteExcluirAutorizacao()) {
			this.notify.error(
				'Você não possui a permissão "5.73 - Cadastro de autorização de cobrança cliente (Incluir)"',
				{
					timeout: undefined,
					bodyMaxLength: 300,
				}
			);
			return;
		}
		const modal: PactoModalRef = this.modalService.open(
			"Nova autorização PIX Automático",
			FormaDeCobrancaComponent,
			PactoModalSize.LARGE
		);
		modal.componentInstance.editar = false;
		modal.componentInstance.pessoa = this.aluno.pessoa;
		modal.componentInstance.empresa =
			this.dadosPessoais && this.dadosPessoais.empresa
				? this.dadosPessoais.empresa.codigo
				: null;
		modal.componentInstance.pixAutomaticoOnly = true; // Flag para abrir direto no PIX Automático
		modal.componentInstance.dadosPessoais = this.dadosPessoais;
		modal.result.then((res) => {
			this.reiniciarRelatorioPixAutomatico(res);
		});
	}

	public reiniciarRelatorioPixAutomatico(res) {
		if (
			res.status === "Salvo com sucesso!" ||
			res.status === "Excluído com sucesso!"
		) {
			this.forcarAtualizacaoCompleta();
		}
	}

	public formatarDataCriacao(cobranca: any): string {
		if (
			!cobranca ||
			!cobranca.pixAutomatico ||
			!cobranca.pixAutomatico.dataRegistroApresentar
		) {
			return "-";
		}
		return cobranca.pixAutomatico.dataRegistroApresentar;
	}

	public formatarDataInicio(cobranca: any): string {
		if (
			!cobranca ||
			!cobranca.pixAutomatico ||
			!cobranca.pixAutomatico.dataInicioApresentar
		) {
			return "-";
		}
		return cobranca.pixAutomatico.dataInicioApresentar;
	}

	public formatarDataFim(cobranca: any): string {
		if (
			!cobranca ||
			!cobranca.pixAutomatico ||
			!cobranca.pixAutomatico.dataFimApresentar
		) {
			return "Indeterminado";
		}
		return cobranca.pixAutomatico.dataFimApresentar;
	}

	public getTooltipDataFim(cobranca: any): string {
		if (
			!cobranca ||
			!cobranca.pixAutomatico ||
			!cobranca.pixAutomatico.dataFimApresentar
		) {
			return "Indeterminado (até que realize o cancelamento)";
		}
		return "A Autorização para cobranças automáticas de Pix é válida até essa data";
	}

	public getStatusClass(status: any): string {
		if (status === null || status === undefined) {
			return "status-nenhum";
		} // Padrão

		// Converte para string para comparação
		const statusStr = String(status);

		// Mapeia os códigos do enum para classes CSS customizadas
		switch (statusStr) {
			case "0": // NENHUM
				return "status-nenhum";
			case "1": // AWAITING_AUTHORIZATION - Aguardando
				return "status-aguardando";
			case "2": // FAILED - Falha
				return "status-falha";
			case "3": // AUTHORIZED - Autorizado
				return "status-autorizado";
			case "4": // PARTIALLY_ACCEPTED - Autorizado parcial
				return "status-autorizado-parcial";
			case "5": // CONSUMED - Expirada
				return "status-expirada";
			case "6": // REVOKED - Negada
				return "status-negada";
			default:
				return "status-nenhum"; // Padrão
		}
	}

	public getStatusNome(status: any): string {
		if (status === null || status === undefined) {
			return "Pendente";
		}

		// Converte para string para comparação
		const statusStr = String(status);

		// Mapeia os códigos do enum para os nomes das situações
		switch (statusStr) {
			case "0": // NENHUM
				return "Nenhum";
			case "1": // AWAITING_AUTHORIZATION
				return "Aguardando autorização";
			case "2": // FAILED
				return "Falha";
			case "3": // AUTHORIZED
				return "Autorizado";
			case "4": // PARTIALLY_ACCEPTED
				return "Autorizado parcial";
			case "5": // CONSUMED
				return "Expirada";
			case "6": // REVOKED
				return "Cancelada";
			default:
				return String(status);
		}
	}

	public getStatusText(cobranca: any): string {
		// Usa o texto dinâmico do backend se disponível
		if (
			cobranca &&
			cobranca.pixAutomatico &&
			cobranca.pixAutomatico.status_Apresentar
		) {
			return cobranca.pixAutomatico.status_Apresentar;
		}
		return "Status não informado";
	}

	public abrirQRCodePixAutomatico(cobranca: any) {
		if (
			!cobranca ||
			!cobranca.pixAutomatico ||
			!cobranca.pixAutomatico.urlPixAutomaticoVendasOnline
		) {
			this.notify.error(
				"URL de redirecionamento não encontrada para esta autorização."
			);
			return;
		}

		const modalQrCode: PactoModalRef = this.modalService.open(
			"Autorização Pix Automático",
			QrcodePixAutomaticoModalComponent,
			PactoModalSize.LARGE
		);

		modalQrCode.componentInstance.url =
			cobranca.pixAutomatico.urlPixAutomaticoVendasOnline;
		modalQrCode.componentInstance.isNewAuthorization = false; // Não é uma nova autorização, apenas visualização
		modalQrCode.componentInstance.tipoRecorrencia =
			this.getTipoRecorrenciaFromBackend(cobranca);

		// Passa o código do PIX Automático para fazer polling do status
		if (cobranca.pixAutomatico && cobranca.pixAutomatico.codigo) {
			modalQrCode.componentInstance.codigoPixAutomatico =
				cobranca.pixAutomatico.codigo.toString();
		}

		// Passa o service para o modal do QRCode
		modalQrCode.componentInstance.zwServletPixAutomaticoApiService =
			this.zwServletPixAutomaticoApi;

		// Passa o código da pessoa
		if (this.aluno && this.aluno.pessoa) {
			modalQrCode.componentInstance.codPessoa = this.aluno.pessoa;
		}

		// Passa os dados pessoais para obter telefone
		if (this.dadosPessoais) {
			modalQrCode.componentInstance.dadosPessoais = this.dadosPessoais;
		}
	}

	public confirmarExclusaoPixAutomatico(autorizacao: any) {
		const modal: PactoModalRef = this.modalService.open(
			"Informativo",
			ModalNotificacaoComponent,
			PactoModalSize.MEDIUM,
			"modal-notificacao-pequeno"
		);

		modal.componentInstance.type = "pct-alert-triangle";
		modal.componentInstance.title = "Atenção!";
		modal.componentInstance.subtitle =
			"Tem certeza que deseja excluir essa autorização?";
		modal.componentInstance.actions = [
			{
				clickHandler: this.excluirAutorizacao(autorizacao, modal),
				type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
				size: BUTTON_SIZE.LARGE,
				label: "Excluir",
				full: true,
			},
			{
				clickHandler: () => modal.close(true),
				type: BUTTON_TYPE.OUTLINE_ACTION,
				size: BUTTON_SIZE.LARGE,
				label: "Cancelar",
				full: true,
			},
		];
	}

	private excluirAutorizacao(autorizacao: any, modalInstance: PactoModalRef) {
		return () => {
			// Verifica se deve cancelar na API externa primeiro
			if (
				autorizacao.pixAutomatico &&
				autorizacao.pixAutomatico.aptoParaCancelarNaAPI === true
			) {
				this.excluirAutorizacaoPixAutomaticoNaAPI(autorizacao, modalInstance);
			} else {
				this.excluirAutorizacaoPadrao(autorizacao, modalInstance);
			}
		};
	}

	private excluirAutorizacaoPixAutomaticoNaAPI(
		autorizacao: any,
		modalInstance: PactoModalRef
	) {
		const payload = {
			convenio: autorizacao.codConvenio,
			usuario: this.session.loggedUser.professorResponse.id,
			idRec: autorizacao.pixAutomatico.idRec,
		};

		this.zwServletPixAutomaticoApi
			.cancelarAutorizacaoPixAutomatico(payload)
			.subscribe(
				(response) => {
					if (response.error) {
						this.notify.error(
							response.message ||
								"Erro ao cancelar autorização recorrência Pix. Tente novamente."
						);
						return;
					}

					// Verifica se o cancelamento foi bem-sucedido
					let sucesso = false;
					try {
						// Se response é string, faz parse para objeto
						const responseObj =
							typeof response === "string" ? JSON.parse(response) : response;
						sucesso = responseObj.sucesso === true;
					} catch (e) {
						console.error("Erro ao parsear response:", e);
					}

					modalInstance.close(true);

					if (sucesso) {
						this.exibirModalSucesso({ content: "" }, "Excluído com sucesso!");
					} else {
						this.notify.error(
							"Falha ao cancelar autorização PIX na API externa."
						);
					}
				},
				(error) => {
					this.notify.error("Erro ao excluir autorização.");
				}
			);
	}

	private excluirAutorizacaoPadrao(
		cobranca: any,
		modalInstance: PactoModalRef
	) {
		// Fluxo padrão usando o serviço existente
		this.msPactoPayApiCobranca.excluirAutorizacao(cobranca.codigo).subscribe(
			(result) => {
				modalInstance.close(true);
				this.exibirModalSucesso(result, "Excluído com sucesso!");
			},
			(httpErrorResponse) => {
				this.tratarErroExclusao(httpErrorResponse);
			}
		);
	}

	private exibirModalSucesso(result: any, tituloSucesso: string) {
		const modal: PactoModalRef = this.modalService.open(
			"Informativo",
			ModalNotificacaoComponent,
			PactoModalSize.MEDIUM,
			"modal-notificacao-pequeno"
		);

		if (result.meta && result.meta.messageValue) {
			modal.componentInstance.type = "pct-alert-triangle";
			modal.componentInstance.title = "Atenção!";
			modal.componentInstance.subtitle = result.meta.messageValue;
			modal.componentInstance.actions = [
				{
					clickHandler: () => {
						modal.close(true);
					},
					type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
					size: BUTTON_SIZE.LARGE,
					label: "Ok",
					full: true,
				},
			];
		} else {
			modal.componentInstance.type = "pct-check";
			modal.componentInstance.title = tituloSucesso;
			modal.componentInstance.subtitle =
				result.content || "Operação realizada com sucesso.";
			modal.componentInstance.actions = [
				{
					clickHandler: () => {
						modal.close(true);
					},
					type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
					size: BUTTON_SIZE.LARGE,
					label: "Ok",
					full: true,
				},
			];
		}

		// Atualiza as listas imediatamente após exclusão bem-sucedida
		this.forcarAtualizacaoCompleta();
	}

	private tratarErroExclusao(httpErrorResponse: any) {
		const err = httpErrorResponse.error;
		if (err.meta && err.meta.messageValue) {
			this.notify.error(err.meta.messageValue);
		} else {
			this.notify.error("Erro ao excluir autorização. Tente novamente.");
		}
	}

	private getTipoRecorrenciaFromBackend(cobranca: any): string {
		if (
			cobranca &&
			cobranca.pixAutomatico &&
			cobranca.pixAutomatico.possuiCobrancaImediata === true
		) {
			return "Cobrança imediata + Autorização";
		} else {
			return "Somente Autorização";
		}
	}

	private atualizarTodasAsListas() {
		this.dataCobranca = true;

		// Força a atualização única
		if (this.relatorioPixAutomatico) {
			this.relatorioPixAutomatico.reloadData();
		}

		if (this.relatorio) {
			this.relatorio.reloadData();
		}

		// Força detecção de mudanças
		this.cd.detectChanges();
	}

	private forcarAtualizacaoCompleta() {
		// Reseta as propriedades de controle
		this.hasPixAutomatico = false;
		this.hasOutrasAutorizacoes = false;

		// Força reload das tabelas
		if (this.relatorio) {
			this.relatorio.reloadData();
		}
		if (this.relatorioPixAutomatico) {
			this.relatorioPixAutomatico.reloadData();
		}

		// Aguarda processamento e força detecção de mudanças múltiplas vezes
		setTimeout(() => {
			this.cd.detectChanges();
			setTimeout(() => {
				this.cd.detectChanges();
			}, 300);
		}, 200);
	}

	public onImageError(event: any): void {
		// Remove a imagem que falhou ao carregar e mostra texto alternativo
		event.target.style.display = "none";
		const parent = event.target.parentElement;
		if (parent) {
			const span = document.createElement("span");
			span.className = "text-muted";
			span.style.fontSize = "12px";
			span.textContent = "Logo indisponível";
			parent.appendChild(span);
		}
	}

	public getTipoAutorizacao(cobranca: any): string {
		console.log("=== DEBUG TIPO AUTORIZAÇÃO ===");
		console.log("Cobrança:", cobranca);
		console.log(
			"possuiCobrancaImediata:",
			cobranca.pixAutomatico && cobranca.pixAutomatico.possuiCobrancaImediata
		);

		// Verifica se possui cobrança imediata para determinar o tipo
		if (
			cobranca.pixAutomatico &&
			cobranca.pixAutomatico.possuiCobrancaImediata === true
		) {
			console.log("Retornando: Autorização + Pgto.");
			return "Autorização + Pgto.";
		} else {
			console.log("Retornando: Autorização");
			return "Autorização";
		}
	}

	public getTipoAutorizacaoTooltip(cobranca: any): string {
		// Retorna o tooltip baseado no tipo de autorização
		if (
			cobranca.pixAutomatico &&
			cobranca.pixAutomatico.possuiCobrancaImediata === true
		) {
			return "Essa autorização foi gerada selecionando parcela(s) para pagamento imediato";
		} else {
			return "Essa autorização não possui pagamento imediato no ato";
		}
	}

	ngOnDestroy() {
		// Remove os listeners quando o componente for destruído
		if (this.pixAutomaticoListener) {
			window.removeEventListener(
				"pixAutomaticoAutorizacaoCriada",
				this.pixAutomaticoListener
			);
		}
		if (this.autorizacaoCobrancaListener) {
			window.removeEventListener(
				"autorizacaoCobrancaCriada",
				this.autorizacaoCobrancaListener
			);
		}
	}

	public verificarConveniosAntesDeAbrir() {
		this.msPactopayApiConvenioCobranca
			.obterTiposDeAutorizacao(
				this.dadosPessoais.empresa.codigo,
				Number(0),
				Number(1)
			)
			.subscribe((res) => {
				const todosFalse = Object.entries(res.content)
					.filter(([key]) => key !== "pix")
					.every(([_, value]) => value === false);

				if (todosFalse) {
					this.abrirModalNenhumConvenio();
				} else {
					this.openModalNovaFormaDeCobranca();
				}
			});
	}

	private abrirModalNenhumConvenio() {
		const modal: PactoModalRef = this.modalService.open(
			"Autorização de cobrança",
			ModalNotificacaoComponent,
			PactoModalSize.LARGE
		);

		modal.componentInstance.imagemUrl = "pacto-ui/images/ds3-illustration.svg";
		modal.componentInstance.title = "Nenhum convênio de cobrança encontrado.";
		modal.componentInstance.subtitle =
			"Para adicionar uma autorização de cobrança, é necessário ter um convênio de cobrança cadastrado e ativo no sistema.";
		modal.componentInstance.body = `<div style="display: flex; align-items: center;"><i class="pct pct-info" style="margin-right: 10px; color: #0a64ff;"></i>
																		<span>Para mais informações ou ajuda, conte com o suporte da Pacto.</span></div>`;
		modal.componentInstance.actions = [
			{
				clickHandler: () => modal.close(true),
				type: BUTTON_TYPE.PRIMARY_NO_TEXT_TRANSFORM,
				size: BUTTON_SIZE.LARGE,
				label: "Entendi",
				full: true,
			},
		];
	}
}
