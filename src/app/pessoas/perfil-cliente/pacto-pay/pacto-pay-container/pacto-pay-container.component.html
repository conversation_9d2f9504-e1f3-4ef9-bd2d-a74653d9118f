<div>
	<pacto-cat-card-plain>
		<pacto-autorizacao-de-cobranca
			[aluno]="aluno"
			[dadosPessoais]="dadosPessoais"
			[autorizada]="aluno?.data_bloqueio_cobranca_automatica === ''"
			(hasDataCobranca)="
				setDataCobranca($event)
			"></pacto-autorizacao-de-cobranca>
	</pacto-cat-card-plain>
</div>

<div [hidden]="!hasGeneralEmpty">
	<pacto-cat-card-plain>
		<div class="d-flex flex-column align-items-center mb-3">
			<img class="icon-empty" src="assets/images/empty-state-nota-fiscal.svg" />
			<div class="text-empty mt-3 body-text-empty mb-3">
				O aluno ainda não possui nenhum registro financeiro no PactoPay!
			</div>
		</div>
	</pacto-cat-card-plain>
</div>

<div [hidden]="hasGeneralEmpty">
	<ng-template #celulaAcoesCobranca let-cobranca="item">
		<pacto-acoes-de-detalhamento
			style="display: block; width: 100px"
			[cobranca]="cobranca"
			[tipo]="'transacao'"
			(acoes)="acaoEvent($event)"></pacto-acoes-de-detalhamento>
	</ng-template>
	<ng-template #celulaAcoesCobrancaVerifica let-cobranca="item">
		<pacto-acoes-de-detalhamento
			style="display: block; width: 100px"
			[cobranca]="cobranca"
			[tipo]="'transacao'"
			(acoes)="acaoEvent($event)"></pacto-acoes-de-detalhamento>
	</ng-template>
	<ng-template #celulaCodigo let-cobranca="item">
		<div ngbTooltip="{{ obterTipoCobrancaHint(cobranca.tipo_cobranca) }}">
			<span>{{ cobranca.codigo }}</span>
		</div>
	</ng-template>

	<!-- Cobrança cartão de crédito -->
	<pacto-cat-card-plain>
		<div [hidden]="dataCobrancaCartao">
			<div class="title-text-empty">
				Histórico de cobrança Cartão de Crédito
			</div>
			<div class="d-flex flex-column align-items-center mb-3">
				<img class="icon-empty" src="assets/images/empty-state-cash-card.svg" />
				<div class="text-empty mt-3 body-text-empty">
					O aluno ainda não possui nenhum histórico em cobrança de cartão de
					crédito!
				</div>
			</div>
		</div>
		<div [hidden]="!dataCobrancaCartao">
			<pacto-relatorio
				#tableCartao
				idSuffix="pay-tbl-hist-cobranca-cartao-credito"
				[id]="'tabelaCartao'"
				[enableZebraStyle]="true"
				[showShare]="false"
				[emptyStateMessage]="'Nenhuma cobrança registrada para este aluno'"
				[table]="cobrancasCartaoRelatorioConfig"
				tableTitle="Histórico de cobrança em cartão de crédito"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>

	<!-- Verificação de cartão de crédito -->
	<pacto-cat-card-plain>
		<div [hidden]="dataCobrancaCartaoVerificacao">
			<div class="title-text-empty">
				Histórico de verificação de Cartão de Crédito
			</div>
			<div class="d-flex flex-column align-items-center mb-3">
				<img class="icon-empty" src="assets/images/empty-state-cash-card.svg" />
				<div class="text-empty mt-3 body-text-empty">
					O aluno ainda não possui nenhum histórico de verificação de cartão de
					crédito!
				</div>
			</div>
		</div>
		<div [hidden]="!dataCobrancaCartaoVerificacao">
			<pacto-relatorio
				#tableCartaoVerificacao
				idSuffix="pay-tbl-hist-cobranca-cartao-credito-verificacao"
				[id]="'tabelaCartaoVerificacao'"
				[enableZebraStyle]="true"
				[showShare]="false"
				[emptyStateMessage]="'Nenhuma cobrança registrada para este aluno'"
				[table]="cobrancasCartaoVerificacaoRelatorioConfig"
				tableTitle="Histórico de verificação de Cartão de Crédito"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>

	<!-- Cobrança PIX Automático -->
	<pacto-cat-card-plain>
		<div [hidden]="dataCobrancaPixAutomatico">
			<div class="title-text-empty">Histórico de cobrança PIX Automático</div>
			<div class="d-flex flex-column align-items-center mb-3">
				<img class="icon-empty" src="assets/images/empty-state-cash-pix.svg" />
				<div class="text-empty mt-3 body-text-empty">
					O aluno ainda não possui nenhum histórico de cobrança PIX Automático!
				</div>
			</div>
		</div>
		<div [hidden]="!dataCobrancaPixAutomatico">
			<pacto-relatorio
				#tabelaPixAutomatico
				idSuffix="pay-hist-cobranca-pix-automatico"
				[id]="'tabelaPixAutomatico'"
				[enableZebraStyle]="true"
				[showShare]="false"
				[emptyStateMessage]="
					'Nenhuma cobrança PIX Automático registrada para este aluno'
				"
				[table]="cobrancasPixAutomaticoRelatorioConfig"
				tableTitle="Histórico de cobrança PIX Automático"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>

	<!-- Cobrança boleto -->
	<pacto-cat-card-plain>
		<div [hidden]="dataCobrancaBoleto">
			<div class="title-text-empty">
				Histórico de cobrança em Boleto bancário
			</div>
			<div class="d-flex flex-column align-items-center mb-3">
				<img class="icon-empty" src="assets/images/empty-state-receipt.svg" />
				<div class="text-empty mt-3 body-text-empty">
					O aluno ainda não possui nenhum histórico de boleto bancário!
				</div>
			</div>
		</div>
		<div [hidden]="!dataCobrancaBoleto">
			<pacto-relatorio
				#tableBoleto
				idSuffix="pay-hist-cobrancao-boleto"
				class="tableBboletosPactopayContainer"
				[id]="'tabelaBoleto'"
				[enableZebraStyle]="true"
				[showShare]="false"
				(rowCheck)="rowCheckHandlerBoleto($event)"
				[allowsCheck]="allowsCheck"
				[customActions]="actionBoletos"
				actionTitulo="Ações"
				[emptyStateMessage]="'Nenhuma cobrança registrada para este aluno'"
				[table]="cobrancasBoletoRelatorioConfig"
				tableTitle="Histórico de cobrança em boleto bancário "></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>

	<!-- Cobrança pix -->
	<pacto-cat-card-plain>
		<div [hidden]="dataCobrancaPix">
			<div class="title-text-empty">Histórico de cobrança PIX</div>
			<div class="d-flex flex-column align-items-center mb-3">
				<img class="icon-empty" src="assets/images/empty-state-cash-pix.svg" />
				<div class="text-empty mt-3 body-text-empty">
					O aluno ainda não possui nenhum histórico de cobrança PIX!
				</div>
			</div>
		</div>
		<div [hidden]="!dataCobrancaPix">
			<pacto-relatorio
				idSuffix="pay-hist-cobranca-pix"
				[id]="'tabelaPix'"
				[enableZebraStyle]="true"
				[showShare]="false"
				[emptyStateMessage]="'Nenhuma cobrança registrada para este aluno'"
				[table]="cobrancasPixRelatorioConfig"
				tableTitle="Histórico de cobrança em PIX"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>

	<!-- Cancelamento Cobrança GetCard -->
	<pacto-cat-card-plain>
		<div
			id="divDataCancelamentoCobrancaGetcardVazio"
			[hidden]="dataCancelamentoCobrancaGetcard">
			<div class="title-text-empty">Cancelamento de cobranças GetCard</div>
			<div class="d-flex flex-column align-items-center mb-3">
				<img
					class="icon-empty"
					src="assets/images/empty-state-nota-fiscal.svg" />
				<div class="text-empty mt-3 body-text-empty">
					O aluno ainda não possui nenhum histórico de cancelamento de cobrança
					Getcard!
				</div>
			</div>
		</div>
		<div
			id="divDataCancelamentoCobrancaGetcardPreenchido"
			[hidden]="!dataCancelamentoCobrancaGetcard">
			<pacto-relatorio
				idSuffix="pay-hist-cancelamento-cobrancao-getcard"
				[id]="'tabelaCancCobrancaGetCard'"
				[enableZebraStyle]="true"
				[showShare]="false"
				[emptyStateMessage]="
					'Nenhum cancelamento de cobrança Getcard registrada para este aluno'
				"
				[table]="cancelamentoCobrancaGetcardRelatorioConfig"
				tableTitle="Cancelamento de cobranças GetCard"></pacto-relatorio>
		</div>
	</pacto-cat-card-plain>

	<!-- Parcela geradas -->
	<pacto-cat-card-plain>
		<div [hidden]="dataCobrancaParcelas">
			<div class="title-text-empty">Parcelas geradas</div>
			<div class="d-flex flex-column align-items-center mb-3">
				<img
					class="icon-empty"
					src="assets/images/empty-state-installments.svg" />
				<div class="text-empty mt-3 body-text-empty">
					O aluno ainda não possui nenhuma parcela gerada!
				</div>
			</div>
		</div>
		<div [hidden]="!dataCobrancaParcelas">
			<pacto-relatorio
				idSuffix="pay-table-parcela-gerada"
				[id]="'tabelaParcelasGeradas'"
				[enableZebraStyle]="true"
				[showShare]="false"
				[emptyStateMessage]="'Nenhuma parcela registrada para este aluno'"
				[table]="parcelasRelatorioConfig"
				[tableTitle]="'Parcelas geradas'"></pacto-relatorio>
			<ng-template #celulaDetalharParcela let-parcela="item">
				<pacto-acoes-de-detalhamento
					[parcela]="parcela"></pacto-acoes-de-detalhamento>
			</ng-template>
		</div>
	</pacto-cat-card-plain>
</div>

<ng-template #actionBoletos>
	<div class="divActionBoletos">
		<div
			[ngbTooltip]="shouldDisableButtons ? tooltipDisabledMessage : ''"
			placement="top"
			container="body"
			tooltipClass="button-tooltip-high-zindex">
			<pacto-cat-button
				(click)="imprimirBoletosSelecionados()"
				[disabled]="shouldDisableButtons"
				[full]="true"
				[icon]="'printer'"
				[label]="'Imprimir boletos'"
				[size]="'LARGE'"
				[type]="'ACTION'"></pacto-cat-button>
		</div>

		<div
			[ngbTooltip]="shouldDisableButtons ? tooltipDisabledMessage : ''"
			placement="top"
			container="body"
			tooltipClass="button-tooltip-high-zindex">
			<pacto-cat-button
				(click)="enviarEmailBoletosSelecionados()"
				[disabled]="shouldDisableButtons"
				[full]="true"
				[icon]="'send'"
				[label]="'Enviar boletos por e-mail'"
				[size]="'LARGE'"
				[type]="'ACTION'"></pacto-cat-button>
		</div>

		<div
			[ngbTooltip]="shouldDisableButtons ? tooltipDisabledMessage : ''"
			placement="top"
			container="body"
			tooltipClass="button-tooltip-high-zindex">
			<pacto-cat-button
				(click)="cancelarBoletosSelecionados()"
				[disabled]="shouldDisableButtons"
				[full]="true"
				[icon]="'x-circle'"
				[label]="'Cancelar boletos'"
				[size]="'LARGE'"
				[type]="'ALERT'"></pacto-cat-button>
		</div>

		<div
			[ngbTooltip]="shouldDisableButtons ? tooltipDisabledMessage : null"
			placement="top"
			container="body"
			tooltipClass="button-tooltip-high-zindex">
			<pacto-cat-button
				(click)="sincronizarBoletosSelecionados()"
				[disabled]="shouldDisableButtons"
				[full]="true"
				[icon]="'refresh-cw'"
				[label]="'Sincronizar boletos'"
				[size]="'LARGE'"
				[type]="'ACTION'"></pacto-cat-button>
		</div>
	</div>
</ng-template>

<ng-template #celulaStatusDaTransacao let-cobranca="item">
	<pacto-status-transacao
		class="ds-3"
		[status]="cobranca.situacao"
		[codigo]="getCobrancaCodigo(cobranca)"
		[tooltip]="getCobrancaTooltip(cobranca)"></pacto-status-transacao>
</ng-template>

<ng-template #celulaStatusDoBoleto let-cobranca="item">
	<pacto-status-transacao
		class="ds-3"
		[status]="cobranca.situacao"
		[tipoCobranca]="cobranca.tipo_cobranca"
		[codigo]="getCobrancaCodigo(cobranca)"
		[tooltip]="getCobrancaTooltip(cobranca)"></pacto-status-transacao>
</ng-template>

<ng-template #celulaStatusDoPix let-cobranca="item">
	<pacto-status-pix
		class="ds-3"
		[status]="cobranca.situacao"></pacto-status-pix>
</ng-template>

<ng-template #celulaStatusDoPixAutomatico let-cobranca="item">
	<pacto-status-pix
		class="ds-3"
		[status]="cobranca.situacao"></pacto-status-pix>
</ng-template>

<ng-template #celulaStatusCobrancaPixAutomatico let-cobranca="item">
	<div
		class="status-pix-automatico"
		[ngClass]="getStatusCobrancaPixAutomaticoClass(cobranca.situacao)"
		[ngbTooltip]="cobranca.situacao_hint || 'Status não disponível'"
		placement="top">
		{{ cobranca.statusApresentar || "Status não disponível" }}
	</div>
</ng-template>

<ng-template #celulaAcoesCobrancaPixAutomatico let-cobranca="item">
	<div class="d-flex justify-content-center">
		<i
			(click)="abrirDetalhesCobrancaPixAutomatico(cobranca)"
			class="pct pct-zoom-in cor-azulim05"
			style="font-size: 16px; cursor: pointer"
			ngbTooltip="Mais detalhes sobre esta cobrança"
			placement="top"></i>
	</div>
</ng-template>

<ng-template #celulaStatusDaParcela let-parcela="item">
	<pacto-status-parcela
		class="ds-3"
		[situacao]="parcela.situacao"
		[retorno]="parcela.codigoRetorno"></pacto-status-parcela>
</ng-template>

<ng-template #celulaConvenioCobranca let-convenio="item">
	<span>{{ convenio.descricao }}</span>
</ng-template>

<ng-template #customActionBoletoRef>
	<pacto-cat-button
		id="pay-btn-sinc-todo-boleto"
		size="LARGE"
		(click)="sincronizarTodosBoletos()"
		label="Sincronizar todos boletos"
		type="PRIMARY"></pacto-cat-button>
</ng-template>

<pacto-traducoes-xingling #traducoes>
	<span xingling="tipo_cobranca_0"></span>
	<span xingling="tipo_cobranca_1">BOLETO</span>
	<span xingling="tipo_cobranca_2">ONLINE</span>
	<span xingling="tipo_cobranca_3">EDI DCC</span>
	<span xingling="tipo_cobranca_4">EDI DCO</span>
	<span xingling="tipo_cobranca_5">PIX</span>
	<span xingling="tipo_cobranca_6">BOLETO ONLINE</span>

	<span xingling="tipo_cobranca_0_hint"></span>
	<span xingling="tipo_cobranca_1_hint">Boleto (Remessa)</span>
	<span xingling="tipo_cobranca_2_hint">Transação Online</span>
	<span xingling="tipo_cobranca_3_hint">Remessa DCC (Cartão de Crédito)</span>
	<span xingling="tipo_cobranca_4_hint">Débito em Conta Corrente</span>
	<span xingling="tipo_cobranca_5_hint">Pix</span>
	<span xingling="tipo_cobranca_6_hint">Boleto (Online)</span>

	<span xingling="tipo_autorizacao_0"></span>
	<span xingling="tipo_autorizacao_1">Cartão de Crédito</span>
	<span xingling="tipo_autorizacao_2">Débito em Conta Corrente</span>
	<span xingling="tipo_autorizacao_3">Boleto Bancário</span>
	<span xingling="tipo_autorizacao_4">Pix</span>

	<span xingling="tipo_parcela_0"></span>
	<span xingling="tipo_parcela_1">Em Aberto</span>
	<span xingling="tipo_parcela_2">Planos</span>
	<span xingling="tipo_parcela_3">Produtos Especificados</span>
	<span xingling="tipo_parcela_4">Contratos Autorrenováveis</span>
</pacto-traducoes-xingling>

<ng-template #celulaDescricao let-parcelas="item.movParcelas">
	<span *ngIf="parcelas && parcelas.length == 1 && parcelas[0].descricao">
		{{ parcelas[0].descricao | captalize }}
	</span>
	<span
		*ngIf="parcelas && parcelas.length > 1"
		[ds3Tooltip]="tooltipDescricoes">
		Múltiplas parcelas
	</span>

	<ng-template #tooltipDescricoes>
		<p *ngFor="let parcela of parcelas">{{ parcela.descricao | captalize }}</p>
	</ng-template>
</ng-template>

<ng-template #parcelasTemplate let-parcelas="item.movParcelas">
	<table
		class="mini-table"
		id="tableParcelasBoleto"
		[ngStyle]="{ display: 'ruby-text' }">
		<thead>
			<tr>
				<th>Parcela</th>
				<th>Descrição</th>
				<th>Valor</th>
			</tr>
		</thead>
		<tbody>
			<tr *ngFor="let parcela of parcelas">
				<td>{{ parcela.codigo }}</td>
				<td>{{ parcela.descricao }}</td>
				<td>{{ parcela.valorParcela | currency : "BRL" : "symbol" }}</td>
			</tr>
		</tbody>
	</table>
</ng-template>
