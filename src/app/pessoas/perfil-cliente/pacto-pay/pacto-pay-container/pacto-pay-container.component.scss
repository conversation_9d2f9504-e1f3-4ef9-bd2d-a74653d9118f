@import "dist/ui-kit/assets/import.scss";

::ng-deep .table-title {
	@extend .type-h6-bold;
	font-weight: 700;
}

pacto-cat-card-plain {
	padding: 16px;
	padding-bottom: 1px;
	margin-bottom: 20px;

	&:first-of-type {
		padding-bottom: 7px;
	}
}

:host {
	::ng-deep {
		pacto-relatorio-cobranca,
		pacto-relatorio {
			.table-title {
				padding: 0;
				padding-bottom: 1.5rem;
			}

			.pacto-table-title-block {
				padding: 0;

				.table-title {
					color: #51555a;
					font-size: 14px;
				}
			}

			.table-content {
				padding: 0;
			}

			.table-content table.table {
				th,
				.column-title {
					font-size: 14px;
					font-weight: 700;
				}

				td {
					font-size: 12px;

					i {
						font-size: 16px;
					}
				}

				.action-cell i {
					margin-right: 8px;
				}
			}

			.command-buttons {
				display: none;
			}
		}
	}
}

.divActionBoletos {
	display: flex;
	gap: 10px;
}

::ng-deep th.sortable span {
	display: flex;
	align-items: center;
	gap: 4px;
	white-space: nowrap;
}

:host ::ng-deep td.selectable input.form-check-input {
	margin-top: 2px;
}

#tableParcelasBoleto {
	table-layout: fixed;
	width: 100%;
}

#tableParcelasBoleto th:nth-child(1),
#tableParcelasBoleto td:nth-child(1) {
	width: 20%;
	padding: 4px;
}

#tableParcelasBoleto th:nth-child(2),
#tableParcelasBoleto td:nth-child(2) {
	width: 130px;
	padding: 4px;
}

#tableParcelasBoleto th:nth-child(3),
#tableParcelasBoleto td:nth-child(3) {
	width: 30%;
	padding: 4px;
}

//centraliza colunas específicas ao meio
:host ::ng-deep #column-title-sort-movParcelas,
:host ::ng-deep #column-title-sort-situacao_descricao,
:host ::ng-deep #column-title-sort-linhaDigitavel {
	display: flex;
	justify-content: center;
	align-items: center;
}

// Status customizados para PIX Automático - Histórico de Cobranças
.status-pix-automatico {
	display: flex !important;
	justify-content: center !important;
	align-items: center !important;
	width: 70% !important;
	height: 26px !important;
	border-radius: 100px !important;
	font-weight: bold !important;
	font-size: 12px !important;
	line-height: 14px !important;
	color: #fff !important;
	white-space: nowrap !important;
	padding: 0 2px !important;
	border: none !important;
}

.status-nenhum {
	background-color: #6c757d; // Cinza
}

.status-aguardando {
	background-color: #0380e3; // Azul
}

.status-falha {
	background-color: #dc3545; // Vermelho
}

.status-autorizado {
	background-color: #2cca4e; // Verde
}

.status-autorizado-parcial {
	background-color: #0380e3; // Azul
}

.status-expirada {
	background-color: #fd7e14; // Laranja
}

.status-negada {
	background-color: #dc3545; // Vermelho
}

// Status customizados para PIX Automático - Histórico de Cobranças
.status-pix-automatico {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 70%;
	height: 26px;
	border-radius: 4px;
	font-size: 10px;
	font-weight: 600;
	color: #fff;
	white-space: nowrap;
	padding: 0 2px;
}

.status-nenhum {
	background-color: #6c757d; // Cinza
}

.status-aguardando {
	background-color: #0380e3; // Azul
}

.status-falha {
	background-color: #dc3545; // Vermelho
}
