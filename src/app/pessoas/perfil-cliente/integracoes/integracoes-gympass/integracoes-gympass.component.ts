import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { SessionService } from "@base-core/client/session.service";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { AdmLegadoTelaClienteService } from "adm-legado-api";
import { SnotifyService } from "ng-snotify";
import { PactoDataGridConfig, RelatorioComponent } from "ui-kit";
import * as moment from "moment";
import { RestService } from "@base-core/rest/rest.service";
import {
	AdmCoreApiClienteService,
	ClienteDadosPessoais,
	AdmCoreApiIntegracoesService,
	ConfiguracaoIntegracaoGymPass,
	AdmCoreApiVendaAvulsaService,
	VendaAvulsa,
	ItemVendaAvulsa,
} from "adm-core-api";

@Component({
	selector: "pacto-integracoes-gympass",
	templateUrl: "./integracoes-gympass.component.html",
	styleUrls: ["./integracoes-gympass.component.scss"],
})
export class IntegracoesGympassComponent implements OnInit {
	@Input()
	dadosPessoais: ClienteDadosPessoais;

	tableData: PactoDataGridConfig;
	@ViewChild("tableDataRef", { static: false })
	tableDataRef: RelatorioComponent;
	@ViewChild("celulaLegenda", { static: true })
	public celulaLegenda;
	matricula;
	temToken = false;
	configuracao: ConfiguracaoIntegracaoGymPass;
	empresaTemConfiguracao: boolean = false;
	alunoTemDados: boolean = false;
	form = new FormGroup({
		tipo: new FormControl(1),
		token: new FormControl("", Validators.required),
		produto: new FormControl(null),
	});

	produtos = [];
	tiposTokenGympass = [
		{
			id: 1,
			label: "Tipo 1",
		},
		{
			id: 2,
			label: "Tipo 2",
		},
		{
			id: 3,
			label: "Tipo 3",
		},
		{
			id: 4,
			label: "Tipo 4",
		},
		{
			id: 5,
			label: "Tipo 5",
		},
	];

	constructor(
		private route: ActivatedRoute,
		private dialog: NgbActiveModal,
		private sessionService: SessionService,
		private msAdmCoreService: AdmCoreApiClienteService,
		private msIntegracoesService: AdmCoreApiIntegracoesService,
		private telaClienteService: AdmLegadoTelaClienteService,
		private snotifyService: SnotifyService,
		private cd: ChangeDetectorRef,
		private rest: RestService,
		private vendaAvulsaService: AdmCoreApiVendaAvulsaService
	) {}

	ngOnInit() {
		this.matricula = this.route.snapshot.params["aluno-matricula"];
		this.msAdmCoreService.dadosPessoais(this.matricula).subscribe(
			(dados) => {
				this.dadosPessoais = dados;
				this.consultarConfiguracao();
				this.initTable();
				this.initDadosGympassCliente();
				this.obterUniqueGymPass();
				this.cd.detectChanges();
			},
			(error) => {
				this.snotifyService.error(error.error.meta.message);
			}
		);
	}
	initDadosGympassCliente() {
		this.msAdmCoreService
			.historicoGympass(this.dadosPessoais.codigoPessoa)
			.subscribe((response) => {
				if (response.totalElements) {
					this.alunoTemDados = response.totalElements > 0;
					this.cd.detectChanges();
				}
			});
	}

	consultarConfiguracao() {
		this.msIntegracoesService
			.configuracaoIntegracaoGympass(this.sessionService.empresaId)
			.subscribe(
				(config) => {
					this.configuracao = config;
					this.empresaTemConfiguracao =
						this.configuracao.codigoGympass &&
						this.configuracao.codigoGympass.length > 0;
					this.cd.detectChanges();
				},
				(error) => {
					console.error(error);
					this.snotifyService.error(error.error.meta.message);
				}
			);
	}

	obterUniqueGymPass() {
		this.telaClienteService
			.obterUniqueGympass(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa
			)
			.subscribe(
				(response) => {
					this.temToken =
						response.content.token && response.content.token.length > 0;
					this.form.get("token").setValue(response.content.token);
					this.form.get("tipo").setValue(response.content.type);

					// Extrair produtos do response
					if (
						response.content.produtos &&
						response.content.produtos.length > 0
					) {
						// Adicionar opção vazia no início do array
						const emptyOption = {
							codigo: null,
							valor: 0,
							descricao: "-",
						};
						this.produtos = [emptyOption, ...response.content.produtos];
					}

					// Forçar detecção de mudanças para atualizar o select
					setTimeout(() => {
						this.cd.detectChanges();
					}, 100);

					this.cd.detectChanges();
				},
				(httpResponseError) => {
					console.log("Erro ao obter dados do Gympass:", httpResponseError);
				}
			);
	}

	salvarGympass() {
		if (!this.form.valid) {
			this.snotifyService.error("Informe o token do gympass!");
			return;
		}

		const produtoSelecionado = this.form.get("produto").value;

		const produtoEncontrado = produtoSelecionado
			? this.produtos.find(
					(p) => Number(p.codigo) === Number(produtoSelecionado)
			  )
			: null;

		// Primeiro salvar o token
		this.telaClienteService
			.cadastrarGympass(
				this.sessionService.chave,
				this.dadosPessoais.codigoPessoa,
				this.sessionService.empresaId,
				this.sessionService.loggedUser.usuarioZw,
				this.form.get("tipo").value,
				this.form.get("token").value
			)
			.subscribe(
				(response) => {
					this.snotifyService.success("Token salvo com sucesso!");

					// Se um produto foi selecionado, efetuar a venda avulsa
					if (produtoSelecionado) {
						if (produtoEncontrado) {
							this.efetuarVendaAvulsaGympass(produtoSelecionado);
						} else {
							this.snotifyService.error(
								"Produto selecionado não encontrado na lista!"
							);
						}
					}

					this.dialog.close();
				},
				(httpResponseError) => {
					this.snotifyService.error(httpResponseError.error.meta.message);
				}
			);
	}

	efetuarVendaAvulsaGympass(codigoProduto) {
		const produtoSelecionado = this.produtos.find(
			(p) => Number(p.codigo) === Number(codigoProduto)
		);

		if (!produtoSelecionado) {
			this.snotifyService.error("Produto não encontrado!");
			return;
		}

		// Criar o objeto ItemVendaAvulsa para o produto selecionado
		const item: ItemVendaAvulsa = {
			codigoProduto: produtoSelecionado.codigo,
			descricaoProduto: produtoSelecionado.descricao,
			precoProduto: produtoSelecionado.valor || 0,
			qtd: 1,
			valorParcial: produtoSelecionado.valor || 0,
			descontoManual: 0,
			pontos: 0,
			pacoteEscolhido: null,
		};

		// Criar o objeto VendaAvulsa
		const vendaAvulsa: VendaAvulsa = {
			pessoa: this.dadosPessoais.codigoPessoa,
			descontoGeral: 0,
			tipo: 0,
			lancamento: new Date().getTime(),
			parcelas: 1,
			primeiraParcela: new Date().getTime(),
			nomeComprador: this.dadosPessoais.nome,
			colaborador: false,
			itens: [item],
		};

		// Chamar o serviço para salvar a venda
		this.vendaAvulsaService.save(vendaAvulsa).subscribe(
			(response) => {
				if (response.error) {
					this.snotifyService.error(response.error);
					return;
				}
				this.snotifyService.success("Venda efetuada com sucesso!");
			},
			(error) => {
				this.snotifyService.error(
					error.error.meta.message || "Erro ao efetuar a venda!"
				);
			}
		);
	}

	excluirGympass() {
		if (this.form.get("token").value && this.form.get("token").value !== "") {
			this.telaClienteService
				.excluirGympass(
					this.sessionService.chave,
					this.dadosPessoais.codigoPessoa,
					this.sessionService.empresaId,
					this.sessionService.loggedUser.usuarioZw
				)
				.subscribe(
					(response) => {
						this.obterUniqueGymPass();
						this.snotifyService.success("Token excluído com suceso!");
						this.dialog.close();
						this.cd.detectChanges();
					},
					(httpResponseError) => {
						this.snotifyService.error(httpResponseError.error.meta.message);
					}
				);
		}
	}

	// autorizarGympass() {
	//     this.telaClienteService
	//         .autorizarGympass(
	//             this.sessionService.chave,
	//             this.codPessoa,
	//             this.sessionService.empresaId,
	//             this.sessionService.loggedUser.usuarioZw
	//         )
	//         .subscribe(
	//             response => {
	//                 this.notificationService.success(
	//                     'Token excluído com suceso!'
	//                 );
	//                 this.dialog.close();
	//             },
	//             httpResponseError => {
	//                 this.notificationService.error(
	//                     httpResponseError.error.meta.message
	//                 );
	//             }
	//         );
	// }

	private initTable() {
		this.tableData = new PactoDataGridConfig({
			endpointUrl: this.rest.buildFullUrlAdmCore(
				`clientes/${this.dadosPessoais.codigoPessoa}/historico-acessos-gympass`
			),
			quickSearch: false,
			ghostLoad: true,
			ghostAmount: 3,
			showFilters: false,
			columns: [
				{
					nome: "codigo",
					titulo: "Código",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "dataAcesso",
					titulo: "Data de acesso",
					visible: true,
					ordenavel: true,
					valueTransform(v) {
						return moment(v).format("DD/MM/YYYY");
					},
				},
				{
					nome: "token",
					titulo: "Token",
					visible: true,
					ordenavel: true,
				},
				{
					nome: "legenda",
					titulo: "Legenda",
					visible: true,
					ordenavel: true,
					celula: this.celulaLegenda,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: true,
					valueTransform(v = 0) {
						return v.toLocaleString("pt-BR", {
							style: "currency",
							currency: "BRL",
						});
					},
				},
			],
		});
	}
}
