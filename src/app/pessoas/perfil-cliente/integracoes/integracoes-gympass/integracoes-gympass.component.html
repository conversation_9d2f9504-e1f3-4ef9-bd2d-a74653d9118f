<div class="nav-aux">
	<a
		[routerLink]="['/pessoas', 'perfil-v2', matricula]"
		class="top-navigation"
		id="voltar-alunos">
		<i class="pct pct-arrow-left"></i>
		<span>WellHub</span>
	</a>
</div>

<pacto-cat-card-plain *ngIf="!empresaTemConfiguracao && !alunoTemDados">
	<img class="icone-historico" src="assets/images/historico_totalpass.svg" />
	<div class="text">A academia não possui integração com o WellHub</div>
</pacto-cat-card-plain>

<pacto-cat-card-plain *ngIf="empresaTemConfiguracao || alunoTemDados">
	<div *ngIf="empresaTemConfiguracao" class="row initialForm">
		<div class="col-12"><span class="table-title">WellHub</span></div>
		<div class="col-5">
			<pacto-cat-form-select
				[control]="form.get('tipo')"
				[items]="tiposTokenGympass"
				label="Tipo de acesso"></pacto-cat-form-select>
		</div>
		<div class="col-5">
			<pacto-cat-form-input
				[control]="form.get('token')"
				errorMsg="Informe o token do WellHub"
				label="Informe o token do WellHub*"></pacto-cat-form-input>
		</div>
		<div class="col-2 divBtnAcoes">
			<pacto-cat-button
				(click)="salvarGympass()"
				id="btn-gympass-salvar"
				label="Salvar"
				size="LARGE"
				type="PRIMARY"></pacto-cat-button>

			<pacto-cat-button
				(click)="excluirGympass()"
				*ngIf="temToken"
				id="btn-gympass-excluir"
				label="Excluir"
				size="LARGE"
				type="OUTLINE"></pacto-cat-button>
		</div>
		<div class="col-10">
			<pacto-cat-form-select
				[control]="form.get('produto')"
				[items]="produtos"
				idKey="codigo"
				labelKey="descricao"
				label="Produto Gympass"></pacto-cat-form-select>
		</div>
	</div>
	<hr *ngIf="empresaTemConfiguracao && alunoTemDados" class="divider" />
	<pacto-relatorio
		#tableDataRef
		*ngIf="alunoTemDados"
		[showShare]="false"
		[table]="tableData"
		tableTitle="Histórico de registro"></pacto-relatorio>
</pacto-cat-card-plain>

<ng-template #celulaLegenda let-gympass="item">
	<div *ngIf="!gympass.legenda" class="itemVermelho"></div>
	<div *ngIf="gympass.legenda" class="itemVerde"></div>
</ng-template>
