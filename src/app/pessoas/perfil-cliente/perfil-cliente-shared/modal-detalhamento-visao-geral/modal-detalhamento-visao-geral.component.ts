import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnInit,
	ViewEncapsulation,
} from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import {
	AdmCoreApiColaboradorService,
	ClienteDadosPessoais,
	OrigemSistema,
} from "adm-core-api";
import {
	AdmLegadoAutorizarAcessoService,
	AdmLegadoTelaClienteService,
	OpcoesContrato,
	TipoContrato,
} from "adm-legado-api";
import { SessionService } from "@base-core/client/session.service";
import { SnotifyService } from "ng-snotify";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";

@Component({
	selector: "pacto-modal-detalhamento-visao-geral",
	templateUrl: "./modal-detalhamento-visao-geral.component.html",
	styleUrls: [
		"./modal-detalhamento-visao-geral.component.scss",
		"../../contratos/contract-common.scss",
	],
})
export class ModalDetalhamentoVisaoGeralComponent implements OnInit {
	@Input() contract: any;
	@Input() dadosPessoais: ClienteDadosPessoais;
	@Input() nomenclaturaVendaCredito: string;
	@Input() opcoesContrato: OpcoesContrato;
	reloadContract: EventEmitter<boolean> = new EventEmitter<boolean>();
	textoOrigemContrato = "Zillion Web";
	editableTipoContrato = false;
	editableNomeConsultorReponsavel = false;
	editablePermiteRenovacaoAutomatica = false;
	apresentarRenovavelAutomaticamente = false;
	apresentarCampoAlterarConsultor: boolean;
	tipoContratos = new Array<{ id: number; label: string }>(
		{
			id: TipoContrato.AGENDADO,
			label: "Agendado",
		},
		{
			id: TipoContrato.ESPONTANEO,
			label: "Espontâneo",
		}
	);

	renovacaoAutomaticaOptions = new Array<any>(
		{
			value: true,
			label: "Sim",
		},
		{
			value: false,
			label: "Não",
		}
	);
	apresentarCampoRenovacaoAutomatica;
	consultores: Array<{ id: number; label: string }> = new Array<{
		id: number;
		label: string;
	}>();
	saldoCredito: number = 0;
	marcacoesFuturas: number = 0;
	formGroup = new FormGroup({
		tipoContrato: new FormControl(),
		nomeConsultorReponsavel: new FormControl(),
		permiteRenovacaoAutomatica: new FormControl(),
	});

	constructor(
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private sessionService: SessionService,
		private colaboradorService: AdmCoreApiColaboradorService,
		private cd: ChangeDetectorRef,
		private telaClienteService: AdmLegadoTelaClienteService,
		private notificationService: SnotifyService
	) {}

	ngOnInit() {
		this.populateForm();
		this.verificarPermissaoAlterarConsultor();
		this.verificarApresentarRenovavelAutomaticamente();
		this.obterConsultores();
		this.findOrigemSistema();
		if (this.contract.contratoRecorrencia) {
			this.apresentarCampoRenovacaoAutomatica =
				(this.contract.plano &&
					this.contract.plano.planoRecorrencia &&
					this.contract.plano.planoRecorrencia.renovavelAutomaticamente) ||
				(this.contract.contratoRecorrencia &&
					this.contract.contratoRecorrencia.renovavelAutomaticamente);
		} else {
			this.apresentarCampoRenovacaoAutomatica =
				this.contract.plano.renovavelAutomaticamente ||
				this.contract.renovavelAutomaticamente;
		}
		if (this.contract && this.contract.vendaCreditoTreino) {
			this.telaClienteService
				.consultarSaldoCredito(
					this.sessionService.chave,
					this.contract.codigo,
					+this.dadosPessoais.matricula
				)
				.subscribe((response) => {
					this.saldoCredito = response.content.saldoCreditoTreino;
					this.marcacoesFuturas = response.content.marcacoesFuturas;
					this.cd.detectChanges();
				});
		}
	}

	populateForm() {
		this.formGroup.get("tipoContrato").setValue(this.contract.origemContrato);
		this.formGroup
			.get("nomeConsultorReponsavel")
			.setValue(this.contract.consultorResponsavel.codigo);
		this.formGroup
			.get("permiteRenovacaoAutomatica")
			.setValue(this.contract.permiteRenovacaoAutomatica);
	}

	obterConsultores() {
		this.colaboradorService.findAllConsultoresAtivos().subscribe((response) => {
			if (response.content) {
				this.consultores = response.content.map((c) => {
					return {
						id: c.codigo,
						label: c.pessoa.nome,
					};
				});
				if (
					this.consultores.findIndex(
						(v) => v.id === this.contract.consultorResponsavel.codigo
					) === -1
				) {
					this.consultores.push({
						id: this.contract.consultorResponsavel.codigo,
						label: this.contract.consultorResponsavel.pessoa.nome,
					});
				}
				this.cd.detectChanges();
			}
		});
	}

	findOrigemSistema() {
		if (this.contract && this.contract.origemSistema) {
			switch (this.contract.origemSistema) {
				case OrigemSistema.ZW:
					this.textoOrigemContrato = "ZillyonWeb";
					break;
				case OrigemSistema.AULA_CHEIA:
					this.textoOrigemContrato = "Agenda Web";
					break;
				case OrigemSistema.TREINO:
					this.textoOrigemContrato = "Pacto Treino";
					break;
				case OrigemSistema.APP_TREINO:
					this.textoOrigemContrato = "App Treino";
					break;
				case OrigemSistema.APP_PROFESSOR:
					this.textoOrigemContrato = "App Professor";
					break;
				case OrigemSistema.AUTO_ATENDIMENTO:
					this.textoOrigemContrato = "Autoatendimento";
					break;
				case OrigemSistema.SITE:
					this.textoOrigemContrato = "Site vendas";
					break;
				case OrigemSistema.BUZZLEAD:
					this.textoOrigemContrato = "Buzz Lead";
					break;
				case OrigemSistema.VENDAS_ONLINE_2:
					this.textoOrigemContrato = "Vendas 2.0";
					break;
				case OrigemSistema.APP_CONSULTOR:
					this.textoOrigemContrato = "App do consultor";
					break;
				case OrigemSistema.BOOKING_GYMPASS:
					this.textoOrigemContrato = "Booking Gympass";
					break;
				case OrigemSistema.FILA_ESPERA:
					this.textoOrigemContrato = "Fila de espera";
					break;
				case OrigemSistema.IMPORTACAO_API:
					this.textoOrigemContrato = "Importação API";
					break;
				case OrigemSistema.HUBSPOT:
					this.textoOrigemContrato = "Hubspot Lead";
					break;
				case OrigemSistema.CRM_META_DIARIA:
					this.textoOrigemContrato = "CRM Meta Diária";
					break;
				case OrigemSistema.APP_FLOW:
					this.textoOrigemContrato = "App Flow";
					break;
				case OrigemSistema.NOVA_TELA_NEGOCIACAO:
					this.textoOrigemContrato = "Nova Tela de Negociação";
					break;
				case OrigemSistema.ZWBOOT:
					this.textoOrigemContrato = "Negociação ZWB";
					break;
				default:
					console.log(
						`Origem sistema ${this.contract.origemSistema} desconhecida`
					);
					console.log(
						`Origem sistema ${this.contract.origemSistema} desconhecida`
					);
					this.textoOrigemContrato = "";
					break;
			}
		}
	}

	editTipoContrato() {
		if (this.editableTipoContrato) {
			this.salvarMudanca();
		}
		this.editableTipoContrato = !this.editableTipoContrato;
		this.cd.detectChanges();
	}

	cancalarEdicaoTipoContrato() {
		this.editableTipoContrato = false;
	}

	editNomeConsultorReponsavel() {
		if (this.editableNomeConsultorReponsavel) {
			this.salvarMudanca();
		}
		this.editableNomeConsultorReponsavel =
			!this.editableNomeConsultorReponsavel;
		this.cd.detectChanges();
	}

	cancelarEdicaoConsultorResponsavel() {
		this.editableNomeConsultorReponsavel = false;
	}
	editPermiteRenovacaoAutomatica() {
		if (this.editablePermiteRenovacaoAutomatica) {
			this.salvarMudanca();
		}
		this.editablePermiteRenovacaoAutomatica =
			!this.editablePermiteRenovacaoAutomatica;
		this.cd.detectChanges();
	}
	cancelarEdicaoRenovacaoAutomatica() {
		this.editablePermiteRenovacaoAutomatica = false;
	}
	salvarMudanca() {
		this.telaClienteService
			.alterarDadosContrato(
				this.sessionService.chave,
				this.contract.codigo,
				this.sessionService.loggedUser.usuarioZw,
				+this.formGroup.get("nomeConsultorReponsavel").value,
				+this.formGroup.get("tipoContrato").value,
				this.formGroup.get("permiteRenovacaoAutomatica").value
			)
			.subscribe(
				(response) => {
					this.notificationService.success("Dados alterados com sucesso!");
					this.editableTipoContrato = false;
					this.editableNomeConsultorReponsavel = false;
					this.editablePermiteRenovacaoAutomatica = false;
					console.log(response.content.permiteRenovacaoAutomatica);
					if (
						response.content.consultorResponsavelDTO &&
						response.content.consultorResponsavelDTO.pessoa
					) {
						this.contract.nomeConsultorReponsavel =
							response.content.consultorResponsavelDTO.pessoa.nome;
					}
					if (response.content.permiteRenovacaoAutomatica !== undefined) {
						this.contract.permiteRenovacaoAutomatica =
							response.content.permiteRenovacaoAutomatica;
					}
					if (response.content.tipoContrato) {
						this.contract.tipoContrato = this.tipoContratos
							.find((v) => v.id === response.content.tipoContrato)
							.label.toUpperCase();
					}

					this.reloadContract.emit(true);
					this.cd.detectChanges();
				},
				(httpResponseError) => {
					const error = httpResponseError.error;
					if (error && error.meta && error.meta.message) {
						this.notificationService.error(error.meta.message);
					}
				}
			);
	}

	verificarPermissaoAlterarConsultor() {
		this.autorizarAcessoService
			.validarPermissaoUsuarioLogado(
				this.sessionService.chave,
				this.sessionService.loggedUser.usuarioZw,
				this.sessionService.empresaId,
				"AlterarConsultorContrato",
				"3.26 - Alterar Consultor do Contrato"
			)
			.subscribe(
				(response) => {
					this.apresentarCampoAlterarConsultor = true;
					this.cd.detectChanges();
				},
				(error) => {
					this.apresentarCampoAlterarConsultor = false;
					this.cd.detectChanges();
				}
			);
	}

	verificarApresentarRenovavelAutomaticamente() {
		if (this.contract.contratoRecorrencia) {
			if (
				this.contract.contratoRecorrencia.renovavelAutomaticamente ||
				(this.contract.plano.planoRecorrencia &&
					this.contract.plano.planoRecorrencia.renovavelAutomaticamente)
			) {
				this.apresentarRenovavelAutomaticamente = true;
			}
		} else {
			if (
				this.contract.renovavelAutomaticamente ||
				(this.contract.plano && this.contract.plano.renovavelAutomaticamente)
			) {
				this.apresentarRenovavelAutomaticamente = true;
			}
		}
	}
}
