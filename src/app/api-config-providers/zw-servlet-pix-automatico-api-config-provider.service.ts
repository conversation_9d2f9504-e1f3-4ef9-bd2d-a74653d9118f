import { Injectable } from "@angular/core";
import { Observable, of } from "rxjs";

import {
	ZwServletApiConfig,
	ZwServletApiConfigProviderBase,
} from "zw-servlet-api";
import { SessionService } from "@base-core/client/session.service";
import { ClientDiscoveryService } from "src/app/microservices/client-discovery/client-discovery.service";

@Injectable()
export class ZwServletPixAutomaticoApiConfigProvider extends ZwServletApiConfigProviderBase {
	constructor(
		private sessionService: SessionService,
		private discoveryService: ClientDiscoveryService
	) {
		super();
	}

	getApiConfig(): Observable<ZwServletApiConfig> {
		if (
			!this.discoveryService.getUrlMap() ||
			!this.discoveryService.getUrlMap().zwUrl
		) {
			throw new Error("URL de descoberta não disponível");
		}

		if (
			!this.sessionService.loggedUser ||
			!this.sessionService.loggedUser.username
		) {
			throw new Error("Usuário não autenticado");
		}

		const baseUrl = `${this.discoveryService.getUrlMap().zwUrl}/prest`;
		const key = this.sessionService.chave;
		const origin = window.location.host;
		const empresa = this.sessionService.empresaId;
		const username = this.sessionService.loggedUser.username;

		return of({
			baseUrl,
			key,
			origin,
			empresa,
			username,
		});
	}
}
