import {
	ChangeDetectorRef,
	Component,
	Input,
	OnInit,
	ViewChild,
} from "@angular/core";
import { Router } from "@angular/router";
import { RestService } from "@base-core/rest/rest.service";
import { transformMoney } from "@base-shared/utils/money.util";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { Cobranca } from "src/app/cobranca/models/cobranca.model";
import { PactoDataGridConfig } from "../../relatorio-cobranca/data-grid.model";
import { ZwPactoPayApiDashService } from "zw-pactopay-api";
import { SnotifyService } from "ng-snotify";

@Component({
	selector: "pacto-detalhamento-da-cobranca",
	templateUrl: "./detalhamento-da-cobranca.component.html",
	styleUrls: ["./detalhamento-da-cobranca.component.scss"],
})
export class DetalhamentoDaCobrancaComponent implements OnInit {
	public gridConfigParcelas: PactoDataGridConfig;

	@Input()
	public readonly tipo: string;
	@Input()
	public cobranca: Cobranca;
	@Input()
	public mostrarDetalhesCobranca: boolean = true;

	@ViewChild("celulaNomeDoAluno", { static: true })
	public readonly celulaNomeDoAluno;
	@ViewChild("celulaStatus", { static: true })
	public readonly celulaStatus;

	constructor(
		private restService: RestService,
		private router: Router,
		private cd: ChangeDetectorRef,
		private readonly modal: NgbModal,
		private readonly notificationService: SnotifyService,
		private readonly zwPactoPayApiDashService: ZwPactoPayApiDashService
	) {}

	ngOnInit() {
		let url: string;
		if (this.tipo === "transacao") {
			if (
				this.cobranca.tipo_cobranca &&
				this.cobranca.tipo_cobranca === 2 &&
				!this.cobranca.transacao &&
				this.cobranca.codigo
			) {
				this.zwPactoPayApiDashService
					.obterCobrancaDetalhe(Number(this.cobranca.codigo))
					.subscribe(
						(resp) => {
							this.cobranca = resp;
							this.cd.detectChanges();
						},
						(httpResponseError) => {
							this.notificationService.error(
								httpResponseError.error.meta.message
							);
						}
					);
			}

			if (this.cobranca.transacao) {
				url = this.restService.buildFullUrlPactoPay(
					`detalhe/transacao/${this.cobranca.transacao}/parcelas`
				);
			} else if (this.cobranca.codigo) {
				url = this.restService.buildFullUrlPactoPay(
					`detalhe/transacao/${this.cobranca.codigo}/parcelas`
				);
			} else if (this.cobranca.remessaItem) {
				url = this.restService.buildFullUrlPactoPay(
					`detalhe/remessaitem/${this.cobranca.remessaItem}/parcelas`
				);
			} else if (this.cobranca.remessa) {
				url = this.restService.buildFullUrlPactoPay(
					`detalhe/remessaitem/${this.cobranca.codigo}/parcelas`
				);
			}
		} else if (this.tipo === "pix") {
			url = this.restService.buildFullUrlPactoPay(
				`detalhe/pix/${
					this.cobranca.transacao || this.cobranca.codigo
				}/parcelas`
			);
		} else if (this.tipo === "pixautomatico") {
			url = this.restService.buildFullUrlPactoPay(
				`detalhe/cobrancapixautomatico/${this.cobranca.codigo}/parcelas`
			);
		}

		if (!url) {
			console.error("Tipo de cobrança não identificado", this.cobranca);
		}

		this.gridConfigParcelas = new PactoDataGridConfig({
			endpointUrl: url,
			quickSearch: false,
			exportButton: true,
			showFilters: false,
			rowClick: false,
			ghostLoad: true,
			ghostAmount: 10,
			pagination: false,
			columns: [
				{
					nome: "matricula",
					titulo: "Matrícula",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nome",
					titulo: "Nome do aluno",
					visible: true,
					ordenavel: false,
					celula: this.celulaNomeDoAluno,
				},
				{
					nome: "codigo",
					titulo: "Cód.: Parcela",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "descricao",
					titulo: "Descrição",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "nrTentativas",
					titulo: "Tentativa",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "situacao",
					titulo: "Status",
					visible: true,
					ordenavel: false,
					celula: this.celulaStatus,
				},
				{
					nome: "vencimento",
					titulo: "Vencimento",
					visible: true,
					ordenavel: false,
				},
				{
					nome: "valor",
					titulo: "Valor",
					visible: true,
					ordenavel: false,
					valueTransform: transformMoney,
				},
			],
		});
	}

	public verAluno(aluno) {
		this.router.navigate(["pessoas", "perfil-v2", aluno.matricula, "pactopay"]);
		this.modal.dismissAll();
	}
}
