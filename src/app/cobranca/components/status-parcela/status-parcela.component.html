<div
	[className]="'situacao-parcela situacao-parcela-' + situacao"
	ngbTooltip="{{ tooltip }}">
	<span *ngIf="customText">{{ customText }}</span>
	<span *ngIf="!customText && situacao === 'NAO_APROVADA'">{{ retorno }}</span>
	<span *ngIf="!customText && situacao === 'PG'">Pago</span>
	<span *ngIf="!customText && situacao === 'RG'">Renegociada</span>
	<span *ngIf="!customText && situacao === 'EA'">Em aberto</span>
	<span *ngIf="!customText && situacao === 'CA'">Cancelado</span>
</div>
