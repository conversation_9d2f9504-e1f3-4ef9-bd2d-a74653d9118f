/* You can add global styles to this file, and also import other style files */
@import "projects/ui/assets/import";

@import "~quill/dist/quill.core.css";
@import "~quill/dist/quill.bubble.css";
@import "~quill/dist/quill.snow.css";

html,
body {
	height: 100%;
	width: 100%;
}

.horario-modal .modal-dialog {
	max-width: 1288px;
}

.btn-row-adm {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	margin-bottom: 36px;

	pacto-cat-button {
		display: block;
		margin-right: 1rem;
	}
}

.action-container {
	border-top: 1px solid $cinza03;
}

.btn-row-adm {
	margin-bottom: unset;
	padding-bottom: 10px;
	padding-top: 10px;
}

.ql-snow {
	.ql-picker {
		&.ql-size {
			.ql-picker-label,
			.ql-picker-item {
				&::before {
					content: attr(data-value) !important;
				}
			}
		}
	}
}

.modal-xl .modal-dialog {
	max-width: 100% !important;
	height: 100% !important;
	margin: 0 !important;
}

.modal-mxl .modal-dialog {
	max-width: 70% !important;
	margin-left: 15% !important;
}

.modal-xmxl .modal-dialog {
	max-width: 88% !important;
	margin-left: 6% !important;
	margin-top: 2vh !important;
}

.modal-xl .modal-lg .modal-content {
	height: 100% !important;
	border: none;
	border-radius: 0 !important;
}

.custom-ngb-modal-window {
	z-index: 1100 !important;
}

.custom-ngb-modal-window .modal-dialog {
	max-width: 60% !important;
	margin-left: 22% !important;
	margin-top: 1vh !important;
}

.custom-ngb-modal-backdrop {
	z-index: 1100 !important;
}

/* 1. Garante que o overlay cubra a tela inteira */
.cdk-overlay-container {
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	width: 100% !important;
	height: 100% !important;
}

/* 2. Centraliza o painel de overlay */
.modal-centralizado {
	position: absolute !important;
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%, -50%) !important;
}
